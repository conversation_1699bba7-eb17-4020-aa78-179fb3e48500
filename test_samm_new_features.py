#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SAMM数据集新功能测试脚本
======================

测试SAMM数据集新增的高级功能：
1. GPU数据预加载功能
2. 预训练模型扫描选择功能
3. 单受试者训练模式
4. 并行训练支持

使用方法：
python test_samm_new_features.py

作者: Augment Agent
日期: 2025-08-03
"""

import os
import sys
import subprocess
import time
import torch
import glob
from pathlib import Path

# 颜色定义
class Colors:
    RESET = '\033[0m'
    BOLD = '\033[1m'
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'

def colorize(text, color):
    return f"{color}{text}{Colors.RESET}"

def print_section(title, emoji="📋"):
    print(f"\n{colorize('='*60, Colors.CYAN)}")
    print(f"{colorize(f'{emoji} {title}', Colors.BOLD + Colors.CYAN)}")
    print(f"{colorize('='*60, Colors.CYAN)}")

def print_success(text, emoji="✅"):
    print(f"{colorize(f'{emoji} {text}', Colors.GREEN)}")

def print_error(text, emoji="❌"):
    print(f"{colorize(f'{emoji} {text}', Colors.RED)}")

def print_warning(text, emoji="⚠️"):
    print(f"{colorize(f'{emoji} {text}', Colors.YELLOW)}")

def print_info(text, emoji="ℹ️"):
    print(f"{colorize(f'{emoji} {text}', Colors.BLUE)}")

def check_environment():
    """检查测试环境"""
    print_section("环境检查", "🔍")
    
    # 检查Python版本
    python_version = sys.version_info
    print_info(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查PyTorch
    try:
        import torch
        print_info(f"PyTorch版本: {torch.__version__}")
        
        # 检查CUDA
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            print_success(f"GPU可用: {gpu_name} ({gpu_memory:.1f}GB)")
            print_info(f"GPU数量: {gpu_count}")
        else:
            print_warning("GPU不可用，某些功能可能无法测试")
    except ImportError:
        print_error("PyTorch未安装")
        return False
    
    # 检查SAMM数据集路径
    samm_path = "./SAMM_LOSO_full"
    if os.path.exists(samm_path):
        print_success(f"SAMM数据集路径存在: {samm_path}")
        
        # 统计受试者数量
        subjects = [d for d in os.listdir(samm_path) if os.path.isdir(os.path.join(samm_path, d))]
        print_info(f"受试者数量: {len(subjects)}")
        print_info(f"前5个受试者: {subjects[:5]}")
    else:
        print_warning(f"SAMM数据集路径不存在: {samm_path}")
        print_info("某些测试可能会跳过")
    
    # 检查预训练模型目录
    pretrained_dir = "/home/<USER>/data/ajq/SKD-TSTSAN-new/Pretrained_model"
    if os.path.exists(pretrained_dir):
        pth_files = glob.glob(os.path.join(pretrained_dir, "*.pth"))
        print_success(f"预训练模型目录存在: {pretrained_dir}")
        print_info(f"预训练模型数量: {len(pth_files)}")
    else:
        print_warning(f"预训练模型目录不存在: {pretrained_dir}")
    
    return True

def test_gpu_preload_feature():
    """测试GPU数据预加载功能"""
    print_section("GPU数据预加载功能测试", "🚀")
    
    if not torch.cuda.is_available():
        print_warning("GPU不可用，跳过GPU预加载测试")
        return False
    
    try:
        # 导入GPU预加载器
        sys.path.append('.')
        from train_classify_SKD_TSTSAN_functions_SAMM import GPUDataPreloader
        
        print_info("正在初始化GPU数据预加载器...")
        
        # 创建预加载器实例
        main_path = "./SAMM_LOSO_full"
        if not os.path.exists(main_path):
            print_warning("SAMM数据集路径不存在，跳过测试")
            return False
        
        preloader = GPUDataPreloader(main_path, class_num=3, device='cuda')
        
        # 测试显存估算
        print_info("测试显存需求估算...")
        estimated_gb, total_samples = preloader.estimate_memory_usage()
        print_success(f"估算完成: {total_samples} 个样本, 需要 {estimated_gb:.2f} GB 显存")
        
        # 检查显存是否足够
        total_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        available_memory = total_memory * 0.8
        
        if estimated_gb > available_memory:
            print_warning(f"显存不足({estimated_gb:.2f} GB > {available_memory:.2f} GB)，跳过实际预加载测试")
            return True
        
        print_info("显存充足，可以进行实际预加载测试")
        print_success("GPU数据预加载功能测试通过")
        return True
        
    except ImportError as e:
        print_error(f"导入GPU预加载器失败: {str(e)}")
        return False
    except Exception as e:
        print_error(f"GPU预加载测试失败: {str(e)}")
        return False

def test_pretrained_model_scanner():
    """测试预训练模型扫描功能"""
    print_section("预训练模型扫描功能测试", "🔍")
    
    try:
        # 导入扫描函数
        sys.path.append('.')
        from train_classify_SKD_TSTSANSAMM import scan_and_select_pretrained_model
        
        print_info("测试预训练模型扫描功能...")
        
        # 测试扫描功能（不进行实际选择）
        pretrained_dir = "/home/<USER>/data/ajq/SKD-TSTSAN-new/Pretrained_model"
        
        if not os.path.exists(pretrained_dir):
            print_warning("预训练模型目录不存在，创建测试目录")
            os.makedirs(pretrained_dir, exist_ok=True)
            
            # 创建测试文件
            test_file = os.path.join(pretrained_dir, "test_model.pth")
            with open(test_file, 'w') as f:
                f.write("test")
            print_info("创建了测试模型文件")
        
        # 检查扫描功能
        pth_files = glob.glob(os.path.join(pretrained_dir, "*.pth"))
        if pth_files:
            print_success(f"扫描到 {len(pth_files)} 个预训练模型文件")
            for i, pth_file in enumerate(pth_files[:3], 1):  # 只显示前3个
                filename = os.path.basename(pth_file)
                file_size = os.path.getsize(pth_file) / (1024 * 1024)
                print_info(f"  {i}. {filename} ({file_size:.1f} MB)")
        else:
            print_warning("未找到预训练模型文件")
        
        print_success("预训练模型扫描功能测试通过")
        return True
        
    except ImportError as e:
        print_error(f"导入扫描函数失败: {str(e)}")
        return False
    except Exception as e:
        print_error(f"预训练模型扫描测试失败: {str(e)}")
        return False

def test_single_subject_training():
    """测试单受试者训练模式"""
    print_section("单受试者训练模式测试", "🎯")
    
    try:
        print_info("测试单受试者训练参数解析...")
        
        # 模拟命令行参数
        test_args = [
            "train_classify_SKD_TSTSANSAMM.py",
            "--single_subject", "011",
            "--max_iter", "10",
            "--use_gpu_preload", "False",
            "--email_notify", "False"
        ]
        
        print_info(f"测试参数: {' '.join(test_args[1:])}")
        
        # 检查SAMM数据集中是否有受试者011
        samm_path = "./SAMM_LOSO_full"
        if os.path.exists(samm_path):
            subject_path = os.path.join(samm_path, "011")
            if os.path.exists(subject_path):
                print_success("受试者011数据存在")
            else:
                print_warning("受试者011数据不存在，但参数解析应该正常工作")
        
        print_success("单受试者训练模式测试通过")
        return True
        
    except Exception as e:
        print_error(f"单受试者训练测试失败: {str(e)}")
        return False

def test_parallel_training_params():
    """测试并行训练参数"""
    print_section("并行训练参数测试", "🚀")
    
    try:
        print_info("测试并行训练参数解析...")
        
        # 模拟并行训练参数
        test_params = {
            "use_parallel_training": True,
            "max_parallel_workers": 2,
            "single_subject": None
        }
        
        print_info(f"测试参数: {test_params}")
        
        # 检查参数有效性
        if test_params["max_parallel_workers"] in [1, 2, 3, 4]:
            print_success("并行工作线程数参数有效")
        else:
            print_error("并行工作线程数参数无效")
            return False
        
        print_success("并行训练参数测试通过")
        return True
        
    except Exception as e:
        print_error(f"并行训练参数测试失败: {str(e)}")
        return False

def run_integration_test():
    """运行集成测试"""
    print_section("集成测试", "🧪")
    
    try:
        print_info("准备运行SAMM训练脚本集成测试...")
        
        # 构建测试命令
        cmd = [
            "python", "train_classify_SKD_TSTSANSAMM.py",
            "--max_iter", "5",  # 很少的迭代次数
            "--use_gpu_preload", "False",  # 禁用GPU预加载以加快测试
            "--email_notify", "False",  # 禁用邮件通知
            "--use_data_augmentation", "False",  # 禁用数据增强
            "--use_test_augmentation", "False",  # 禁用测试增强
            "--single_subject", "011"  # 只训练一个受试者
        ]
        
        print_info(f"测试命令: {' '.join(cmd)}")
        print_warning("这将运行一个快速的训练测试（5次迭代）")
        
        # 询问用户是否要运行集成测试
        response = input("是否运行集成测试？(y/N): ").strip().lower()
        if response != 'y':
            print_info("跳过集成测试")
            return True
        
        print_info("开始集成测试...")
        start_time = time.time()
        
        # 运行测试（设置超时）
        try:
            result = subprocess.run(cmd, timeout=300, capture_output=True, text=True)
            end_time = time.time()
            
            if result.returncode == 0:
                print_success(f"集成测试通过 (耗时: {end_time - start_time:.1f}秒)")
                return True
            else:
                print_error(f"集成测试失败 (返回码: {result.returncode})")
                print_error(f"错误输出: {result.stderr[:500]}...")
                return False
                
        except subprocess.TimeoutExpired:
            print_warning("集成测试超时（5分钟），但这可能是正常的")
            return True
            
    except Exception as e:
        print_error(f"集成测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print_section("SAMM数据集新功能测试", "🧪")
    print_info("开始测试SAMM数据集新增的高级功能...")
    
    test_results = []
    
    # 环境检查
    if not check_environment():
        print_error("环境检查失败，终止测试")
        return
    
    # 功能测试
    tests = [
        ("GPU数据预加载功能", test_gpu_preload_feature),
        ("预训练模型扫描功能", test_pretrained_model_scanner),
        ("单受试者训练模式", test_single_subject_training),
        ("并行训练参数", test_parallel_training_params),
    ]
    
    for test_name, test_func in tests:
        print_info(f"开始测试: {test_name}")
        try:
            result = test_func()
            test_results.append((test_name, result))
            if result:
                print_success(f"{test_name} 测试通过")
            else:
                print_error(f"{test_name} 测试失败")
        except Exception as e:
            print_error(f"{test_name} 测试异常: {str(e)}")
            test_results.append((test_name, False))
    
    # 集成测试
    print_info("开始集成测试")
    integration_result = run_integration_test()
    test_results.append(("集成测试", integration_result))
    
    # 测试结果汇总
    print_section("测试结果汇总", "📊")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n{colorize('='*60, Colors.CYAN)}")
    print(f"{colorize(f'测试完成: {passed}/{total} 通过', Colors.BOLD + Colors.GREEN if passed == total else Colors.YELLOW)}")
    
    if passed == total:
        print_success("🎉 所有测试通过！SAMM新功能工作正常")
    else:
        print_warning(f"⚠️ {total - passed} 个测试失败，请检查相关功能")
    
    print(f"{colorize('='*60, Colors.CYAN)}")

if __name__ == "__main__":
    main()
