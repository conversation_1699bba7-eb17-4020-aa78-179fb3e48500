#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU预加载修复测试脚本
==================

测试GPU预加载功能的数据格式处理修复

使用方法：
python test_gpu_preload_fix.py

作者: Augment Agent
日期: 2025-08-03
"""

import os
import sys
import torch
import numpy as np

def test_data_format_conversion():
    """测试数据格式转换逻辑"""
    print("🧪 测试数据格式转换逻辑...")
    
    # 模拟GPU预加载的数据格式
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 创建模拟的预加载数据 (NHWC格式，48x48x38)
    batch_size = 5
    height, width, channels = 48, 48, 38
    
    # 模拟GPU预加载数据：tensor列表
    X_train_gpu = []
    y_train_gpu = []
    
    for i in range(batch_size):
        # 创建随机数据 (HWC格式)
        sample = torch.randn(height, width, channels).to(device)
        label = torch.randint(0, 3, (1,)).item()
        
        X_train_gpu.append(sample)
        y_train_gpu.append(label)
    
    print(f"✅ 创建了 {len(X_train_gpu)} 个GPU预加载样本")
    print(f"   样本形状: {X_train_gpu[0].shape} (HWC格式)")
    print(f"   标签: {y_train_gpu}")
    
    # 测试数据转换逻辑
    try:
        # 模拟修复后的转换逻辑
        use_gpu_preload = True
        skip_traditional_loading = True
        
        if use_gpu_preload and skip_traditional_loading:
            # GPU预加载模式：X_train已经是tensor列表，需要stack并调整维度
            if len(X_train_gpu) > 0 and isinstance(X_train_gpu[0], torch.Tensor):
                # 检查tensor是否已经在GPU上
                if X_train_gpu[0].device.type == 'cuda':
                    # 数据已在GPU上，直接stack
                    X_train = torch.stack(X_train_gpu)
                else:
                    # 数据在CPU上，stack后移到GPU
                    X_train = torch.stack(X_train_gpu).to(device)
                
                # 检查维度是否需要调整 (从 NHWC 到 NCHW)
                if X_train.dim() == 4 and X_train.shape[-1] == 38:  # NHWC格式
                    X_train = X_train.permute(0, 3, 1, 2)  # 转换为NCHW
            else:
                # 空数据处理
                X_train = torch.empty(0, 38, 48, 48).to(device)
            
            # 处理y_train
            if isinstance(y_train_gpu, list):
                y_train = torch.tensor(y_train_gpu, dtype=torch.long).to(device)
            elif isinstance(y_train_gpu, torch.Tensor):
                y_train = y_train_gpu.to(dtype=torch.long).to(device)
        
        print(f"✅ 数据转换成功")
        print(f"   转换后X_train形状: {X_train.shape} (NCHW格式)")
        print(f"   转换后y_train形状: {y_train.shape}")
        print(f"   X_train设备: {X_train.device}")
        print(f"   y_train设备: {y_train.device}")
        
        # 验证维度正确性
        expected_shape = (batch_size, channels, height, width)
        if X_train.shape == expected_shape:
            print(f"✅ 维度转换正确: {X_train.shape}")
        else:
            print(f"❌ 维度转换错误: 期望 {expected_shape}, 实际 {X_train.shape}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据转换失败: {str(e)}")
        return False

def test_traditional_mode():
    """测试传统模式的数据处理"""
    print("\n🧪 测试传统模式数据处理...")
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 模拟传统模式的numpy数组数据
    batch_size = 3
    height, width, channels = 48, 48, 38
    
    # 创建numpy数组列表
    X_train_numpy = []
    y_train_numpy = []
    
    for i in range(batch_size):
        sample = np.random.randn(height, width, channels).astype(np.float32)
        label = np.random.randint(0, 3)
        
        X_train_numpy.append(sample)
        y_train_numpy.append(label)
    
    print(f"✅ 创建了 {len(X_train_numpy)} 个传统模式样本")
    print(f"   样本形状: {X_train_numpy[0].shape} (HWC格式)")
    print(f"   数据类型: {type(X_train_numpy[0])}")
    
    try:
        # 模拟传统模式的转换逻辑
        use_gpu_preload = False
        skip_traditional_loading = False
        
        if not (use_gpu_preload and skip_traditional_loading):
            # 传统模式：X_train是numpy数组列表
            X_train = torch.Tensor(X_train_numpy).permute(0, 3, 1, 2).to(device)
            y_train = torch.Tensor(y_train_numpy).to(dtype=torch.long).to(device)
        
        print(f"✅ 传统模式转换成功")
        print(f"   转换后X_train形状: {X_train.shape} (NCHW格式)")
        print(f"   转换后y_train形状: {y_train.shape}")
        print(f"   X_train设备: {X_train.device}")
        print(f"   y_train设备: {y_train.device}")
        
        return True
        
    except Exception as e:
        print(f"❌ 传统模式转换失败: {str(e)}")
        return False

def test_empty_data_handling():
    """测试空数据处理"""
    print("\n🧪 测试空数据处理...")
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 模拟空数据
    X_train_empty = []
    y_train_empty = []
    
    try:
        use_gpu_preload = True
        skip_traditional_loading = True
        
        if use_gpu_preload and skip_traditional_loading:
            if len(X_train_empty) > 0 and isinstance(X_train_empty[0], torch.Tensor):
                X_train = torch.stack(X_train_empty)
            else:
                # 空数据处理
                X_train = torch.empty(0, 38, 48, 48).to(device)
            
            if isinstance(y_train_empty, list):
                y_train = torch.tensor(y_train_empty, dtype=torch.long).to(device)
        
        print(f"✅ 空数据处理成功")
        print(f"   X_train形状: {X_train.shape}")
        print(f"   y_train形状: {y_train.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 空数据处理失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 GPU预加载数据格式修复测试")
    print("=" * 50)
    
    tests = [
        ("GPU预加载数据格式转换", test_data_format_conversion),
        ("传统模式数据处理", test_traditional_mode),
        ("空数据处理", test_empty_data_handling),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 结果汇总
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总结: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！GPU预加载数据格式修复成功")
        print("\n📋 修复内容:")
        print("  1. ✅ GPU预加载tensor列表正确转换为batch tensor")
        print("  2. ✅ 维度从NHWC正确转换为NCHW")
        print("  3. ✅ 设备位置正确处理")
        print("  4. ✅ 空数据情况正确处理")
        print("  5. ✅ 传统模式兼容性保持")
    else:
        print(f"⚠️ {total - passed} 个测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
