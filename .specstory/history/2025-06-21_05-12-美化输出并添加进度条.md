<!-- Generated by SpecStory -->

# 美化输出并添加进度条 (2025-06-21 13:12:07)

_**User**_

@train_classify_SKD_TSTSAN.py 输出的提示美观化 高级化 彩色化
加上进度条

---

_**Assistant**_

Read file: train_classify_SKD_TSTSAN.py

---

<details>
            <summary>Searched codebase "train_classify_SKD_TSTSAN_functions.py main_SKD_TSTSAN_with_Aug_with_SKD" • **6** results</summary>
        
| File | Lines |
|------|-------|
| `train_classify_SKD_TSTSAN_functions.py` | L727-747 |
| `train_classify_SKD_TSTSAN_functions.py` | L803-824 |
| `casme2训练/train_classify_SKD_TSTSAN_functions copy.py` | L280-329 |
| `casme2训练/train_classify_SKD_TSTSAN_functions.py` | L249-298 |
| `casme2训练/train_classify_SKD_TSTSAN_functions copy.py` | L359-401 |
| `casme2训练/train_classify_SKD_TSTSAN_functions.py` | L328-370 |

</details>

---

```diff
  from train_classify_SKD_TSTSAN_functions import main_SKD_TSTSAN_with_Aug_with_SKD, Logger  # 导入主训练函数和日志类
  import sys
+ import time
+ from rich.console import Console
+ from rich.panel import Panel
+ from rich.table import Table
+ from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
+ from rich.live import Live
+ from rich import box
+ from rich.text import Text
  
  
  if __name__ == '__main__':
+     # 创建Rich控制台
+     console = Console()
+     
      # 创建参数解析器
      parser = argparse.ArgumentParser(description='微表情识别模型训练参数配置')
```

```diff
      config = parser.parse_args()
      
-     # 打印配置信息
-     print("\n========== 训练配置信息 ==========")
-     print(f"模型名称: {config.model}")
-     print(f"损失函数: {config.loss_function}")
-     print(f"学习率: {config.learning_rate}")
-     print(f"批次大小: {config.batch_size}")
-     print(f"最大迭代次数: {config.max_iter}")
-     print(f"分类类别数: {config.class_num}")
+     # 显示启动动画
+     with console.status("[bold green]初始化微表情识别系统...", spinner="dots"):
+         time.sleep(1.5)  # 给用户一个视觉上的初始化过程
+     
+     # 创建配置信息表格
+     console.print()
+     table = Table(title="[bold cyan]微表情识别模型训练配置", box=box.ROUNDED, border_style="cyan", title_style="bold cyan")
+     
+     # 添加基本配置列
+     table.add_column("参数类别", style="dim", width=20)
+     table.add_column("配置项", style="cyan")
+     table.add_column("值", style="green")
+     
+     # 添加基本训练配置
+     table.add_row("模型配置", "模型名称", f"[bold]{config.model}[/bold]")
+     table.add_row("", "损失函数", config.loss_function)
+     table.add_row("", "分类类别数", str(config.class_num))
+     
+     # 添加训练参数
+     table.add_row("训练参数", "学习率", f"[yellow]{config.learning_rate}[/yellow]")
+     table.add_row("", "批次大小", str(config.batch_size))
+     table.add_row("", "最大迭代次数", str(config.max_iter))
+     table.add_row("", "随机种子", str(config.seed))
+     
+     # 添加知识蒸馏参数
+     table.add_row("知识蒸馏参数", "温度参数", str(config.temperature))
+     table.add_row("", "蒸馏损失权重", str(config.alpha))
+     table.add_row("", "特征损失权重", str(config.beta))
+     
+     # 添加数据扩充配置
      if config.use_data_augmentation:
-         print("\n数据扩充配置:")
-         print(f"旋转角度范围: {config.aug_rotation_range}")
-         print(f"使用镜像: {config.aug_use_mirror}")
-         print(f"各标签扩充倍数: {config.aug_multipliers}")
-     print("================================\n")
- 
+         table.add_row("数据扩充配置", "旋转角度范围", config.aug_rotation_range)
+         table.add_row("", "使用镜像", "✓" if config.aug_use_mirror else "✗")
+         table.add_row("", "各标签扩充倍数", config.aug_multipliers)
+     
+     # 打印配置表格
+     console.print(table)
+     console.print()
+     
+     # 创建进度显示组件
+     progress = Progress(
+         SpinnerColumn(),
+         TextColumn("[bold blue]{task.description}"),
+         BarColumn(bar_width=40),
+         TextColumn("[bold green]{task.percentage:>3.1f}%"),
+         TimeElapsedColumn()
+     )
+     
      # 启动训练
-     print("开始训练...")
-     try:
-         main_SKD_TSTSAN_with_Aug_with_SKD(config)
-         print("训练完成!")
+     console.print(Panel("[bold yellow]系统准备就绪，即将开始训练...", 
+                        border_style="yellow", 
+                        title="[bold]微表情识别系统", 
+                        subtitle="[bold green]SKD-TSTSAN"))
+     
+     try:
+         # 将进度显示组件传递给训练函数
+         main_SKD_TSTSAN_with_Aug_with_SKD(config, progress_component=progress)
+         
+         # 训练完成后显示成功消息
+         console.print()
+         console.print(Panel("[bold green]训练过程已成功完成！", 
+                            border_style="green", 
+                            title="[bold]训练状态", 
+                            subtitle=f"[bold]用时: {time.strftime('%H:%M:%S', time.gmtime(time.time()))}"))
      except Exception as e:
-         print(f"训练过程出错: {str(e)}")
+         # 训练出错时显示错误消息
+         console.print()
+         error_text = Text(f"训练过程出错: {str(e)}", style="bold red")
+         console.print(Panel(error_text, border_style="red", title="[bold]错误信息"))
          raise
      finally:
```

---

<no message>

---

<details>
            <summary>Searched codebase "train_classify_SKD_TSTSAN_functions.py main_SKD_TSTSAN_with_Aug_with_SKD 函数定义" • **5** results</summary>
        
| File | Lines |
|------|-------|
| `train_classify_SKD_TSTSAN_functions.py` | L727-747 |
| `train_classify_SKD_TSTSAN_functions.py` | L803-824 |
| `casme2训练/train_classify_SKD_TSTSAN_functions copy.py` | L280-329 |
| `casme2训练/train_classify_SKD_TSTSAN_functions.py` | L249-298 |
| `train_classify_SKD_TSTSAN_functions.py` | L747-778 |

</details>

---



---

