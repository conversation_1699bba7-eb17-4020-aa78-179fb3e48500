<!-- Generated by SpecStory -->

# Calculate overall evaluation metrics (2025-06-22 09:01Z)

_**User**_

================================================================================
【实验结果报告】
================================================================================

【基本信息】
实验名称: ceshi
时间: 2025-06-22 17:00:18
总训练时间: 00:09:53
数据集: CASME2_LOSO_full


【系统环境】
操作系统: Linux 6.8.0-1028-nvidia
处理器: 32核
内存: 63.3GB
GPU: NVIDIA A800 80GB PCIe (79.3GB)

【模型配置】
模型架构: SKD_TSTSAN
是否使用预训练: 是
预训练模型: SKD-TSTSAN.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
批次大小: 32
最大迭代次数: 20
损失函数: FocalLoss_weighted
随机种子: 1337

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: 0.4327
- UAR分数: 0.5407

【各表情类别准确率】
--------------------------------------------------
- 快乐: 0.2703
- 惊讶: 0.9333
- 厌恶: 0.6538
- 压抑: 0.8462
- 其他: 0.0000


【各受试者评估结果】
--------------------------------------------------
受试者 sub01: UF1=0.1333, UAR=0.2000
受试者 sub02: UF1=0.3455, UAR=0.3600
受试者 sub03: UF1=0.2400, UAR=0.2500
受试者 sub04: UF1=0.1333, UAR=0.2000
受试者 sub05: UF1=0.1600, UAR=0.1714
受试者 sub06: UF1=0.1818, UAR=0.2000
受试者 sub07: UF1=0.1333, UAR=0.1200
受试者 sub08: UF1=0.1333, UAR=0.2000
受试者 sub09: UF1=0.2338, UAR=0.2500
受试者 sub10: UF1=0.0000, UAR=0.0000
受试者 sub11: UF1=0.1200, UAR=0.1500
受试者 sub12: UF1=0.4985, UAR=0.5000
受试者 sub13: UF1=0.1333, UAR=0.1000
受试者 sub15: UF1=0.3600, UAR=0.4000
受试者 sub16: UF1=0.0000, UAR=0.0000
受试者 sub17: UF1=0.2241, UAR=0.2667
受试者 sub18: UF1=0.0000, UAR=0.0000
受试者 sub19: UF1=0.3946, UAR=0.4667
受试者 sub20: UF1=0.0000, UAR=0.0000
受试者 sub21: UF1=0.0000, UAR=0.0000
受试者 sub22: UF1=0.2000, UAR=0.2000
受试者 sub23: UF1=0.3800, UAR=0.3667
受试者 sub24: UF1=0.1600, UAR=0.1333
受试者 sub25: UF1=0.3818, UAR=0.4000
受试者 sub26: UF1=0.2300, UAR=0.2500
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率   
--------------------------------------------------
  sub01      0.1333     0.2000     0.2500 
  sub02      0.3455     0.3600     0.7000 
  sub03      0.2400     0.2500     0.3750 
  sub04      0.1333     0.2000     0.5000 
  sub05      0.1600     0.1714     0.4800 
  sub06      0.1818     0.2000     0.5556 
  sub07      0.1333     0.1200     0.3750 
  sub08      0.1333     0.2000     0.5000 
  sub09      0.2338     0.2500     0.6000 
  sub10      0.0000     0.0000     0.0000 
  sub11      0.1200     0.1500     0.3750 
  sub12      0.4985     0.5000     0.8571 
  sub13      0.1333     0.1000     0.2500 
  sub15      0.3600     0.4000     0.8333 
  sub16      0.0000     0.0000     0.0000 
  sub17      0.2241     0.2667     0.5000 
  sub18      0.0000     0.0000     0.0000 
  sub19      0.3946     0.4667     0.7200 
  sub20      0.0000     0.0000     0.0000 
  sub21      0.0000     0.0000     0.0000 
  sub22      0.2000     0.2000     1.0000 
  sub23      0.3800     0.3667     0.4286 
  sub24      0.1600     0.1333     0.2500 
  sub25      0.3818     0.4000     0.8000 
  sub26      0.2300     0.2500     0.5882 
--------------------------------------------------
    平均       0.1911     0.2074     0.4375 


【最优预测结果与原始标签对比】
--------------------------------------------------

受试者 sub01:
预测标签: [3, 3, 2, 2, 3, 3, 2, 2]
真实标签: [4, 4, 4, 4, 0, 0, 2, 2]
--------------------------------------------------

受试者 sub02:
预测标签: [3, 1, 1, 1, 3, 3, 3, 0, 3, 3]
真实标签: [4, 1, 1, 1, 0, 3, 3, 3, 3, 3]
--------------------------------------------------

受试者 sub03:
预测标签: [3, 1, 1, 1, 3, 0, 3, 2]
真实标签: [4, 4, 1, 1, 2, 2, 2, 2]
--------------------------------------------------

受试者 sub04:
预测标签: [2, 2, 2, 2]
真实标签: [4, 4, 2, 2]
--------------------------------------------------

受试者 sub05:
预测标签: [2, 1, 2, 2, 3, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 3, 1, 1, 1, 1]
真实标签: [4, 4, 4, 4, 4, 4, 4, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 2]
--------------------------------------------------

受试者 sub06:
预测标签: [1, 1, 1, 1, 1, 1, 3, 3, 0]
真实标签: [4, 1, 1, 1, 1, 1, 0, 0, 2]
--------------------------------------------------

受试者 sub07:
预测标签: [2, 0, 0, 2, 2, 2, 0, 0]
真实标签: [4, 4, 4, 2, 2, 2, 2, 2]
--------------------------------------------------

受试者 sub08:
预测标签: [3, 3]
真实标签: [4, 3]
--------------------------------------------------

受试者 sub09:
预测标签: [2, 3, 0, 3, 3, 3, 3, 3, 3, 3]
真实标签: [4, 0, 0, 0, 0, 3, 3, 3, 3, 3]
--------------------------------------------------

受试者 sub10:
预测标签: [2, 2, 2, 0, 2, 2, 2, 2, 2, 2, 2, 0, 3]
真实标签: [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4]
--------------------------------------------------

受试者 sub11:
预测标签: [2, 2, 3, 2, 2, 0, 2, 2]
真实标签: [4, 4, 4, 4, 2, 2, 2, 2]
--------------------------------------------------

受试者 sub12:
预测标签: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 2, 2, 2, 2, 2, 2]
真实标签: [4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 2, 2, 2, 2, 2]
--------------------------------------------------

受试者 sub13:
预测标签: [1, 2, 0, 1]
真实标签: [4, 4, 0, 0]
--------------------------------------------------

受试者 sub15:
预测标签: [1, 1, 1, 0, 0, 0]
真实标签: [1, 1, 1, 0, 0, 2]
--------------------------------------------------

受试者 sub16:
预测标签: [3, 3, 1]
真实标签: [4, 0, 0]
--------------------------------------------------

受试者 sub17:
预测标签: [3, 3, 3, 3, 3, 3, 3, 3, 3, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
真实标签: [4, 1, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
--------------------------------------------------

受试者 sub18:
预测标签: [2, 2, 3]
真实标签: [4, 4, 4]
--------------------------------------------------

受试者 sub19:
预测标签: [2, 0, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 2, 2, 1, 1, 2, 2, 2]
真实标签: [4, 4, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 2, 2, 2]
--------------------------------------------------

受试者 sub20:
预测标签: [3, 0, 0, 0, 0, 0, 0, 0]
真实标签: [4, 4, 4, 4, 4, 4, 2, 2]
--------------------------------------------------

受试者 sub21:
预测标签: [0, 0]
真实标签: [4, 3]
--------------------------------------------------

受试者 sub22:
预测标签: [3, 3]
真实标签: [3, 3]
--------------------------------------------------

受试者 sub23:
预测标签: [2, 0, 2, 3, 3, 1, 1]
真实标签: [4, 0, 2, 2, 3, 3, 3]
--------------------------------------------------

受试者 sub24:
预测标签: [3, 3, 3, 1, 1, 3, 0, 3]
真实标签: [4, 4, 4, 1, 1, 1, 2, 2]
--------------------------------------------------

受试者 sub25:
预测标签: [0, 1, 1, 1, 1, 1, 1, 2, 2, 2]
真实标签: [4, 4, 1, 1, 1, 1, 1, 2, 2, 2]
--------------------------------------------------

受试者 sub26:
预测标签: [0, 2, 0, 0, 0, 3, 3, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]
真实标签: [4, 4, 4, 4, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2]
--------------------------------------------------


给我一个代码 计算依据这个 上面的信息计算整体的评价指标

---

_**Assistant**_

Read file: train_classify_SKD_TSTSAN_functions.py

---

```diff
- 
+ import numpy as np
+ from sklearn.metrics import confusion_matrix
+ import matplotlib.pyplot as plt
+ import seaborn as sns
+ 
+ def recognition_evaluation(final_gt, final_pred):
+     """
+     评估表情识别结果,计算UF1(未加权F1分数)和UAR(未加权平均召回率)
+     
+     参数:
+         final_gt: 真实标签列表
+         final_pred: 预测标签列表
+     返回:
+         UF1: 未加权F1分数(各类别F1的平均值)
+         UAR: 未加权平均召回率(各类别召回率的平均值)
+         f1_list: 各类别的F1分数列表
+         ar_list: 各类别的召回率列表
+     """
+     try:
+         # 标签字典 - CASME2数据集
+         label_dict = {'happiness': 0, 'surprise': 1, 'disgust': 2, 'repression': 3, 'others': 4}
+         
+         f1_list = []  # 存储每个类别的F1分数
+         ar_list = []  # 存储每个类别的召回率(Average Recall)
+         
+         # 对每个表情类别分别计算
+         for emotion, emotion_index in label_dict.items():
+             # 转换为二分类问题(one-vs-rest)
+             gt_recog = [1 if x == emotion_index else 0 for x in final_gt]
+             pred_recog = [1 if x == emotion_index else 0 for x in final_pred]
+             
+             # 计算混淆矩阵
+             cm = confusion_matrix(gt_recog, pred_recog)
+             
+             # 确保混淆矩阵是2x2的
+             if cm.shape != (2, 2):
+                 # 如果某个类别完全没有样本，扩展混淆矩阵
+                 if cm.shape == (1, 1):
+                     if gt_recog.count(1) == 0:  # 如果没有正样本
+                         TN = cm[0, 0]
+                         FP, FN, TP = 0, 0, 0
+                     else:  # 如果只有正样本
+                         TP = cm[0, 0]
+                         FP, FN, TN = 0, 0, 0
+                 else:
+                     print(f"警告: {emotion}类别的混淆矩阵形状异常: {cm.shape}")
+                     continue
+             else:
+                 TN, FP, FN, TP = cm.ravel()
+             
+             # 计算F1分数
+             if (2 * TP + FP + FN) > 0:
+                 f1_recog = (2 * TP) / (2 * TP + FP + FN)
+             else:
+                 f1_recog = 0
+             
+             # 计算召回率
+             num_samples = gt_recog.count(1)  # 该类别的总样本数
+             ar_recog = TP / num_samples if num_samples > 0 else 0
+             
+             # 存储每个类别的指标
+             f1_list.append(f1_recog)
+             ar_list.append(ar_recog)
+                 
+         # 计算未加权平均值
+         if len(f1_list) > 0 and len(ar_list) > 0:
+             # UF1: 所有类别F1分数的算术平均值
+             UF1 = float(np.mean(f1_list))
+             # UAR: 所有类别召回率的算术平均值
+             UAR = float(np.mean(ar_list))
+             return UF1, UAR, f1_list, ar_list
+         else:
+             print("没有有效的评估结果")
+             return 0.0, 0.0, [], []
+             
+     except Exception as e:
+         print(f"评估过程出错: {str(e)}")
+         print("GT标签分布:", np.unique(final_gt, return_counts=True))
+         print("Pred标签分布:", np.unique(final_pred, return_counts=True))
+         return 0.0, 0.0, [], []
+ 
+ def calculate_metrics():
+     """计算实验结果报告中的评估指标"""
+     # 定义情绪标签映射
+     emotion_labels = {0: '快乐', 1: '惊讶', 2: '厌恶', 3: '压抑', 4: '其他'}
+     
+     # 从报告中提取的数据
+     subject_results = {
+         'sub01': {'pred': [3, 3, 2, 2, 3, 3, 2, 2], 'truth': [4, 4, 4, 4, 0, 0, 2, 2]},
+         'sub02': {'pred': [3, 1, 1, 1, 3, 3, 3, 0, 3, 3], 'truth': [4, 1, 1, 1, 0, 3, 3, 3, 3, 3]},
+         'sub03': {'pred': [3, 1, 1, 1, 3, 0, 3, 2], 'truth': [4, 4, 1, 1, 2, 2, 2, 2]},
+         'sub04': {'pred': [2, 2, 2, 2], 'truth': [4, 4, 2, 2]},
+         'sub05': {'pred': [2, 1, 2, 2, 3, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 3, 1, 1, 1, 1], 'truth': [4, 4, 4, 4, 4, 4, 4, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 2]},
+         'sub06': {'pred': [1, 1, 1, 1, 1, 1, 3, 3, 0], 'truth': [4, 1, 1, 1, 1, 1, 0, 0, 2]},
+         'sub07': {'pred': [2, 0, 0, 2, 2, 2, 0, 0], 'truth': [4, 4, 4, 2, 2, 2, 2, 2]},
+         'sub08': {'pred': [3, 3], 'truth': [4, 3]},
+         'sub09': {'pred': [2, 3, 0, 3, 3, 3, 3, 3, 3, 3], 'truth': [4, 0, 0, 0, 0, 3, 3, 3, 3, 3]},
+         'sub10': {'pred': [2, 2, 2, 0, 2, 2, 2, 2, 2, 2, 2, 0, 3], 'truth': [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4]},
+         'sub11': {'pred': [2, 2, 3, 2, 2, 0, 2, 2], 'truth': [4, 4, 4, 4, 2, 2, 2, 2]},
+         'sub12': {'pred': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 2, 2, 2, 2, 2, 2], 'truth': [4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 2, 2, 2, 2, 2]},
+         'sub13': {'pred': [1, 2, 0, 1], 'truth': [4, 4, 0, 0]},
+         'sub15': {'pred': [1, 1, 1, 0, 0, 0], 'truth': [1, 1, 1, 0, 0, 2]},
+         'sub16': {'pred': [3, 3, 1], 'truth': [4, 0, 0]},
+         'sub17': {'pred': [3, 3, 3, 3, 3, 3, 3, 3, 3, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], 'truth': [4, 1, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]},
+         'sub18': {'pred': [2, 2, 3], 'truth': [4, 4, 4]},
+         'sub19': {'pred': [2, 0, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 2, 2, 1, 1, 2, 2, 2], 'truth': [4, 4, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 2, 2, 2]},
+         'sub20': {'pred': [3, 0, 0, 0, 0, 0, 0, 0], 'truth': [4, 4, 4, 4, 4, 4, 2, 2]},
+         'sub21': {'pred': [0, 0], 'truth': [4, 3]},
+         'sub22': {'pred': [3, 3], 'truth': [3, 3]},
+         'sub23': {'pred': [2, 0, 2, 3, 3, 1, 1], 'truth': [4, 0, 2, 2, 3, 3, 3]},
+         'sub24': {'pred': [3, 3, 3, 1, 1, 3, 0, 3], 'truth': [4, 4, 4, 1, 1, 1, 2, 2]},
+         'sub25': {'pred': [0, 1, 1, 1, 1, 1, 1, 2, 2, 2], 'truth': [4, 4, 1, 1, 1, 1, 1, 2, 2, 2]},
+         'sub26': {'pred': [0, 2, 0, 0, 0, 3, 3, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], 'truth': [4, 4, 4, 4, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2]}
+     }
+     
+     # 合并所有预测和真实标签
+     all_pred = []
+     all_truth = []
+     for subject, data in subject_results.items():
+         all_pred.extend(data['pred'])
+         all_truth.extend(data['truth'])
+     
+     # 计算总体UF1和UAR
+     UF1, UAR, f1_list, ar_list = recognition_evaluation(all_truth, all_pred)
+     
+     # 计算每个情绪类别的准确率
+     emotion_acc = {}
+     for label, emotion in emotion_labels.items():
+         mask = np.array(all_truth) == label
+         if np.any(mask):
+             acc = np.mean(np.array(all_pred)[mask] == np.array(all_truth)[mask])
+             emotion_acc[emotion] = acc
+         else:
+             emotion_acc[emotion] = 0.0
+     
+     # 计算每个受试者的准确率、UF1和UAR
+     subject_metrics = {}
+     for subject, data in subject_results.items():
+         pred = data['pred']
+         truth = data['truth']
+         
+         # 计算准确率
+         accuracy = np.mean(np.array(pred) == np.array(truth))
+         
+         # 计算UF1和UAR
+         subj_UF1, subj_UAR, _, _ = recognition_evaluation(truth, pred)
+         
+         subject_metrics[subject] = {
+             'accuracy': accuracy,
+             'UF1': subj_UF1,
+             'UAR': subj_UAR
+         }
+     
+     # 计算混淆矩阵
+     cm = confusion_matrix(all_truth, all_pred)
+     
+     # 打印结果
+     print("=" * 80)
+     print("【总体评估指标】")
+     print(f"UF1: {UF1:.4f}")
+     print(f"UAR: {UAR:.4f}")
+     print("\n【各表情类别F1分数】")
+     for i, emotion in enumerate(emotion_labels.values()):
+         if i < len(f1_list):
+             print(f"{emotion}: {f1_list[i]:.4f}")
+     
+     print("\n【各表情类别召回率】")
+     for i, emotion in enumerate(emotion_labels.values()):
+         if i < len(ar_list):
+             print(f"{emotion}: {ar_list[i]:.4f}")
+     
+     print("\n【各表情类别准确率】")
+     for emotion, acc in emotion_acc.items():
+         print(f"{emotion}: {acc:.4f}")
+     
+     print("\n【各受试者评估指标】")
+     print(f"{'受试者':<10} {'准确率':<10} {'UF1':<10} {'UAR':<10}")
+     print("-" * 40)
+     
+     avg_acc = 0
+     avg_uf1 = 0
+     avg_uar = 0
+     count = 0
+     
+     for subject, metrics in subject_metrics.items():
+         print(f"{subject:<10} {metrics['accuracy']:.4f} {metrics['UF1']:.4f} {metrics['UAR']:.4f}")
+         avg_acc += metrics['accuracy']
+         avg_uf1 += metrics['UF1']
+         avg_uar += metrics['UAR']
+         count += 1
+     
+     print("-" * 40)
+     print(f"平均值: {avg_acc/count:.4f} {avg_uf1/count:.4f} {avg_uar/count:.4f}")
+     
+     # 绘制混淆矩阵
+     plt.figure(figsize=(10, 8))
+     sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
+                 xticklabels=list(emotion_labels.values()),
+                 yticklabels=list(emotion_labels.values()))
+     plt.xlabel('预测标签')
+     plt.ylabel('真实标签')
+     plt.title('混淆矩阵')
+     plt.savefig('confusion_matrix.png')
+     print("\n混淆矩阵已保存为 confusion_matrix.png")
+     
+     return {
+         'UF1': UF1,
+         'UAR': UAR,
+         'emotion_f1': dict(zip(emotion_labels.values(), f1_list)),
+         'emotion_recall': dict(zip(emotion_labels.values(), ar_list)),
+         'emotion_acc': emotion_acc,
+         'subject_metrics': subject_metrics,
+         'confusion_matrix': cm
+     }
+ 
+ if __name__ == "__main__":
+     results = calculate_metrics()
+     print("\n计算完成!") 
```

---

Cancelled

---



---

_**User**_

@后处理评价指标计算evaluate_results.py 解决问题

---

_**Assistant**_

我明白了,这个问题主要有两个需要解决的地方:

1. 混淆矩阵计算时的警告问题
2. 中文字体显示问题

让我修改代码来解决这些问题:

```python
import numpy as np
from sklearn.metrics import confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.font_manager import FontProperties

def recognition_evaluation(final_gt, final_pred):
    """评估表情识别结果"""
    try:
        label_dict = {'happiness': 0, 'surprise': 1, 'disgust': 2, 'repression': 3, 'others': 4}
        
        f1_list = []
        ar_list = []
        
        # 修改: 添加labels参数确保混淆矩阵形状一致
        all_labels = [0, 1]  # 二分类问题的所有可能标签
        
        for emotion, emotion_index in label_dict.items():
            gt_recog = [1 if x == emotion_index else 0 for x in final_gt]
            pred_recog = [1 if x == emotion_index else 0 for x in final_pred]
            
            # 修改: 使用labels参数
            cm = confusion_matrix(gt_recog, pred_recog, labels=all_labels)
            TN, FP, FN, TP = cm.ravel()
            
            # 计算F1分数
            if (2 * TP + FP + FN) > 0:
                f1_recog = (2 * TP) / (2 * TP + FP + FN)
            else:
                f1_recog = 0
            
            # 计算召回率
            num_samples = gt_recog.count(1)
            ar_recog = TP / num_samples if num_samples > 0 else 0
            
            f1_list.append(f1_recog)
            ar_list.append(ar_recog)
                
        if len(f1_list) > 0 and len(ar_list) > 0:
            UF1 = float(np.mean(f1_list))
            UAR = float(np.mean(ar_list))
            return UF1, UAR, f1_list, ar_list
        else:
            return 0.0, 0.0, [], []
            
    except Exception as e:
        print(f"评估过程出错: {str(e)}")
        return 0.0, 0.0, [], []

def calculate_metrics():
    """计算评估指标"""
    # 定义情绪标签映射
    emotion_labels = {0: '快乐', 1: '惊讶', 2: '厌恶', 3: '压抑', 4: '其他'}
    
    # ... (保持原有的数据处理代码不变) ...

    # 修改: 设置中文字体
    try:
        # 尝试使用系统自带的中文字体
        font_paths = [
            '/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf',  # Linux
            '/System/Library/Fonts/PingFang.ttc',  # macOS
            'C:/Windows/Fonts/simhei.ttf',  # Windows
            '/usr/share/fonts/truetype/noto/NotoSansCJK-Regular.ttc'  # 通用
        ]
        
        font = None
        for path in font_paths:
            try:
                font = FontProperties(fname=path)
                break
            except:
                continue
                
        if font is None:
            print("警告: 未找到合适的中文字体,将使用系统默认字体")
            font = FontProperties()
            
    except Exception as e:
        print(f"字体设置出错: {str(e)}")
        font = FontProperties()

    # 修改: 绘制混淆矩阵时使用中文字体
    plt.figure(figsize=(10, 8))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=list(emotion_labels.values()),
                yticklabels=list(emotion_labels.values()))
                
    plt.xlabel('预测标签', fontproperties=font)
    plt.ylabel('真实标签', fontproperties=font)
    plt.title('混淆矩阵', fontproperties=font)
    
    # 设置刻度标签字体
    plt.xticks(fontproperties=font)
    plt.yticks(fontproperties=font)
    
    # 修改: 调整布局避免标签被截断
    plt.tight_layout()
    
    # 保存图像
    plt.savefig('confusion_matrix.png', bbox_inches='tight', dpi=300)
    plt.close()
    
    print("\n混淆矩阵已保存为 confusion_matrix.png")
    
    return {
        'UF1': UF1,
        'UAR': UAR,
        'emotion_f1': dict(zip(emotion_labels.values(), f1_list)),
        'emotion_recall': dict(zip(emotion_labels.values(), ar_list)),
        'emotion_acc': emotion_acc,
        'subject_metrics': subject_metrics,
        'confusion_matrix': cm
    }

if __name__ == "__main__":
    results = calculate_metrics()
    print("\n计算完成!")
```

主要修改内容:

1. 解决混淆矩阵警告:
   - 在`confusion_matrix`函数调用时添加`labels`参数
   - 确保二分类问题中包含所有可能的标签[0, 1]

2. 解决中文字体显示问题:
   - 添加中文字体支持
   - 尝试多个常见中文字体路径
   - 为所有文字元素设置字体属性
   - 调整图像布局避免标签被截断

3. 其他优化:
   - 增加图像DPI提高清晰度
   - 使用`bbox_inches='tight'`确保保存完整图像
   - 添加`plt.close()`释放内存
   - 完善异常处理和提示信息

这些修改应该能解决警告信息和中文显示问题。如果仍然遇到字体问题,可以根据实际系统环境调整字体路径。

---

_**User**_

/home/<USER>/data/ajq/SKD-TSTSAN-new/后处理评价指标计算evaluate_results.py
/home/<USER>/data/ajq/SKD-TSTSAN-new/文件树.py
/home/<USER>/data/ajq/SKD-TSTSAN-new/预处理0裁切.py
/home/<USER>/data/ajq/SKD-TSTSAN-new/预处理1prepare_casme2_data数据准备.py
/home/<USER>/data/ajq/SKD-TSTSAN-new/预处理2光流optflow_for_classify.py
/home/<USER>/data/ajq/SKD-TSTSAN-new/预处理3统计数据.py
/home/<USER>/data/ajq/SKD-TSTSAN-new/预处理4loso.py
/home/<USER>/data/ajq/SKD-TSTSAN-new/预处理5casme2class5loss数据分析.py
/home/<USER>/data/ajq/SKD-TSTSAN-new/all_model.py
/home/<USER>/data/ajq/SKD-TSTSAN-new/CASME2_LOSO_full_statistics.csv
/home/<USER>/data/ajq/SKD-TSTSAN-new/CASME2-coding-20140508.csv
/home/<USER>/data/ajq/SKD-TSTSAN-new/casme2数据提取.py
/home/<USER>/data/ajq/SKD-TSTSAN-new/casme2csv统计.py
/home/<USER>/data/ajq/SKD-TSTSAN-new/confusion_matrix.png
/home/<USER>/data/ajq/SKD-TSTSAN-new/directory_tree_20250619_165852.txt
/home/<USER>/data/ajq/SKD-TSTSAN-new/directory_tree_20250619_171738.txt
/home/<USER>/data/ajq/SKD-TSTSAN-new/directory_tree_20250619_172557.txt
/home/<USER>/data/ajq/SKD-TSTSAN-new/evaluate_results.py
/home/<USER>/data/ajq/SKD-TSTSAN-new/LICENSE
/home/<USER>/data/ajq/SKD-TSTSAN-new/README.md
/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN_functions.py
/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN.py


压缩上面这些到一个压缩文件

---

_**Assistant**_

我来帮你将这些文件压缩打包。我们可以使用 tar 命令来创建一个压缩文件。让我们按照以下步骤进行：

1. 首先创建一个压缩文件，包含所有指定的文件：

```bash
cd /home/<USER>/data/ajq/SKD-TSTSAN-new/
tar -czvf SKD-TSTSAN-new.tar.gz \
    后处理评价指标计算evaluate_results.py \
    文件树.py \
    预处理0裁切.py \
    预处理1prepare_casme2_data数据准备.py \
    预处理2光流optflow_for_classify.py \
    预处理3统计数据.py \
    预处理4loso.py \
    预处理5casme2class5loss数据分析.py \
    all_model.py \
    CASME2_LOSO_full_statistics.csv \
    CASME2-coding-20140508.csv \
    casme2数据提取.py \
    casme2csv统计.py \
    confusion_matrix.png \
    directory_tree_20250619_165852.txt \
    directory_tree_20250619_171738.txt \
    directory_tree_20250619_172557.txt \
    evaluate_results.py \
    LICENSE \
    README.md \
    train_classify_SKD_TSTSAN_functions.py \
    train_classify_SKD_TSTSAN.py
```

这个命令会创建一个名为 `SKD-TSTSAN-new.tar.gz` 的压缩文件，其中包含所有指定的文件。命令参数说明：
- `-c`: 创建新的归档文件
- `-z`: 使用 gzip 压缩
- `-v`: 显示详细信息
- `-f`: 指定归档文件名

压缩完成后，你可以在当前目录下找到 `SKD-TSTSAN-new.tar.gz` 文件。要解压这个文件，可以使用以下命令：

```bash
tar -xzvf SKD-TSTSAN-new.tar.gz
```

这样就完成了文件的压缩和打包。压缩文件会保留原始的文件结构和权限。你可以使用以下命令查看压缩文件的大小：

```bash
ls -lh SKD-TSTSAN-new.tar.gz
```

需要我为你执行这些命令吗？或者你需要其他格式的压缩文件（比如 .zip）？

---

