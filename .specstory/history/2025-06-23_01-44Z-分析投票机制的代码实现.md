<!-- Generated by SpecStory -->

# 分析投票机制的代码实现 (2025-06-23 01:44Z)

_**User**_

@train_classify_SKD_TSTSAN_functions.py @train_classify_SKD_TSTSAN.py 看一下投票的代码

---

_**Assistant**_

让我帮你分析一下代码中的投票相关实现。主要的投票逻辑在两个文件中都有涉及，我来详细解释一下。

首先，投票的核心函数是在`train_classify_SKD_TSTSAN_functions.py`中的`voting_predict`函数：

```12:25:train_classify_SKD_TSTSAN_functions.py
def voting_predict(predictions, confidences, method='weighted'):
    """
    对多个预测结果进行投票
    参数:
        predictions: 预测标签列表
        confidences: 预测置信度列表
        method: 投票方式 ('majority' 或 'weighted')
    返回:
        最终预测标签
    """
    if method == 'majority':
        # 简单多数投票
        return max(set(predictions), key=predictions.count)
    else:
        # 加权投票
        vote_dict = {}
        for pred, conf in zip(predictions, confidences):
            if pred not in vote_dict:
                vote_dict[pred] = 0
            vote_dict[pred] += conf
        return max(vote_dict.items(), key=lambda x: x[1])[0]
```

这个投票函数支持两种投票方式：
1. `majority`（多数投票）：直接统计每个类别出现的次数，选择出现次数最多的类别
2. `weighted`（加权投票）：根据每个预测的置信度进行加权投票，最终选择加权和最大的类别

投票机制主要用在测试阶段，具体实现在`train_classify_SKD_TSTSAN_functions.py`中的主训练循环里：

```1000:1039:train_classify_SKD_TSTSAN_functions.py
                # 评估模式
                print('\n开始验证...')
                model.eval()
                num_val_correct = 0
                middle1_num_val_correct = 0
                middle2_num_val_correct = 0
                num_val_examples = 0
                temp_best_each_subject_pred = []
                temp_y = []

                # 用于存储每个原始样本的所有预测结果
                original_predictions = {}
                current_sample_idx = 0

                for batch in test_dl:
                    x = batch[0].to(device)
                    y = batch[1].to(device)
                    yhat, AC1_out, AC2_out, final_feature, AC1_feature, AC2_feature = model(x)
                    
                    # 获取预测结果和置信度
                    predictions = torch.max(yhat, 1)[1]
                    confidences = torch.max(torch.softmax(yhat, dim=1), 1)[0]
                    
                    # 如果使用测试数据增强，需要对预测结果进行投票
                    if config.use_test_augmentation:
                        for i in range(len(y)):
                            orig_idx = current_sample_idx // current_multiplier
                            if orig_idx not in original_predictions:
                                original_predictions[orig_idx] = {'preds': [], 'confs': [], 'true_label': y[i].item()}
                            original_predictions[orig_idx]['preds'].append(predictions[i].item())
                            original_predictions[orig_idx]['confs'].append(confidences[i].item())
                            current_sample_idx += 1
```

投票的工作流程是：

1. 当启用测试数据增强时(`config.use_test_augmentation=True`)，每个原始样本会生成多个增强版本
2. 对于每个增强样本：
   - 记录其预测标签(`predictions`)
   - 记录其预测置信度(`confidences`)
   - 将这些信息存储在`original_predictions`字典中，以原始样本的索引为键

然后在所有预测完成后，进行投票决策：

```1041:1052:train_classify_SKD_TSTSAN_functions.py
                # 如果使用测试数据增强，进行投票
                if config.use_test_augmentation:
                    for orig_idx in sorted(original_predictions.keys()):
                        pred_info = original_predictions[orig_idx]
                        final_pred = voting_predict(pred_info['preds'], pred_info['confs'], config.voting_method)
                        temp_best_each_subject_pred.append(final_pred)
                        temp_y.append(pred_info['true_label'])
                        num_val_correct += (final_pred == pred_info['true_label'])
                        num_val_examples += 1
```

投票方式可以通过配置参数来控制，在`train_classify_SKD_TSTSAN.py`中有相关配置：

```326:327:train_classify_SKD_TSTSAN.py
    parser.add_argument('--test_aug_multipliers', type=str, default='6,8,3,8,2', help='测试数据各标签扩充倍数(逗号分隔)')
    parser.add_argument('--voting_method', type=str, default='weighted', choices=['majority', 'weighted'], help='预测结果投票方式')
```

这个投票机制的主要优点是：

1. 提高预测的鲁棒性：通过多个增强样本的预测结果综合决策，减少单个预测的偶然性
2. 灵活的投票策略：
   - 多数投票适合预测结果比较确定的情况
   - 加权投票能够利用模型的置信度信息，对更有把握的预测给予更高的权重
3. 与数据增强的良好配合：充分利用数据增强产生的多个版本，提高预测准确性

这种设计在处理微表情这种细微变化的识别任务时特别有用，因为它能够综合考虑不同角度和变化下的预测结果。

---

_**User**_

我的真实标签和投票标签 是如何构建的

---

_**Assistant**_

让我帮你分析真实标签和投票标签的构建过程。这个过程主要发生在`train_classify_SKD_TSTSAN_functions.py`中的验证阶段。

让我们详细看一下代码的实现：

```1000:1016:train_classify_SKD_TSTSAN_functions.py
                # 评估模式
                print('\n开始验证...')
                model.eval()
                num_val_correct = 0
                middle1_num_val_correct = 0
                middle2_num_val_correct = 0
                num_val_examples = 0
                temp_best_each_subject_pred = []  # 存储预测标签
                temp_y = []  # 存储真实标签

                # 用于存储每个原始样本的所有预测结果
                original_predictions = {}
                current_sample_idx = 0
```

标签构建的过程分两种情况：

1. 不使用测试数据增强时的标签构建：
```1029:1037:train_classify_SKD_TSTSAN_functions.py
                    else:
                        # 不使用增强时直接添加预测结果
                        for i in range(len(y)):
                            temp_best_each_subject_pred.append(predictions[i].item())  # 直接添加预测标签
                            temp_y.append(y[i].item())  # 直接添加真实标签
                            num_val_correct += (predictions[i] == y[i]).item()
                            num_val_examples += 1
```

2. 使用测试数据增强时的标签构建：
```1018:1028:train_classify_SKD_TSTSAN_functions.py
                    # 如果使用测试数据增强，需要对预测结果进行投票
                    if config.use_test_augmentation:
                        for i in range(len(y)):
                            orig_idx = current_sample_idx // current_multiplier  # 计算原始样本的索引
                            if orig_idx not in original_predictions:
                                original_predictions[orig_idx] = {
                                    'preds': [], 
                                    'confs': [], 
                                    'true_label': y[i].item()  # 记录真实标签
                                }
                            original_predictions[orig_idx]['preds'].append(predictions[i].item())  # 记录预测标签
                            original_predictions[orig_idx]['confs'].append(confidences[i].item())  # 记录置信度
                            current_sample_idx += 1
```

然后进行投票决策：
```1041:1047:train_classify_SKD_TSTSAN_functions.py
                # 如果使用测试数据增强，进行投票
                if config.use_test_augmentation:
                    for orig_idx in sorted(original_predictions.keys()):
                        pred_info = original_predictions[orig_idx]
                        final_pred = voting_predict(pred_info['preds'], pred_info['confs'], config.voting_method)  # 投票得到最终预测
                        temp_best_each_subject_pred.append(final_pred)  # 添加投票后的预测标签
                        temp_y.append(pred_info['true_label'])  # 添加对应的真实标签
```

这些标签的来源是从数据加载过程开始的。在加载测试数据时：

```714:734:train_classify_SKD_TSTSAN_functions.py
            # 加载测试数据
            print('正在加载测试数据...')
            expression = os.listdir(main_path + '/' + n_subName + '/test')
            for n_expression in expression:
                case_list = get_folder_all_cases(main_path + '/' + n_subName + '/test/' + n_expression)
                label = int(n_expression)  # 文件夹名即为标签

                # 获取当前标签的扩充倍数
                if config.use_test_augmentation:
                    multipliers = [int(x) for x in config.test_aug_multipliers.split(',')]
                    current_multiplier = multipliers[label]
                    min_angle, max_angle = map(float, config.aug_rotation_range.split(','))
                else:
                    current_multiplier = 1

                for case in case_list:
                    y_test.append(label)  # 添加真实标签
```

总结一下标签构建的关键点：

1. 真实标签(`temp_y`)的构建：
   - 来源于测试数据目录的文件夹名称（整数标签）
   - 在数据加载时就已经确定
   - 对于增强数据，每个原始样本的真实标签会被复制多次

2. 预测标签(`temp_best_each_subject_pred`)的构建：
   - 不使用增强时：直接使用模型的预测结果
   - 使用增强时：
     - 先收集每个原始样本的所有增强版本的预测结果
     - 然后通过投票机制（多数投票或加权投票）得到最终预测
     - 投票结果作为该样本的最终预测标签

3. 标签对应关系的维护：
   - 通过`current_sample_idx`和`current_multiplier`来追踪增强样本与原始样本的对应关系
   - 确保投票后的预测标签与真实标签一一对应

这种设计确保了即使在使用数据增强的情况下，也能正确维护预测标签和真实标签的对应关系，为后续的性能评估提供准确的基础。

---

_**User**_

所以最终预测的对比的还是这个真实数据对吧

---

_**Assistant**_

是的,你理解得对。让我详细解释一下最终预测与真实数据的对比过程：

```1041:1047:train_classify_SKD_TSTSAN_functions.py
                # 如果使用测试数据增强，进行投票
                if config.use_test_augmentation:
                    for orig_idx in sorted(original_predictions.keys()):
                        pred_info = original_predictions[orig_idx]
                        final_pred = voting_predict(pred_info['preds'], pred_info['confs'], config.voting_method)  # 投票得到最终预测
                        temp_best_each_subject_pred.append(final_pred)  # 最终预测标签
                        temp_y.append(pred_info['true_label'])  # 原始真实标签
```

这里的关键点是：

1. 虽然我们对测试数据进行了增强（比如一个样本可能产生了多个增强版本），但在最终评估时：
   - `pred_info['true_label']`是原始样本的真实标签
   - `final_pred`是通过投票机制得到的一个最终预测结果
   - 每个原始样本只会产生一个最终的预测结果

2. 最终的性能评估是在原始样本级别进行的：
```1049:1052:train_classify_SKD_TSTSAN_functions.py
                        num_val_correct += (final_pred == pred_info['true_label'])  # 计算正确预测数
                        num_val_examples += 1  # 计数原始样本数量
```

所以，尽管在预测过程中使用了数据增强来提高模型的鲁棒性，但最终的评估确实是在原始数据的粒度上进行的，确保了评估的公平性和准确性。每个原始样本只对应一个预测结果和一个真实标签，不会因为数据增强而重复计算或影响评估结果。

---

