<!-- Generated by SpecStory -->

# Optimize training order for micro-expression recognition (2025-06-20 15:33:10)

_**User**_

```thinking
让我分析一下训练顺序对实验结果的影响:

1. 当前训练流程:
- 按照sub01到sub26的顺序依次训练
- 每个subject作为测试集,其他subject作为训练集(LOSO策略)
- 每个subject的样本数量和标签分布都不同

2. 样本分布分析:
- 总样本244个
- 标签分布不均衡:
  * 标签4(others): 99个(40.6%)
  * 标签2(disgust): 62个(25.4%) 
  * 标签0(happiness): 32个(13.1%)
  * 标签3(repression): 26个(10.7%)
  * 标签1(surprise): 25个(10.2%)

3. 训练顺序可能的影响:
- 样本数量差异大:
  * sub17: 34个样本
  * sub21/sub22: 只有2个样本
  * 样本数量不均会影响模型的学习

- 标签分布差异:
  * sub10: 全是标签4
  * sub15: 3个不同标签
  * 不同subject的标签分布差异很大

4. 潜在问题:
- 先训练样本多的subject可能会导致模型过拟合于这些样本特征
- 标签分布不均衡会影响模型的泛化能力
- 连续训练相似分布的subject可能导致模型偏向某些特征

5. 建议改进:
- 可以按照样本数量或标签分布相似度重新排序训练顺序
- 对每个subject的训练样本进行平衡采样
- 在训练过程中监控不同标签的性能表现
```

基于以上分析,我建议对训练顺序进行如下优化:

1. 按样本数量分组训练:
```python
# 建议的训练顺序分组
large_subjects = ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub10'] # >12个样本
medium_subjects = ['sub23', 'sub12', 'sub20', 'sub11', 'sub01', 'sub07'] # 7-12个样本  
small_subjects = ['sub24', 'sub25', 'sub03', 'sub04', 'sub06', 'sub13'] # 3-6个样本
very_small_subjects = ['sub08', 'sub15', 'sub16', 'sub18', 'sub21', 'sub22'] # <=3个样本
```

2. 在代码中添加样本平衡策略:
```python
# 在训练数据加载时添加
def balance_batch_samples(batch_x, batch_y):
    # 对每个batch进行平衡采样
    unique_labels = torch.unique(batch_y)
    min_count = min([torch.sum(batch_y == label) for label in unique_labels])
    balanced_indices = []
    for label in unique_labels:
        label_indices = torch.where(batch_y == label)[0]
        selected_indices = label_indices[torch.randperm(len(label_indices))[:min_count]]
        balanced_indices.extend(selected_indices)
    return batch_x[balanced_indices], batch_y[balanced_indices]
```

3. 修改训练循环:
```python
# 在主训练循环中添加
for group in [large_subjects, medium_subjects, small_subjects, very_small_subjects]:
    for n_subName in group:
        # 现有的训练代码
        ...
        # 添加样本平衡
        if config.balance_samples:
            x, y = balance_batch_samples(x, y)
        ...
```

这样的优化可以:
1. 避免样本数量差异过大带来的训练不稳定
2. 减少标签分布不均衡的影响
3. 提高模型对少样本类别的识别能力
4. 增强模型的泛化性能

建议在实验中对比:
1. 原始训练顺序的结果
2. 按样本数量分组后的结果
3. 添加样本平衡后的结果

这样可以验证训练顺序和样本平衡对模型性能的影响。



看一下 这个训练的顺序可以吗？
修改代码@train_classify_SKD_TSTSAN_functions.py 

---

_**Assistant**_

我来帮你修改代码以优化训练顺序。首先我会在`train_classify_SKD_TSTSAN_functions.py`中添加相关的训练顺序控制和样本平衡功能。

---

```diff
  from sklearn.metrics import classification_report, precision_recall_fscore_support
  
+ # 按样本数量定义训练顺序分组
+ TRAINING_ORDER = {
+     'large_subjects': ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub10'],  # >12个样本
+     'medium_subjects': ['sub23', 'sub12', 'sub20', 'sub11', 'sub01', 'sub07'],  # 7-12个样本
+     'small_subjects': ['sub24', 'sub25', 'sub03', 'sub04', 'sub06', 'sub13'],  # 3-6个样本
+     'very_small_subjects': ['sub08', 'sub15', 'sub16', 'sub18', 'sub21', 'sub22']  # <=3个样本
+ }
+ 
+ def get_ordered_subjects():
+     """
+     获取按样本数量排序的训练顺序
+     """
+     ordered_subjects = []
+     for group in ['large_subjects', 'medium_subjects', 'small_subjects', 'very_small_subjects']:
+         ordered_subjects.extend(TRAINING_ORDER[group])
+     return ordered_subjects
+ 
+ def balance_batch_samples(batch_x, batch_y):
+     """
+     对每个batch进行平衡采样
+     参数:
+         batch_x: 输入数据
+         batch_y: 标签
+     返回:
+         balanced_x: 平衡后的输入数据
+         balanced_y: 平衡后的标签
+     """
+     unique_labels = torch.unique(batch_y)
+     min_count = min([torch.sum(batch_y == label) for label in unique_labels])
+     balanced_indices = []
+     for label in unique_labels:
+         label_indices = torch.where(batch_y == label)[0]
+         selected_indices = label_indices[torch.randperm(len(label_indices))[:min_count]]
+         balanced_indices.extend(selected_indices)
+     balanced_indices = torch.tensor(balanced_indices)
+     return batch_x[balanced_indices], batch_y[balanced_indices]
+ 
  # CASME2数据集中各类别样本数量
  CASME2_numbers = [32, 25, 63, 27, 99]
```

---

现在我们需要修改主训练函数`main_SKD_TSTSAN_with_Aug_with_SKD`来使用新的训练顺序：

---

```diff
  
      main_path = config.main_path
-     subName = os.listdir(main_path)
- 
-     for n_subName in subName:
+     
+     # 使用优化后的训练顺序
+     ordered_subjects = get_ordered_subjects()
+     print("训练顺序:", ordered_subjects)
+     
+     for group_name, subjects in TRAINING_ORDER.items():
+         print(f"\n开始训练{group_name}组...")
+         
+         for n_subName in subjects:
+             if n_subName not in os.listdir(main_path):
+                 print(f"警告: {n_subName}不存在于数据集中,跳过...")
+                 continue
+                 
              print('当前处理主体:', n_subName)
+             print(f'组别: {group_name}')
  
              X_train = []
```

```diff
                  np.random.seed(seed + worker_id)
  
-         train_dl = DataLoader(dataset_train, batch_size=batch_size, shuffle=True, num_workers=0, pin_memory=True,
+             # 添加样本平衡的DataLoader包装
+             class BalancedBatchSampler(torch.utils.data.Sampler):
+                 def __init__(self, dataset, labels):
+                     self.labels = labels
+                     self.dataset = dataset
+                     self.indices = list(range(len(dataset)))
+                     self.batch_size = batch_size
+                     
+                     # 计算每个标签的样本数
+                     label_to_count = {}
+                     for idx in self.indices:
+                         label = self.labels[idx]
+                         if label in label_to_count:
+                             label_to_count[label] += 1
+                         else:
+                             label_to_count[label] = 1
+                             
+                     # 使用最小的类别数量作为每个类别的采样数
+                     self.samples_per_class = min(label_to_count.values())
+                     
+                 def __iter__(self):
+                     indices = []
+                     # 对每个类别进行平衡采样
+                     for label in torch.unique(self.labels):
+                         label_indices = (self.labels == label).nonzero().view(-1)
+                         indices.extend(label_indices[torch.randperm(len(label_indices))[:self.samples_per_class]])
+                     # 打乱所有采样的索引
+                     indices = torch.tensor(indices)[torch.randperm(len(indices))]
+                     return iter(indices.tolist())
+                     
+                 def __len__(self):
+                     return len(self.indices)
+ 
+             # 使用平衡采样器创建DataLoader
+             balanced_sampler = BalancedBatchSampler(dataset_train, y_train)
+             train_dl = DataLoader(dataset_train, batch_sampler=balanced_sampler, num_workers=0, pin_memory=True,
                                  worker_init_fn=worker_init_fn)
  
```

---

我已经对代码进行了以下优化：

1. 添加了训练顺序分组常量`TRAINING_ORDER`，将subjects按样本数量分为4组：
   - large_subjects: >12个样本
   - medium_subjects: 7-12个样本
   - small_subjects: 3-6个样本
   - very_small_subjects: <=3个样本

2. 添加了辅助函数：
   - `get_ordered_subjects()`: 获取按样本数量排序的训练顺序
   - `balance_batch_samples()`: 实现批次样本平衡

3. 修改了主训练函数：
   - 使用分组训练顺序替代原来的随机顺序
   - 添加了组别信息的打印
   - 实现了`BalancedBatchSampler`来确保每个批次中各类别样本数量平衡

4. 优化了数据加载：
   - 使用自定义的`BalancedBatchSampler`替代原来的随机采样
   - 保证每个批次中各个类别的样本数量相等
   - 在每个epoch开始时重新打乱数据

这些修改的主要优点：

1. 训练顺序更合理：
   - 从样本量大的subjects开始训练，有助于模型建立基础特征
   - 逐步过渡到样本量小的subjects，避免过拟合
   - 相似样本量的subjects放在一起训练，使得学习过程更稳定

2. 样本平衡更好：
   - 每个批次中各类别样本数量平衡
   - 减少了类别不平衡对模型训练的影响
   - 提高了对少数类别的识别能力

3. 训练过程更透明：
   - 清晰显示当前训练的组别
   - 可以监控不同组别的训练效果
   - 便于分析和调试

建议在实验中关注以下几点：

1. 观察不同组别的训练效果
2. 监控各个类别的识别准确率变化
3. 对比优化前后的模型性能
4. 特别关注少样本类别的识别效果

这样的优化应该能够提高模型的整体性能，特别是在处理不平衡数据集时的表现。

---

