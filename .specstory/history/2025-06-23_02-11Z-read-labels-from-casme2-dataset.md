<!-- Generated by SpecStory -->

# Read labels from CASME2 dataset (2025-06-23 02:11Z)

_**User**_

/home/<USER>/data/ajq/SKD-TSTSAN-new/CASME2_LOSO_full

性代码读取所有sub、内的test内的标签
输出给我

真实标签: [4, 0, 0, 0, 0, 3, 3, 3, 3, 3]

---

_**Assistant**_

```thinking
让我理解一下用户的需求：
1. 需要读取CASME2_LOSO_full目录下所有sub_*目录中test子目录内的标签文件
2. 这是一个数据集目录结构,每个sub_*代表一个受试者的数据
3. 需要编写代码来遍历目录并读取标签信息

让我先看看目录结构:
```

---

<details>
            <summary>Listed directory /home/<USER>/data/ajq/SKD-TSTSAN-new/CASME2_LOSO_full • **26** results</summary>
        
| Name |
|-------|
| 📁 `sub26` |
| 📁 `sub25` |
| 📁 `sub24` |
| 📁 `sub23` |
| 📁 `sub22` |
| 📁 `sub21` |
| 📁 `sub20` |
| 📁 `sub19` |
| 📁 `sub18` |
| 📁 `sub17` |
| 📁 `sub16` |
| 📁 `sub15` |
| 📁 `sub14` |
| 📁 `sub13` |
| 📁 `sub12` |
| 📁 `sub11` |
| 📁 `sub10` |
| 📁 `sub09` |
| 📁 `sub08` |
| 📁 `sub07` |
| 📁 `sub06` |
| 📁 `sub05` |
| 📁 `sub04` |
| 📁 `sub03` |
| 📁 `sub02` |
| 📁 `sub01` |

</details>

---

```thinking
好的,我看到了目录结构,包含了sub01到sub26共26个受试者目录。
让我看看其中一个sub目录的结构,以确认test目录的位置:
```

---

<details>
            <summary>Listed directory /home/<USER>/data/ajq/SKD-TSTSAN-new/CASME2_LOSO_full/sub01 • **2** results</summary>
        
| Name |
|-------|
| 📁 `test` |
| 📁 `train` |

</details>

---

```thinking
很好,我看到每个sub目录下都有test和train目录。
让我看看test目录中的内容:
```

---

<details>
            <summary>Listed directory /home/<USER>/data/ajq/SKD-TSTSAN-new/CASME2_LOSO_full/sub01/test • **3** results</summary>
        
| Name |
|-------|
| 📁 `4` |
| 📁 `2` |
| 📁 `0` |

</details>

---

```thinking
我明白了,test目录下是按标签分类的子目录。
现在我可以编写代码来遍历所有sub目录并读取test目录下的标签信息。
我将创建一个Python脚本来完成这个任务。
```

---

```diff
- 
+ import os
+ import glob
+ 
+ def read_test_labels(base_path):
+     """
+     读取所有受试者test目录中的标签信息
+     Args:
+         base_path: CASME2_LOSO_full的根目录路径
+     Returns:
+         dict: 包含每个受试者test标签信息的字典
+     """
+     results = {}
+     
+     # 获取所有sub_*目录
+     sub_dirs = sorted(glob.glob(os.path.join(base_path, "sub*")))
+     
+     for sub_dir in sub_dirs:
+         sub_name = os.path.basename(sub_dir)
+         test_dir = os.path.join(sub_dir, "test")
+         
+         if not os.path.exists(test_dir):
+             print(f"警告: {sub_name} 中未找到test目录")
+             continue
+             
+         # 获取test目录下的所有标签目录
+         label_dirs = sorted(os.listdir(test_dir))
+         
+         # 存储当前受试者的标签信息
+         results[sub_name] = {
+             "labels": label_dirs,
+             "samples_per_label": {}
+         }
+         
+         # 统计每个标签下的样本数
+         for label in label_dirs:
+             label_path = os.path.join(test_dir, label)
+             if os.path.isdir(label_path):
+                 sample_count = len(os.listdir(label_path))
+                 results[sub_name]["samples_per_label"][label] = sample_count
+     
+     return results
+ 
+ if __name__ == "__main__":
+     # CASME2_LOSO_full的路径
+     base_path = "/home/<USER>/data/ajq/SKD-TSTSAN-new/CASME2_LOSO_full"
+     
+     # 读取所有标签信息
+     all_labels = read_test_labels(base_path)
+     
+     # 打印结果
+     print("\n=== CASME2 测试集标签统计 ===\n")
+     
+     for sub_name, info in all_labels.items():
+         print(f"\n{sub_name}:")
+         print("标签列表:", info["labels"])
+         print("每个标签的样本数:")
+         for label, count in info["samples_per_label"].items():
+             print(f"  - 标签 {label}: {count} 个样本") 
```

---

```thinking
现在我已经创建了脚本,让我运行它来获取所有受试者test目录中的标签信息:
```

---

Cancelled

---

_**User**_

@casme2真实标签.py 输出真实标签: [4, 0, 0, 0, 0, 3, 3, 3, 3, 3]这样子大概

---

_**Assistant**_

```thinking
我理解用户需要修改代码,让输出格式变成真实标签的列表形式。需要:
1. 修改代码以按顺序收集每个目录下的样本标签
2. 将输出格式改为[4, 0, 0, 0, 0, 3, 3, 3, 3, 3]这样的列表形式
3. 需要按照文件名顺序来确保标签顺序正确

让我修改代码:
```

---

```diff
  def read_test_labels(base_path):
      """
-     读取所有受试者test目录中的标签信息
+     读取所有受试者test目录中的标签信息,并按顺序输出标签列表
      Args:
          base_path: CASME2_LOSO_full的根目录路径
      Returns:
-         dict: 包含每个受试者test标签信息的字典
+         dict: 包含每个受试者标签列表的字典
      """
      results = {}
```

```diff
              continue
              
-         # 获取test目录下的所有标签目录
+         # 存储当前受试者的标签列表
+         labels = []
+         
+         # 遍历test目录下的所有标签目录
          label_dirs = sorted(os.listdir(test_dir))
-         
-         # 存储当前受试者的标签信息
-         results[sub_name] = {
-             "labels": label_dirs,
-             "samples_per_label": {}
-         }
-         
-         # 统计每个标签下的样本数
          for label in label_dirs:
              label_path = os.path.join(test_dir, label)
              if os.path.isdir(label_path):
-                 sample_count = len(os.listdir(label_path))
-                 results[sub_name]["samples_per_label"][label] = sample_count
+                 # 获取该标签下的所有样本文件
+                 samples = sorted(os.listdir(label_path))
+                 # 将标签添加到列表中,重复样本数量次
+                 labels.extend([int(label)] * len(samples))
+         
+         results[sub_name] = labels
      
      return results
```

```diff
      
      # 打印结果
-     print("\n=== CASME2 测试集标签统计 ===\n")
+     print("\n=== CASME2 测试集真实标签 ===\n")
      
-     for sub_name, info in all_labels.items():
-         print(f"\n{sub_name}:")
-         print("标签列表:", info["labels"])
-         print("每个标签的样本数:")
-         for label, count in info["samples_per_label"].items():
-             print(f"  - 标签 {label}: {count} 个样本") 
+     for sub_name, labels in all_labels.items():
+         print(f"{sub_name} 真实标签: {labels}") 
```

---

```thinking
现在让我运行修改后的代码来查看输出:
```

---

Cancelled

---

_**User**_

我只要测试数据
训练不读

---

_**Assistant**_

```thinking
明白了,用户只需要读取test目录下的标签数据,不需要读取train目录。我来修改代码:
```

---

```diff
  def read_test_labels(base_path):
      """
-     读取所有受试者test目录中的标签信息,并按顺序输出标签列表
+     只读取test目录下的标签数据
      Args:
          base_path: CASME2_LOSO_full的根目录路径
      Returns:
-         dict: 包含每个受试者标签列表的字典
+         list: 测试集标签列表
      """
-     results = {}
-     
-     # 获取所有sub_*目录
-     sub_dirs = sorted(glob.glob(os.path.join(base_path, "sub*")))
-     
-     for sub_dir in sub_dirs:
-         sub_name = os.path.basename(sub_dir)
-         test_dir = os.path.join(sub_dir, "test")
-         
+     # 获取test目录下的所有标签目录
+     test_dir = os.path.join(base_path, "test")
      if not os.path.exists(test_dir):
-             print(f"警告: {sub_name} 中未找到test目录")
-             continue
+         print(f"警告: 未找到test目录: {test_dir}")
+         return []
          
-         # 存储当前受试者的标签列表
+     # 存储标签列表
      labels = []
      
```

```diff
              labels.extend([int(label)] * len(samples))
      
-         results[sub_name] = labels
-     
-     return results
+     return labels
  
  if __name__ == "__main__":
      # CASME2_LOSO_full的路径
      base_path = "/home/<USER>/data/ajq/SKD-TSTSAN-new/CASME2_LOSO_full"
      
-     # 读取所有标签信息
-     all_labels = read_test_labels(base_path)
+     # 读取测试集标签
+     test_labels = read_test_labels(base_path)
      
      # 打印结果
-     print("\n=== CASME2 测试集真实标签 ===\n")
-     
-     for sub_name, labels in all_labels.items():
-         print(f"{sub_name} 真实标签: {labels}") 
+     print(f"\n真实标签: {test_labels}") 
```

---

```thinking
现在让我运行修改后的代码来获取测试集标签:
```

---

Cancelled

---



---

