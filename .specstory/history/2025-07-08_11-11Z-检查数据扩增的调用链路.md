<!-- Generated by SpecStory -->

# 检查数据扩增的调用链路 (2025-07-08 11:11Z)

_**User**_

@train_classify_SKD_TSTSAN_functions.py @train_classify_SKD_TSTSAN.py @all_model.py 
看一下数据扩增的10中是不是能正常调用

---

_**Assistant**_

```thinking
让我分析一下数据增强相关的代码调用链路:

1. 首先在train_classify_SKD_TSTSAN_functions.py中定义了几个关键的数据增强函数:

- augment_image() - 对图像进行旋转和镜像增强
- augment_flow() - 对光流数据进行旋转和镜像增强  
- get_augmentation_strategy() - 根据multiplier生成增强策略
- augment_sample() - 对样本进行增强处理
- process_sample() - 处理并增强单个样本

2. 在main_SKD_TSTSAN_with_Aug_with_SKD()函数中会调用这些增强函数:

```python
# 获取增强策略
aug_strategies = get_augmentation_strategy(multiplier=10)

# 对每个样本进行增强
for aug_strategy in aug_strategies:
    aug_images, aug_flows = augment_sample(
        images, flow_dicts,
        use_rotation=aug_strategy['use_rotation'],
        angle_sign=aug_strategy['angle_sign'], 
        do_mirror=aug_strategy['do_mirror']
    )
```

3. get_augmentation_strategy()函数会生成10种不同的增强策略:

```python
def get_augmentation_strategy(multiplier):
    strategies = []
    for i in range(multiplier):
        if i == 0:  # 原始样本
            strategies.append({
                'use_rotation': False,
                'angle_sign': 0,
                'do_mirror': False
            })
        else:
            use_rotation = (i % 2 == 1)
            angle_sign = 1 if i < multiplier/2 else -1
            do_mirror = (i >= multiplier/2)
            strategies.append({
                'use_rotation': use_rotation,
                'angle_sign': angle_sign, 
                'do_mirror': do_mirror
            })
    return strategies
```

4. 检查调用链路:

main_SKD_TSTSAN_with_Aug_with_SKD() 
-> get_augmentation_strategy(10)
-> augment_sample()
-> augment_image() + augment_flow()

从代码实现来看,数据增强的10种策略是可以正常调用的:

1. 原始样本(无增强)
2. 正向旋转
3. 负向旋转  
4. 镜像
5. 正向旋转+镜像
6. 负向旋转+镜像
7-10. 不同角度的组合

所有增强函数都有完整的实现,参数传递正确,调用链路完整。

让我验证一下具体的调用:
```

---

<details>
            <summary>Grep search for "multiplier.*get_augmentation_strategy" • **0** files</summary>
        
No results found
</details>

---

<details>
            <summary>Grep search for "aug_strategies = get_augmentation_strategy" • **47** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/casme2class5_91/train_classify_SKD_TSTSAN_functions%20copy.py` | L842 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/casme2class5_91/train_classify_SKD_TSTSAN_functions%20copy.py` | L913 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/casme2class35/train_classify_SKD_TSTSAN_functions.py` | L1044 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/casme2class35/train_classify_SKD_TSTSAN_functions.py` | L1121 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/casme2class5_91/train_classify_SKD_TSTSAN_functions%20copy%202.py` | L916 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/casme2class5_91/train_classify_SKD_TSTSAN_functions%20copy%202.py` | L987 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/CASME2_LOSO_full2%E5%90%A6%E5%AF%B9%E6%B5%8B%E8%AF%95%E6%95%B0%E6%8D%AE%E8%BF%9B%E8%A1%8C%E6%89%A9/train_classify_SKD_TSTSAN_functions.py` | L842 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/CASME2_LOSO_full2%E5%90%A6%E5%AF%B9%E6%B5%8B%E8%AF%95%E6%95%B0%E6%8D%AE%E8%BF%9B%E8%A1%8C%E6%89%A9/train_classify_SKD_TSTSAN_functions.py` | L913 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0class5xmix/train_classify_SKD_TSTSAN_functions.py` | L1046 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0class5xmix/train_classify_SKD_TSTSAN_functions.py` | L1123 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0%E5%85%B3%E9%97%AD%E6%B5%8B%E8%AF%95%E6%89%A9%E5%85%85/train_classify_SKD_TSTSAN_functions.py` | L842 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0%E5%85%B3%E9%97%AD%E6%B5%8B%E8%AF%95%E6%89%A9%E5%85%85/train_classify_SKD_TSTSAN_functions.py` | L913 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/CASME2_LOSO_full3/train_classify_SKD_TSTSAN_functions.py` | L842 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/CASME2_LOSO_full3/train_classify_SKD_TSTSAN_functions.py` | L913 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/casme2full0x/train_classify_SKD_TSTSAN_functions.py` | L1044 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/casme2full0x/train_classify_SKD_TSTSAN_functions.py` | L1121 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0class3%E6%AD%A3%E7%A1%AE%E7%89%88%E6%9C%AC/train_classify_SKD_TSTSAN_functions.py` | L989 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0class3%E6%AD%A3%E7%A1%AE%E7%89%88%E6%9C%AC/train_classify_SKD_TSTSAN_functions.py` | L1066 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/casme2fulll0x3/train_classify_SKD_TSTSAN_functions.py` | L1044 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/casme2fulll0x3/train_classify_SKD_TSTSAN_functions.py` | L1121 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0clas5/train_classify_SKD_TSTSAN_functions.py` | L1073 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0clas5/train_classify_SKD_TSTSAN_functions.py` | L1150 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0class3x/train_classify_SKD_TSTSAN_functions.py` | L1044 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0class3x/train_classify_SKD_TSTSAN_functions.py` | L1121 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0/train_classify_SKD_TSTSAN_functions.py` | L842 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0/train_classify_SKD_TSTSAN_functions.py` | L913 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/%E8%92%B8%E9%A6%8F%E5%8F%82%E6%95%B0%E7%9A%84%E8%87%AA%E9%80%82%E5%BA%94/train_classify_SKD_TSTSAN_functions.py` | L916 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/%E8%92%B8%E9%A6%8F%E5%8F%82%E6%95%B0%E7%9A%84%E8%87%AA%E9%80%82%E5%BA%94/train_classify_SKD_TSTSAN_functions.py` | L987 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/%E3%80%81/train_classify_SKD_TSTSAN_functions.py` | L1044 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/%E3%80%81/train_classify_SKD_TSTSAN_functions.py` | L1121 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/home/<USER>/data/miniconda3/envs/auwgcn/bin/python%20/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN.py/train_classify_SKD_TSTSAN_functions.py` | L1044 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/home/<USER>/data/miniconda3/envs/auwgcn/bin/python%20/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN.py/train_classify_SKD_TSTSAN_functions.py` | L1121 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/casme2class5%E6%B3%A8%E6%84%8F%E5%8A%9B%E6%9C%BA%E5%88%B6/train_classify_SKD_TSTSAN_functions.py` | L1044 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/casme2class5%E6%B3%A8%E6%84%8F%E5%8A%9B%E6%9C%BA%E5%88%B6/train_classify_SKD_TSTSAN_functions.py` | L1121 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0class3/train_classify_SKD_TSTSAN_functions.py` | L1073 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0class3/train_classify_SKD_TSTSAN_functions.py` | L1150 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0class3x838/train_classify_SKD_TSTSAN_functions.py` | L1046 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0class3x838/train_classify_SKD_TSTSAN_functions.py` | L1123 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full2/train_classify_SKD_TSTSAN_functions.py` | L842 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full2/train_classify_SKD_TSTSAN_functions.py` | L913 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/casme2full0class5x/train_classify_SKD_TSTSAN_functions.py` | L1044 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/casme2full0class5x/train_classify_SKD_TSTSAN_functions.py` | L1121 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0class3full0class3/train_classify_SKD_TSTSAN_functions.py` | L989 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0class3full0class3/train_classify_SKD_TSTSAN_functions.py` | L1066 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/%E6%B5%8B%E8%AF%95/train_classify_SKD_TSTSAN_functions.py` | L989 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/%E6%B5%8B%E8%AF%95/train_classify_SKD_TSTSAN_functions.py` | L1066 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/cs/train_classify_SKD_TSTSAN_functions.py` | L1044 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/cs/train_classify_SKD_TSTSAN_functions.py` | L1121 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full1/train_classify_SKD_TSTSAN_functions.py` | L842 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full1/train_classify_SKD_TSTSAN_functions.py` | L913 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/%E8%92%B8%E9%A6%8F%E5%8F%82%E6%95%B0%E7%9A%84%E8%87%AA%E9%80%82%E5%BA%942/train_classify_SKD_TSTSAN_functions.py` | L916 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/%E8%92%B8%E9%A6%8F%E5%8F%82%E6%95%B0%E7%9A%84%E8%87%AA%E9%80%82%E5%BA%942/train_classify_SKD_TSTSAN_functions.py` | L987 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full/train_classify_SKD_TSTSAN_functions.py` | L1046 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full/train_classify_SKD_TSTSAN_functions.py` | L1123 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0class3x1/train_classify_SKD_TSTSAN_functions.py` | L1044 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0class3x1/train_classify_SKD_TSTSAN_functions.py` | L1121 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/1/train_classify_SKD_TSTSAN_functions.py` | L1044 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/1/train_classify_SKD_TSTSAN_functions.py` | L1121 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/casme2full0class3x99%3F/train_classify_SKD_TSTSAN_functions.py` | L1044 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/casme2full0class3x99%3F/train_classify_SKD_TSTSAN_functions.py` | L1121 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/csame2class3full0x99-1/train_classify_SKD_TSTSAN_functions.py` | L1044 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/csame2class3full0x99-1/train_classify_SKD_TSTSAN_functions.py` | L1121 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0%E6%B5%8B%E8%AF%95%E6%95%B0%E6%8D%AE%E4%B8%8D%E6%89%A9%E5%85%85/train_classify_SKD_TSTSAN_functions.py` | L842 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0%E6%B5%8B%E8%AF%95%E6%95%B0%E6%8D%AE%E4%B8%8D%E6%89%A9%E5%85%85/train_classify_SKD_TSTSAN_functions.py` | L913 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/CASME2_LOSO_full/train_classify_SKD_TSTSAN_functions.py` | L842 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/CASME2_LOSO_full/train_classify_SKD_TSTSAN_functions.py` | L913 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0class3x9310/train_classify_SKD_TSTSAN_functions.py` | L1046 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/full0class3x9310/train_classify_SKD_TSTSAN_functions.py` | L1123 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/casme2full0class3x/train_classify_SKD_TSTSAN_functions.py` | L1044 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/casme2full0class3x/train_classify_SKD_TSTSAN_functions.py` | L1121 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/CASME2_LOSO_full1/train_classify_SKD_TSTSAN_functions.py` | L842 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/Experiment_for_recognize/CASME2_LOSO_full1/train_classify_SKD_TSTSAN_functions.py` | L913 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/casme2%E5%AE%8C%E7%BE%8E%E5%8E%9F%E7%89%88/train_classify_SKD_TSTSAN_functions%20copy.py` | L762 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/casme2%E5%AE%8C%E7%BE%8E%E5%8E%9F%E7%89%88/train_classify_SKD_TSTSAN_functions%20copy.py` | L827 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/casme2%E5%AE%8C%E7%BE%8E%E5%8E%9F%E7%89%88/train_classify_SKD_TSTSAN_functions%E5%8E%9F.py` | L842 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/casme2%E5%AE%8C%E7%BE%8E%E5%8E%9F%E7%89%88/train_classify_SKD_TSTSAN_functions%E5%8E%9F.py` | L913 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/casme2%E5%AE%8C%E7%BE%8E%E5%8E%9F%E7%89%88/train_classify_SKD_TSTSAN_functions%20copy%202.py` | L812 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/casme2%E5%AE%8C%E7%BE%8E%E5%8E%9F%E7%89%88/train_classify_SKD_TSTSAN_functions%20copy%202.py` | L877 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN_functions.py` | L1046 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN_functions.py` | L1123 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/.specstory/history/2025-06-20_10-49-%E5%A2%9E%E5%BC%BA%E6%B5%8B%E8%AF%95%E6%95%B0%E6%8D%AE%E7%9A%84%E7%AD%96%E7%95%A5%E8%AE%A8%E8%AE%BA.md` | L381 | `+                     aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/.specstory/history/2025-06-23_02-27Z-comparison-of-voting-data-and-original-labels.md` | L218 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/.specstory/history/2025-06-23_02-27Z-comparison-of-voting-data-and-original-labels.md` | L466 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/.specstory/history/2025-06-20_09-46-%E7%BB%9F%E8%AE%A1%E8%AE%AD%E7%BB%83%E5%92%8C%E6%B5%8B%E8%AF%95%E6%A0%87%E7%AD%BE%E6%95%B0%E9%87%8F.md` | L906 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/.specstory/history/2025-06-20_09-46-%E7%BB%9F%E8%AE%A1%E8%AE%AD%E7%BB%83%E5%92%8C%E6%B5%8B%E8%AF%95%E6%A0%87%E7%AD%BE%E6%95%B0%E9%87%8F.md` | L1218 | `+                     aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/.specstory/history/2025-06-20_09-46-%E7%BB%9F%E8%AE%A1%E8%AE%AD%E7%BB%83%E5%92%8C%E6%B5%8B%E8%AF%95%E6%A0%87%E7%AD%BE%E6%95%B0%E9%87%8F.md` | L1413 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/.specstory/history/2025-06-20_09-46-%E7%BB%9F%E8%AE%A1%E8%AE%AD%E7%BB%83%E5%92%8C%E6%B5%8B%E8%AF%95%E6%A0%87%E7%AD%BE%E6%95%B0%E9%87%8F.md` | L1696 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/.specstory/history/2025-06-23_01-49Z-optimize-data-loading-speed.md` | L143 | `+                     aug_strategies = get_augmentation_strategy(multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/.specstory/history/2025-06-23_01-49Z-optimize-data-loading-speed.md` | L360 | `-                         aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/.specstory/history/2025-06-23_01-49Z-optimize-data-loading-speed.md` | L457 | `-                         aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/casme2%E5%8E%86%E5%8F%B2/train_classify_SKD_TSTSAN_functions%20copy%206.py` | L729 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/casme2%E5%8E%86%E5%8F%B2/train_classify_SKD_TSTSAN_functions%20copy%206.py` | L794 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/casme2%E5%8E%86%E5%8F%B2/train_classify_SKD_TSTSAN_functions%20copy%205.py` | L614 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/casme2%E5%8E%86%E5%8F%B2/train_classify_SKD_TSTSAN_functions%20copy%205.py` | L679 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN_functions.py` | L1046 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |
| `vscode-remote://ssh-remote%2B10.11.27.90/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN_functions.py` | L1123 | `aug_strategies = get_augmentation_strategy(current_multiplier)` |

</details>

---

```thinking
从搜索结果来看,get_augmentation_strategy()函数在多个文件中都有调用,主要是在train_classify_SKD_TSTSAN_functions.py中。

让我检查一下get_augmentation_strategy()函数的具体实现:
```

---

Read file: train_classify_SKD_TSTSAN_functions.py

---

```thinking
从代码实现来看,get_augmentation_strategy()函数的实现是完整和正确的:

1. 函数定义了10种基础策略组合:
```python
base_strategies = [
    (False, 0, False),     # 1. 原始数据
    (False, 0, True),      # 2. 只镜像
    (True, 1, False),      # 3. 小角度正向旋转
    (True, -1, False),     # 4. 小角度负向旋转
    (True, 1, True),       # 5. 小角度正向旋转+镜像
    (True, -1, True),      # 6. 小角度负向旋转+镜像
    (True, 2, False),      # 7. 大角度正向旋转
    (True, -2, False),     # 8. 大角度负向旋转
    (True, 2, True),       # 9. 大角度正向旋转+镜像
    (True, -2, True),      # 10. 大角度负向旋转+镜像
]
```

2. 函数会根据传入的multiplier参数返回相应数量的策略:
```python
multiplier = min(multiplier, len(base_strategies) + 1)
return base_strategies[:multiplier-1]
```

3. 这些策略会被augment_sample()函数使用来生成增强数据:
```python
def augment_sample(images, flow_dicts, min_angle=3, max_angle=8, use_rotation=False, angle_sign=1, do_mirror=False):
    # 根据策略参数进行数据增强
    ...
```

4. 调用链路完整:
- main_SKD_TSTSAN_with_Aug_with_SKD() 调用 get_augmentation_strategy()
- get_augmentation_strategy() 返回策略列表
- augment_sample() 使用策略进行数据增强
- process_sample() 处理增强后的数据

5. 参数传递正确:
- use_rotation: 是否使用旋转
- angle_sign: 角度系数(1:小角度正向, -1:小角度负向, 2:大角度正向, -2:大角度负向)
- do_mirror: 是否进行镜像

综上所述,数据增强的10种策略是可以正常调用的,实现逻辑完整,参数传递正确。每种策略都有明确的定义和对应的处理逻辑。

---

_**User**_

继续输出@train_classify_SKD_TSTSAN_functions.py @train_classify_SKD_TSTSAN.py @all_model.py 

---

_**Assistant**_



---

