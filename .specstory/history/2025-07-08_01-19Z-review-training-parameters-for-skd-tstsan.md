<!-- Generated by SpecStory -->

# Review training parameters for SKD-TSTSAN (2025-07-08 01:19Z)

_**User**_

@train_classify_SKD_TSTSAN_functions copy 2.py @train_classify_SKD_TSTSAN copy 2.py 看一下
记住参数的设置


---

_**Assistant**_

```thinking
让我仔细分析这两个文件的参数设置:

1. 首先看主要的训练参数配置:
- learning_rate = 0.001
- batch_size = 32 
- seed = 1337
- max_iter = 20000
- class_num = 5 (CASME2数据集5类表情)

2. 知识蒸馏相关参数:
- temperature = 3 (蒸馏温度)
- alpha = 0.1 (蒸馏损失权重)
- beta = 1e-6 (特征损失权重)
- Aug_alpha = 2 (数据增强强度)

3. 数据增强参数:
- use_data_augmentation = True
- use_test_augmentation = True
- aug_rotation_range = '3,8' (旋转角度范围)
- aug_multipliers = '6,8,3,8,2' (各标签扩充倍数)
- aug_use_mirror = True (使用镜像)
- voting_method = 'weighted' (预测投票方式)

4. 模型相关:
- model = "SKD_TSTSAN"
- loss_function = "FocalLoss_weighted"
- pre_trained = True
- Aug_COCO_pre_trained = True
- save_model = True

5. 路径设置:
- pre_trained_model_path = "/home/<USER>/data/ajq/SKD-TSTSAN-new/Pretrained_model/SKD-TSTSAN.pth"
- main_path = "/home/<USER>/data/ajq/SKD-TSTSAN-new/CASME2_LOSO_full"

6. 可视化和通知:
- use_visdom = False
- email_notify = True
- email_sender = "<EMAIL>"

7. CUDNN设置:
- cudnn_benchmark = False
- cudnn_deterministic = True

这是一个面向CASME2数据集的微表情识别任务的完整训练配置。
```

我来总结一下这两个文件的主要内容和参数设置：

1. 训练框架设计：
- 采用知识蒸馏(SKD)和时空注意力(TSTSAN)的混合架构
- 实现了留一法(LOSO)交叉验证
- 支持数据增强和测试时增强

2. 数据集组织：
按照受试者样本量分为4组：
- 大样本组(>12个样本): sub17, sub05, sub26等
- 中等样本组(7-12个样本): sub23, sub12, sub20等  
- 小样本组(3-6个样本): sub24, sub25, sub03等
- 极小样本组(≤3个样本): sub08, sub15, sub16等

3. 关键训练参数：
- 学习率: 0.001
- 批次大小: 32
- 最大迭代: 20000次
- 分类数: 5类表情
- 使用FocalLoss带权重的损失函数

4. 知识蒸馏参数：
- 温度系数: T=3
- 蒸馏权重: α=0.1 
- 特征损失权重: β=1e-6
- 增强系数: Aug_alpha=2

5. 数据增强策略：
- 旋转角度范围: 3-8度
- 支持镜像增强
- 不同类别采用不同的增强倍数(6,8,3,8,2)
- 使用加权投票的测试时增强

这套参数配置主要针对CASME2数据集的微表情识别任务，通过知识蒸馏和数据增强来解决样本不平衡问题。参数设置体现了在保证模型性能的同时，也注重训练的稳定性和可重复性。

需要注意的是，代码中实现了自适应的知识蒸馏参数调整机制，会根据不同受试者的样本量动态调整温度、alpha和beta值，这是一个很好的设计。

---

_**User**_

@train_classify_SKD_TSTSAN_functions.py @train_classify_SKD_TSTSAN.py 有什么不一样的？

---

_**Assistant**_

让我分析一下这两个文件的主要区别：

1. 功能定位不同：
- `train_classify_SKD_TSTSAN_functions.py`：是核心功能实现文件，包含了所有训练相关的具体函数实现，如数据处理、模型训练、评估等
- `train_classify_SKD_TSTSAN.py`：是主程序入口文件，主要负责参数配置、结果展示和实验管理

2. 代码结构差异：

`train_classify_SKD_TSTSAN_functions.py` 包含：
- 数据集相关常量定义(CASME2样本数量、分组等)
- GPU设置和管理函数
- 数据增强和处理函数
- 损失函数实现
- 模型训练主循环
- 评估函数
- Visdom可视化服务

`train_classify_SKD_TSTSAN.py` 包含：
- 命令行参数解析
- 系统信息获取
- 结果格式化和展示
- 邮件通知功能
- 实验报告生成

3. 新增功能：
`train_classify_SKD_TSTSAN_functions.py` 新增了：
- 3分类支持(原来只支持5分类)
- 自适应参数调整
- 更完善的数据增强策略
- 更详细的评估指标

4. 参数设置：
- `train_classify_SKD_TSTSAN.py` 提供了完整的命令行参数配置
- `train_classify_SKD_TSTSAN_functions.py` 实现了这些参数的具体使用

5. 错误处理：
- `train_classify_SKD_TSTSAN_functions.py` 包含了详细的错误处理和日志记录
- `train_classify_SKD_TSTSAN.py` 提供了更高层次的错误报告和通知

6. 可视化和报告：
- `train_classify_SKD_TSTSAN.py` 负责生成人类可读的实验报告和可视化结果
- `train_classify_SKD_TSTSAN_functions.py` 负责生成原始数据和指标

总的来说，这是一个典型的功能分离设计：
- `train_classify_SKD_TSTSAN_functions.py` 是"引擎"，负责核心功能实现
- `train_classify_SKD_TSTSAN.py` 是"驾驶舱"，负责控制和展示

这种设计使得代码更容易维护和扩展，同时也方便其他程序复用核心功能。

---

_**User**_

@train_classify_SKD_TSTSAN_functions.py @train_classify_SKD_TSTSAN.py 看一下有什么区别吗

---

_**Assistant**_

让我详细对比一下这两个文件的具体区别：

1. 数据集处理的差异：
```python
# train_classify_SKD_TSTSAN_functions.py 新增了3分类支持
CASME2_5class_numbers = [32, 25, 63, 27, 99]  # 5分类样本数量
CASME2_3class_numbers = [32, 90, 25]  # 3分类样本数量 (正性、负性、惊讶)

# 新增了情绪标签映射
EMOTION_LABELS_5CLASS = {'快乐': 0, '惊讶': 1, '厌恶': 2, '压抑': 3, '其他': 4}
EMOTION_LABELS_3CLASS = {'正性': 0, '负性': 1, '惊讶': 2}

# 新增了5分类到3分类的映射
CLASS_5_TO_3_MAPPING = {
    0: 0,  # 快乐 -> 正性
    1: 2,  # 惊讶 -> 惊讶
    2: 1,  # 厌恶 -> 负性
    3: 1,  # 压抑 -> 负性
}
```

2. 参数配置的差异：
```python
# train_classify_SKD_TSTSAN.py 提供了更多命令行参数
parser.add_argument('--class_num', type=int, default=5, help='分类类别数')
parser.add_argument('--use_data_augmentation', type=strtobool, default=True)
parser.add_argument('--use_test_augmentation', type=strtobool, default=True)
parser.add_argument('--test_aug_multipliers', type=str, default='6,8,3,8,2')
parser.add_argument('--voting_method', type=str, default='weighted')
```

3. 训练流程的差异：
```python
# train_classify_SKD_TSTSAN_functions.py 新增了自适应参数
def get_subject_parameters(subject_id, class_num=5):
    """根据分类数和受试者ID获取相关参数"""
    if class_num == 3:
        if subject_id in LARGE_SUBJECTS_3CLASS:
            return 0.8, 0.8, 0.8  # 大样本组
        elif subject_id in MEDIUM_SUBJECTS_3CLASS:
            return 1.0, 1.0, 1.0  # 中等样本组
    # ...

# 新增了动态参数调整
def get_subject_temperature(subject_id, class_num=5):
    base_temp = 3.0
    temp_adjust, _, _ = get_subject_parameters(subject_id, class_num)
    return base_temp * temp_adjust
```

4. 评估方法的差异：
```python
# train_classify_SKD_TSTSAN_functions.py 增强了评估功能
def recognition_evaluation(dataset, final_gt, final_pred, show=False, class_num=5):
    # 3分类模式下跳过标签4
    if class_num == 3:
        valid_indices = [i for i, label in enumerate(final_gt) if label != 4]
        if not valid_indices:
            print("警告: 没有有效的样本用于3分类评估")
            return 0.0, 0.0
```

5. 结果展示的差异：
```python
# train_classify_SKD_TSTSAN.py 新增了更详细的结果展示
def format_subject_results(results_dict):
    brief_results = "\n【各受试者评估结果】\n"
    detailed_results = "\n【各受试者详细预测结果】\n"
    # ...

def send_training_results(config, results_dict, total_time):
    """发送训练结果邮件并在终端显示内容"""
```

6. 错误处理的差异：
```python
# train_classify_SKD_TSTSAN_functions.py 增加了更多错误处理
try:
    # 检查输入数据有效性
    if not final_gt or not final_pred:
        print("错误: 真实标签或预测标签为空")
        return 0.0, 0.0
except Exception as e:
    print(f"评估过程出错: {str(e)}")
    return 0.0, 0.0
```

7. 可视化支持的差异：
```python
# train_classify_SKD_TSTSAN_functions.py 提供了更完善的Visdom支持
class VisdomServer:
    def __init__(self, port=8097, timeout=60, open_browser=True):
        self.port = port
        self.process = None
        # ...
```

主要改进点：
1. 增加了3分类支持
2. 增强了数据增强和处理能力
3. 添加了自适应参数调整
4. 改进了评估方法
5. 增强了错误处理
6. 提供了更详细的结果展示
7. 改进了可视化支持

总体来看，`train_classify_SKD_TSTSAN_functions.py` 是核心功能实现，而 `train_classify_SKD_TSTSAN.py` 则负责参数配置和结果展示，两者形成了一个完整的训练系统。新版本增加了更多功能和更好的用户体验。

---

