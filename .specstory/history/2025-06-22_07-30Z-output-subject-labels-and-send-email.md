<!-- Generated by SpecStory -->

# Output subject labels and send email (2025-06-22 07:30Z)

_**User**_

@train_classify_SKD_TSTSAN_functions.py @train_classify_SKD_TSTSAN.py 最后输出 每个sub的真实标签与最佳预测标签并且发送邮件的内容打印到终端



---

_**Assistant**_

```thinking
让我分析一下这个需求:

1. 需要找到代码中输出每个subject预测结果和发送邮件的相关部分:
- 在train_classify_SKD_TSTSAN_functions.py中的main_SKD_TSTSAN_with_Aug_with_SKD函数中有记录每个subject的结果
- 在train_classify_SKD_TSTSAN.py中的send_training_results函数负责发送邮件和打印结果

2. 关键部分:
- subject_results字典存储了每个subject的预测和真实标签
- format_subject_results函数格式化这些结果
- send_training_results函数将结果发送邮件并打印到终端

3. 需要查看:
- 结果格式化的具体实现
- 邮件内容的组织方式
- 终端输出的格式

4. 重点关注:
- 每个subject的预测标签和真实标签对比
- 整体性能指标(UF1, UAR等)
- 邮件通知的完整内容

让我查看相关代码实现。
```

这个功能已经在代码中实现了。具体来说,在`train_classify_SKD_TSTSAN.py`中的`send_training_results`函数会将训练结果格式化并打印到终端。主要输出包括:

1. 基本实验信息(实验名称、时间、环境等)
2. 模型配置信息
3. 训练参数
4. 总体性能指标
5. 每个受试者的详细结果

输出示例如下:

```
================================================================================
【实验结果报告】
================================================================================

【基本信息】
实验名称: [实验名称]
时间: [当前时间]
总训练时间: [训练用时]
数据集: [数据集名称]

【系统环境】
操作系统: [系统信息]
处理器: [CPU信息]
内存: [内存信息]
GPU: [GPU信息]

【模型配置】
模型架构: SKD_TSTSAN
是否使用预训练: [是/否]
预训练模型: [模型名称]
是否使用COCO预训练: [是/否]
是否保存模型: [是/否]

【训练参数】
学习率: [学习率]
批次大小: [批次大小]
最大迭代次数: [迭代次数]
损失函数: [损失函数名称]
随机种子: [随机种子]

【蒸馏参数】
温度: [温度值]
α值: [alpha值]
β值: [beta值]
数据增强系数: [增强系数]

【训练结果】
总体性能:
- UF1分数: [UF1值]
- UAR分数: [UAR值]

【各表情类别准确率】
--------------------------------------------------
- 快乐: [准确率]
- 惊讶: [准确率]
- 厌恶: [准确率]
- 压抑: [准确率]
- 其他: [准确率]

【各受试者评估结果】
--------------------------------------------------
受试者 sub01: UF1=x.xxxx, UAR=x.xxxx
受试者 sub02: UF1=x.xxxx, UAR=x.xxxx
...

详细统计:
受试者      UF1        UAR        准确率
--------------------------------------------------
sub01     x.xxxx     x.xxxx     x.xxxx
sub02     x.xxxx     x.xxxx     x.xxxx
...
--------------------------------------------------
平均      x.xxxx     x.xxxx     x.xxxx

【各受试者详细预测结果】
==================================================

受试者 sub01:
性能指标: UF1=x.xxxx, UAR=x.xxxx

类别统计:
- 快乐: x.xxxx (正确数/总数)
- 惊讶: x.xxxx (正确数/总数)
- 厌恶: x.xxxx (正确数/总数)
- 压抑: x.xxxx (正确数/总数)
- 其他: x.xxxx (正确数/总数)

标签对比:
预测标签: [预测标签序列]
真实标签: [真实标签序列]
--------------------------------------------------

[其他受试者的详细结果...]

================================================================================
```

这个输出会同时显示在终端并通过邮件发送。每个受试者的结果都包含:
1. UF1和UAR性能指标
2. 各个表情类别的准确率统计
3. 预测标签和真实标签的完整序列对比

这样可以全面了解每个受试者的识别效果,并且方便进行错误分析和模型改进。

```thinking
这个实现已经很完整了,主要通过以下步骤完成:

1. 在训练过程中收集每个subject的结果
2. 使用format_subject_results函数格式化结果
3. 通过send_training_results函数输出到终端和邮件

不需要额外修改代码,现有实现已经满足需求。建议用户直接运行代码,就可以看到详细的结果输出。
```

---

_**User**_

修改代码
实现 完成训练 后最后输出 每个sub的真实标签与最佳预测标签并且发送邮件的内容打印到终端


---

_**Assistant**_

```thinking
我需要修改代码来实现在训练完成后输出每个subject的标签对比和邮件内容。

主要修改点:
1. 在main_SKD_TSTSAN_with_Aug_with_SKD函数的最后添加结果输出
2. 确保send_training_results函数在训练完成后被调用
3. 添加一个清晰的分隔来展示最终结果

我将修改train_classify_SKD_TSTSAN.py文件。
```

我将修改代码来实现这个功能。首先我来查看并编辑train_classify_SKD_TSTSAN.py文件:

---

Read file: train_classify_SKD_TSTSAN.py

---

我将修改代码来在训练完成后添加结果输出。让我编辑train_classify_SKD_TSTSAN.py文件:

---

```diff
          print(f"\n【错误】邮件处理过程出错: {str(e)}")
  
+ def print_final_results(results_dict):
+     """
+     打印每个受试者的最终预测结果
+     """
+     print("\n" + "="*80)
+     print("【训练最终结果】")
+     print("="*80)
+     
+     if not results_dict or 'subject_results' not in results_dict:
+         print("没有可用的训练结果")
+         return
+         
+     print("\n【各受试者预测结果】")
+     print("-"*50)
+     
+     for subject_id, data in sorted(results_dict['subject_results'].items()):
+         print(f"\n受试者 {subject_id}:")
+         print(f"性能指标: UF1={data['UF1']:.4f}, UAR={data['UAR']:.4f}")
+         print("标签对比:")
+         print(f"预测标签: {data['pred']}")
+         print(f"真实标签: {data['truth']}")
+         print("-"*50)
+     
+     if 'UF1' in results_dict and 'UAR' in results_dict:
+         print(f"\n【总体性能】")
+         print(f"UF1: {results_dict['UF1']:.4f}")
+         print(f"UAR: {results_dict['UAR']:.4f}")
+     
+     if 'emotion_acc' in results_dict:
+         print("\n【各表情类别准确率】")
+         for emotion, acc in results_dict['emotion_acc'].items():
+             print(f"- {emotion}: {acc:.4f}")
+     
+     print("\n" + "="*80)
  
  if __name__ == '__main__':
```

```diff
          results_dict = main_SKD_TSTSAN_with_Aug_with_SKD(config)
          total_time = time.time() - start_time
+         
+         # 打印最终结果
+         print_final_results(results_dict)
          
          # 如果启用了邮件通知，发送训练结果
```

---

我已经修改了代码,主要做了以下更改:

1. 添加了新的`print_final_results`函数,用于打印训练的最终结果,包括:
   - 每个受试者的UF1和UAR指标
   - 每个受试者的预测标签和真实标签对比
   - 总体性能指标
   - 各表情类别的准确率

2. 在主函数中,在训练完成后立即调用`print_final_results`函数来显示结果,然后再发送邮件通知。

现在当训练完成后,终端会显示:
1. 详细的训练结果,包括每个受试者的预测情况
2. 邮件通知的内容(如果启用了邮件通知)

输出格式示例:
```
================================================================================
【训练最终结果】
================================================================================

【各受试者预测结果】
--------------------------------------------------

受试者 sub01:
性能指标: UF1=0.xxxx, UAR=0.xxxx
标签对比:
预测标签: [0, 1, 2, ...]
真实标签: [0, 1, 2, ...]
--------------------------------------------------

[其他受试者的结果...]

【总体性能】
UF1: 0.xxxx
UAR: 0.xxxx

【各表情类别准确率】
- 快乐: 0.xxxx
- 惊讶: 0.xxxx
- 厌恶: 0.xxxx
- 压抑: 0.xxxx
- 其他: 0.xxxx

================================================================================
```

这样可以清晰地看到每个受试者的预测结果以及整体性能。如果您想运行代码查看结果,直接执行修改后的脚本即可。

---

_**User**_

训练完 为什么 没有输出


---

_**Assistant**_

```thinking
我看到问题了:

1. 从日志输出来看,训练确实完成了,但是没有看到我们刚才添加的最终结果输出。

2. 可能的原因:
- 训练过程中提前结束了(达到了完美准确率)
- sys.stdout.close() 在输出结果前就被调用了
- 日志记录器的问题

3. 需要修改:
- 调整Logger类的实现,确保所有输出都能正确显示
- 修改finally块的位置,让它在所有输出完成后再关闭日志
- 确保print_final_results在日志关闭前被调用

让我修改代码来解决这个问题。
```

让我修改train_classify_SKD_TSTSAN.py文件:

---

```diff
      # 启动训练
      print("开始训练...")
-     try:
      start_time = time.time()
+     results_dict = None
+     logger = None
+     
+     try:
+         # 设置日志文件
+         log_file_path = './Experiment_for_recognize/' + config.exp_name + "/log.txt"
+         logger = Logger(log_file_path)
+         sys.stdout = logger
+         
          results_dict = main_SKD_TSTSAN_with_Aug_with_SKD(config)
          total_time = time.time() - start_time
          
          # 打印最终结果
-         print_final_results(results_dict)
+         print("\n" + "="*80)
+         print("【训练最终结果】")
+         print("="*80)
+         
+         if results_dict and 'subject_results' in results_dict:
+             print("\n【各受试者预测结果】")
+             print("-"*50)
+             
+             for subject_id, data in sorted(results_dict['subject_results'].items()):
+                 print(f"\n受试者 {subject_id}:")
+                 print(f"性能指标: UF1={data['UF1']:.4f}, UAR={data['UAR']:.4f}")
+                 print("标签对比:")
+                 print(f"预测标签: {data['pred']}")
+                 print(f"真实标签: {data['truth']}")
+                 print("-"*50)
+             
+             if 'UF1' in results_dict and 'UAR' in results_dict:
+                 print(f"\n【总体性能】")
+                 print(f"UF1: {results_dict['UF1']:.4f}")
+                 print(f"UAR: {results_dict['UAR']:.4f}")
+             
+             if 'emotion_acc' in results_dict:
+                 print("\n【各表情类别准确率】")
+                 for emotion, acc in results_dict['emotion_acc'].items():
+                     print(f"- {emotion}: {acc:.4f}")
+             
+             print("\n" + "="*80)
          
          # 如果启用了邮件通知，发送训练结果
          if config.email_notify and config.email_sender and config.email_receiver:
              send_training_results(config, results_dict, total_time)
              
-         print("训练完成!")
+         print("\n训练完成!")
+         
      except Exception as e:
-         print(f"训练过程出错: {str(e)}")
+         print(f"\n训练过程出错: {str(e)}")
          # 如果启用了邮件通知，发送错误信息
          if config.email_notify and config.email_sender and config.email_receiver:
```

```diff
      finally:
          # 确保日志文件被正确关闭
-         if hasattr(sys.stdout, 'close'):
-             sys.stdout.close()
+         if logger:
+             sys.stdout = sys.__stdout__  # 恢复标准输出
+             logger.close()
  
  
```

---

我已经修改了代码,主要做了以下更改:

1. 重构了日志记录和输出的流程:
   - 将Logger的初始化移到了try块的开始
   - 保存了Logger实例以便在finally中正确关闭
   - 在关闭日志前确保所有输出都完成

2. 直接在主流程中实现了结果输出,而不是通过单独的函数:
   - 确保结果输出在日志关闭前完成
   - 添加了更多的格式化和分隔符,使输出更清晰

3. 改进了错误处理:
   - 在finally块中正确恢复标准输出
   - 确保日志文件被正确关闭

4. 优化了输出格式:
   - 添加了更多的分隔符和空行
   - 使结果显示更加清晰和有结构

现在当你运行代码时,应该能看到完整的输出结果,包括:
1. 训练过程的日志
2. 每个受试者的详细预测结果
3. 总体性能指标
4. 各表情类别的准确率

输出会同时显示在终端和保存在日志文件中。你可以再次运行代码来验证输出是否正确显示。

---

_**User**_

现在邮件发送了 最后终端输出没有 我需要输出每个sub的真实标签与最佳预测标签并且发送邮件的内容打印到终端


---

_**Assistant**_

```thinking
我理解问题了:
1. 邮件发送成功了,但终端没有显示预期的输出
2. 需要确保邮件内容也打印到终端
3. 问题可能在于sys.stdout的切换时机

解决方案:
1. 修改send_training_results函数,让它同时打印到终端
2. 在恢复标准输出前确保所有内容都已打印
3. 调整输出顺序,确保结果在日志关闭前完整显示
```

让我修改代码:

---

```diff
          total_time = time.time() - start_time
          
-         # 打印最终结果
+         # 生成并打印实验报告
+         sys_info = get_system_info()
+         brief_results, detailed_results = format_subject_results(results_dict)
+         current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
+         
          print("\n" + "="*80)
-         print("【训练最终结果】")
+         print("【实验结果报告】")
          print("="*80)
          
-         if results_dict and 'subject_results' in results_dict:
-             print("\n【各受试者预测结果】")
-             print("-"*50)
-             
-             for subject_id, data in sorted(results_dict['subject_results'].items()):
-                 print(f"\n受试者 {subject_id}:")
-                 print(f"性能指标: UF1={data['UF1']:.4f}, UAR={data['UAR']:.4f}")
-                 print("标签对比:")
-                 print(f"预测标签: {data['pred']}")
-                 print(f"真实标签: {data['truth']}")
-                 print("-"*50)
-             
+         print("\n【基本信息】")
+         print(f"实验名称: {config.exp_name}")
+         print(f"时间: {current_time}")
+         print(f"总训练时间: {format_time(total_time)}")
+         print(f"数据集: {os.path.basename(config.main_path)}")
+         
+         print("\n【系统环境】")
+         print(f"操作系统: {sys_info['OS']}")
+         print(f"处理器: {sys_info['CPU']}")
+         print(f"内存: {sys_info['内存']}")
+         print(f"GPU: {sys_info['GPU']}")
+         
+         print("\n【模型配置】")
+         print(f"模型架构: {config.model}")
+         print(f"是否使用预训练: {'是' if config.pre_trained else '否'}")
+         print(f"预训练模型: {os.path.basename(config.pre_trained_model_path) if config.pre_trained else '无'}")
+         print(f"是否使用COCO预训练: {'是' if config.Aug_COCO_pre_trained else '否'}")
+         print(f"是否保存模型: {'是' if config.save_model else '否'}")
+         
+         print("\n【训练参数】")
+         print(f"学习率: {config.learning_rate}")
+         print(f"批次大小: {config.batch_size}")
+         print(f"最大迭代次数: {config.max_iter}")
+         print(f"损失函数: {config.loss_function}")
+         print(f"随机种子: {config.seed}")
+         
+         print("\n【蒸馏参数】")
+         print(f"温度: {config.temperature}")
+         print(f"α值: {config.alpha}")
+         print(f"β值: {config.beta}")
+         print(f"数据增强系数: {config.Aug_alpha}")
+         
+         print("\n【训练结果】")
          if 'UF1' in results_dict and 'UAR' in results_dict:
-                 print(f"\n【总体性能】")
-                 print(f"UF1: {results_dict['UF1']:.4f}")
-                 print(f"UAR: {results_dict['UAR']:.4f}")
+             print(f"总体性能:")
+             print(f"- UF1分数: {format_metric(results_dict['UF1'])}")
+             print(f"- UAR分数: {format_metric(results_dict['UAR'])}")
          
          if 'emotion_acc' in results_dict:
              print("\n【各表情类别准确率】")
+             print("-"*50)
              for emotion, acc in results_dict['emotion_acc'].items():
-                     print(f"- {emotion}: {acc:.4f}")
+                 print(f"- {emotion}: {format_metric(acc)}")
+         
+         # 打印每个受试者的结果
+         print(brief_results)
+         print(detailed_results)
          
-             print("\n" + "="*80)
+         print("="*80)
          
          # 如果启用了邮件通知，发送训练结果
```

---

我已经修改了代码,主要做了以下改进:

1. 重构了结果输出流程:
   - 使用format_subject_results函数生成详细的结果报告
   - 按照邮件格式组织输出内容
   - 确保所有内容在日志关闭前打印

2. 输出内容包括:
   - 基本信息(实验名称、时间、训练时长等)
   - 系统环境信息
   - 模型配置
   - 训练参数
   - 蒸馏参数
   - 训练结果
   - 每个受试者的详细预测结果
   - 总体性能指标
   - 各表情类别准确率

3. 格式优化:
   - 添加了清晰的分节标题
   - 使用分隔线增加可读性
   - 统一了数值的格式化显示

现在当你运行代码时,应该能在终端看到完整的实验报告,包括:
```
================================================================================
【实验结果报告】
================================================================================

【基本信息】
实验名称: xxx
时间: xxx
总训练时间: xx:xx:xx
数据集: xxx

【系统环境】
...

【模型配置】
...

【训练参数】
...

【蒸馏参数】
...

【训练结果】
总体性能:
- UF1分数: x.xxxx
- UAR分数: x.xxxx

【各表情类别准确率】
--------------------------------------------------
- 快乐: x.xxxx
- 惊讶: x.xxxx
...

【各受试者评估结果】
--------------------------------------------------
受试者 xxx:
性能指标: UF1=x.xxxx, UAR=x.xxxx
标签对比:
预测标签: [x, x, x, ...]
真实标签: [x, x, x, ...]
--------------------------------------------------
...

================================================================================
```

这样可以清晰地看到所有训练结果和每个受试者的预测情况。你可以再次运行代码来验证输出是否符合预期。

---

