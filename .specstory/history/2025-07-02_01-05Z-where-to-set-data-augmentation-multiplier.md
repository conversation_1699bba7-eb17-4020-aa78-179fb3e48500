<!-- Generated by SpecStory -->

# Where to set data augmentation multiplier (2025-07-02 01:05Z)

_**User**_

@train_classify_SKD_TSTSAN_functions.py @train_classify_SKD_TSTSAN_functions.py 扩充数据倍数在哪里？

---

_**Assistant**_

```thinking
让我分析一下数据扩充倍数的相关代码。

首先需要在代码中搜索与数据扩充相关的关键部分。从代码结构来看,数据扩充应该在加载训练和测试数据的部分。

我应该重点关注:
1. 配置参数中的扩充倍数设置
2. 实际执行数据扩充的代码位置
3. 如何根据不同类别设置不同的扩充倍数

让我先搜索相关代码...
```

---

<details>
            <summary>Searched codebase "multiplier aug_multipliers" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `train_classify_SKD_TSTSAN_functions.py` | L595-622 |
| `Experiment_for_recognize/full0clas5/train_classify_SKD_TSTSAN_functions.py` | L595-622 |
| `Experiment_for_recognize/full0class3x/train_classify_SKD_TSTSAN_functions.py` | L595-622 |
| `Experiment_for_recognize/full0class3x1/train_classify_SKD_TSTSAN_functions.py` | L595-622 |
| `Experiment_for_recognize/full0class3/train_classify_SKD_TSTSAN_functions.py` | L595-622 |
| `train_classify_SKD_TSTSAN_functions.py` | L1040-1073 |
| `Experiment_for_recognize/full0clas5/train_classify_SKD_TSTSAN_functions.py` | L1069-1102 |
| `Experiment_for_recognize/full0class3x1/train_classify_SKD_TSTSAN_functions.py` | L1040-1073 |
| `Experiment_for_recognize/full0class3x/train_classify_SKD_TSTSAN_functions.py` | L1040-1073 |
| `Experiment_for_recognize/full0class3/train_classify_SKD_TSTSAN_functions.py` | L1069-1102 |
| `train_classify_SKD_TSTSAN_functions.py` | L1073-1094 |
| `Experiment_for_recognize/full0clas5/train_classify_SKD_TSTSAN_functions.py` | L1028-1049 |
| `train_classify_SKD_TSTSAN_functions.py` | L993-1020 |
| `Experiment_for_recognize/full0clas5/train_classify_SKD_TSTSAN_functions.py` | L1102-1123 |
| `Experiment_for_recognize/full0class3x1/train_classify_SKD_TSTSAN_functions.py` | L1073-1094 |
| `Experiment_for_recognize/full0class3/train_classify_SKD_TSTSAN_functions.py` | L1028-1049 |
| `Experiment_for_recognize/full0class3/train_classify_SKD_TSTSAN_functions.py` | L1102-1123 |
| `Experiment_for_recognize/full0class3x/train_classify_SKD_TSTSAN_functions.py` | L1073-1094 |
| `train_classify_SKD_TSTSAN_functions.py` | L1117-1154 |
| `Experiment_for_recognize/full0class3x1/train_classify_SKD_TSTSAN_functions.py` | L993-1020 |
| `Experiment_for_recognize/full0class3x/train_classify_SKD_TSTSAN_functions.py` | L993-1020 |
| `Experiment_for_recognize/full0clas5/train_classify_SKD_TSTSAN_functions.py` | L1146-1183 |
| `Experiment_for_recognize/full0class3x/train_classify_SKD_TSTSAN_functions.py` | L1117-1154 |
| `Experiment_for_recognize/full0class3x1/train_classify_SKD_TSTSAN_functions.py` | L1117-1154 |
| `Experiment_for_recognize/full0class3/train_classify_SKD_TSTSAN_functions.py` | L1146-1183 |

</details>

---

```thinking
根据代码分析，数据扩充倍数的设置和使用主要体现在以下几个方面：

1. 配置参数中的扩充倍数设置：
- config.aug_multipliers: 训练数据的扩充倍数，以逗号分隔的字符串形式指定每个类别的扩充倍数
- config.test_aug_multipliers: 测试数据的扩充倍数，同样以逗号分隔的字符串形式指定

2. 扩充倍数的使用：
- 训练数据扩充：
```python
if config.use_data_augmentation:
    multipliers = [int(x) for x in config.aug_multipliers.split(',')]
    current_multiplier = multipliers[label]
else:
    current_multiplier = 1
```

- 测试数据扩充：
```python
if config.use_test_augmentation:
    multipliers = [int(x) for x in config.test_aug_multipliers.split(',')]
    current_multiplier = multipliers[label]
else:
    current_multiplier = 1
```

3. 扩充策略的实现：
通过get_augmentation_strategy()函数实现，最大支持8倍扩增，包含以下策略：
1. 原始数据
2. 只镜像
3. 小角度正向旋转
4. 小角度负向旋转
5. 小角度正向旋转+镜像
6. 小角度负向旋转+镜像
7. 大角度正向旋转
8. 大角度负向旋转

让我总结一下回答：
```

数据扩充倍数的设置主要在配置文件中通过两个参数控制：

1. `config.aug_multipliers`：训练数据的扩充倍数
- 格式为逗号分隔的字符串，如"2,3,4,2,1"
- 每个数字对应一个类别的扩充倍数
- 例如对于5分类，"2,3,4,2,1"表示第1类扩2倍，第2类扩3倍，第3类扩4倍，第4类扩2倍，第5类扩1倍

2. `config.test_aug_multipliers`：测试数据的扩充倍数
- 格式与训练数据相同
- 可以为测试数据设置不同的扩充倍数

扩充的具体实现支持最多8种不同的扩充策略，包括镜像、旋转等组合。当指定某个类别的扩充倍数为N时，会从这些策略中选择N-1种进行扩充（因为原始数据算1倍）。

扩充是否启用由`config.use_data_augmentation`和`config.use_test_augmentation`两个开关控制。如果关闭，则所有类别的扩充倍数都为1，即不进行扩充。

---

_**User**_

设置倍数的代码在哪

---

_**Assistant**_

```thinking
用户想知道在哪里设置具体的扩充倍数。从之前的分析可以看出，扩充倍数是通过config参数传入的，所以我需要找到config的定义或者使用的地方。

我应该搜索：
1. config的定义文件
2. aug_multipliers和test_aug_multipliers的设置位置
3. 调用main_SKD_TSTSAN_with_Aug_with_SKD函数的地方
```

---

<details>
            <summary>Searched codebase "train_classify_SKD_TSTSAN" • **10** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

Read file: /home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN.py

---

Read file: /home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN.py

---

Read file: /home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN.py

---

根据代码分析，数据扩充倍数的设置在主训练文件`train_classify_SKD_TSTSAN.py`中通过命令行参数配置：

1. 对于5分类情况：
```python
parser.add_argument('--aug_multipliers', type=str, default='6,8,3,8,2', help='训练数据各标签扩充倍数(逗号分隔)')
parser.add_argument('--test_aug_multipliers', type=str, default='6,8,3,8,2', help='测试数据各标签扩充倍数(逗号分隔)')
```

2. 对于3分类情况：
```python
parser.set_defaults(aug_multipliers="8,2,6")  # 修改为3分类的数据增强倍数
parser.set_defaults(test_aug_multipliers="8,2,6")
```

具体来说：

5分类模式下的扩充倍数：
- 快乐类：6倍
- 惊讶类：8倍
- 厌恶类：3倍
- 压抑类：8倍
- 其他类：2倍

3分类模式下的扩充倍数：
- 正性类：8倍
- 负性类：2倍
- 惊讶类：6倍

这些倍数可以在运行程序时通过命令行参数修改，例如：
```bash
python train_classify_SKD_TSTSAN.py --aug_multipliers "4,6,2,6,1"
```

或者在程序运行时，通过选择分类方案（3分类或5分类）自动设置相应的默认值。

---

