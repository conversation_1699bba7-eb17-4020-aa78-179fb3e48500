<!-- Generated by SpecStory -->

# Fixing type error in evaluation function (2025-06-20 00:44:49)

_**User**_

@train_classify_SKD_TSTSAN.py @train_classify_SKD_TSTSAN_functions.py 

为什么出现

---

_**Assistant**_

```thinking
让我分析一下这个错误:

1. 错误出现在 train_classify_SKD_TSTSAN_functions.py 的第521行:
print('UF1:', round(UF1, 4), '| UAR:', round(UAR, 4))

2. 错误信息是:
TypeError: type str doesn't define __round__ method

3. 追踪问题根源:
- UF1和UAR是从recognition_evaluation()函数返回的
- 查看recognition_evaluation()函数的实现:
```python
def recognition_evaluation(dataset, final_gt, final_pred, show=False):
    try:
        # ... 计算逻辑 ...
        return UF1, UAR
    except:
        return '', '' # 这里返回了空字符串!
```

4. 问题原因:
- recognition_evaluation()函数在出现异常时返回两个空字符串
- 然后代码尝试对这些字符串调用round()函数
- 但字符串类型不支持round()操作,所以报错

5. 解决方案:
需要在打印前检查UF1和UAR是否为字符串,如果是字符串则不进行round操作
```

根据分析,这个错误是因为`recognition_evaluation()`函数在出现异常时返回了空字符串,而代码又试图对这些字符串进行round()操作导致的。

建议修改代码如下:

```python
# 在train_classify_SKD_TSTSAN_functions.py中
# 将第521行附近的代码修改为:

UF1, UAR = recognition_evaluation(config.main_path.split("/")[1].split("_")[0], total_gt, total_pred, show=True)
best_UF1, best_UAR = recognition_evaluation(config.main_path.split("/")[1].split("_")[0], total_gt, best_total_pred, show=True)

# 添加类型检查
if isinstance(UF1, (int, float)) and isinstance(UAR, (int, float)):
    print('UF1:', round(UF1, 4), '| UAR:', round(UAR, 4))
    print('best UF1:', round(best_UF1, 4), '| best UAR:', round(best_UAR, 4))
else:
    print('UF1:', UF1, '| UAR:', UAR)
    print('best UF1:', best_UF1, '| best UAR:', best_UAR)
```

这样修改后:
1. 会先检查UF1和UAR的类型
2. 如果是数值类型(int或float)则进行round操作
3. 如果是字符串(说明发生了异常)则直接打印
4. 避免了对字符串调用round()的错误

另外,建议也可以改进`recognition_evaluation()`函数的异常处理:
1. 可以返回None而不是空字符串
2. 或者抛出具体的异常以便更好地定位问题
3. 或者在异常时返回0.0这样的默认数值

这样可以使代码更加健壮,错误处理更加清晰。

---

_**User**_

/mnt/a/CODE/SKD-TSTSAN-new/@预处理0裁切.py @预处理0裁切改.py @预处理0裁切改进.py @预处理1prepare_casme2_data数据准备.py @预处理2光流optflow_for_classify.py @预处理3统计数据.py @预处理4loso.py 看一下代码 分析数据最终CASME2_LOSO_full的里面有什么 每一个sub的情况 给我完整的py代码

---

_**Assistant**_

```thinking
让我分析一下这个任务:

1. 需要分析CASME2_LOSO_full数据集的结构和内容
2. 需要统计每个sub(受试者)的具体情况
3. 需要编写一个完整的Python脚本来实现这个分析

从预处理流程来看:
1. 预处理0: 人脸检测和裁剪
2. 预处理1: 准备CASME2数据,分类到5个情绪类别
3. 预处理2: 计算光流
4. 预处理3: 统计数据
5. 预处理4: 构建LOSO(Leave-One-Subject-Out)数据集

CASME2_LOSO_full的结构应该是:
- sub01-sub26的文件夹
- 每个sub下有train和test
- 每个train/test下有0-4五个情绪类别
- 每个情绪类别下有:
  - onset/apex/offset原始图像
  - 光流图像(_1_u.jpg, _1_v.jpg, _2_u.jpg, _2_v.jpg)
  - 光流npy文件(_1.npy, _2.npy)

我来编写一个完整的分析脚本
```

```python
import os
import numpy as np
import pandas as pd
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns

class CASME2DatasetAnalyzer:
    """CASME2数据集分析器"""
    
    def __init__(self, dataset_path):
        """
        初始化分析器
        
        参数:
            dataset_path: CASME2_LOSO_full数据集的路径
        """
        self.dataset_path = dataset_path
        
        # 情绪标签映射
        self.emotion_labels = {
            '0': '快乐(happiness)',
            '1': '惊讶(surprise)', 
            '2': '厌恶(disgust)',
            '3': '压抑(repression)',
            '4': '其他(others)'
        }
        
        # 初始化统计数据结构
        self.stats = {
            'subjects': defaultdict(lambda: {
                'train': defaultdict(int),
                'test': defaultdict(int)
            }),
            'emotions': defaultdict(lambda: {
                'train': defaultdict(int),
                'test': defaultdict(int)
            }),
            'file_types': defaultdict(lambda: {
                'train': defaultdict(int),
                'test': defaultdict(int)
            })
        }

    def analyze_subject(self, subject):
        """分析单个受试者的数据"""
        subject_path = os.path.join(self.dataset_path, subject)
        
        # 分析训练集和测试集
        for split in ['train', 'test']:
            split_path = os.path.join(subject_path, split)
            if not os.path.exists(split_path):
                continue
                
            # 分析每个情绪类别
            for emotion in self.emotion_labels.keys():
                emotion_path = os.path.join(split_path, emotion)
                if not os.path.exists(emotion_path):
                    continue
                
                # 获取所有文件
                files = os.listdir(emotion_path)
                
                # 统计文件类型
                file_types = {
                    'onset': len([f for f in files if f.endswith('onset.jpg')]),
                    'apex': len([f for f in files if f.endswith('apex.jpg')]),
                    'offset': len([f for f in files if f.endswith('offset.jpg')]),
                    'flow_u': len([f for f in files if '_u.jpg' in f]),
                    'flow_v': len([f for f in files if '_v.jpg' in f]),
                    'flow_npy': len([f for f in files if f.endswith('.npy')])
                }
                
                # 更新统计信息
                self.stats['subjects'][subject][split][emotion] = len(files)
                self.stats['emotions'][emotion][split][subject] = len(files)
                for ftype, count in file_types.items():
                    self.stats['file_types'][ftype][split][subject] = count

    def analyze_dataset(self):
        """分析整个数据集"""
        print("\n开始分析CASME2_LOSO_full数据集...")
        
        # 获取所有受试者
        subjects = sorted([d for d in os.listdir(self.dataset_path) 
                         if os.path.isdir(os.path.join(self.dataset_path, d))])
        
        # 分析每个受试者
        for subject in subjects:
            print(f"\n分析受试者: {subject}")
            self.analyze_subject(subject)
            
    def print_statistics(self):
        """打印统计信息"""
        print("\n========== CASME2_LOSO_full 数据集统计 ==========")
        
        # 1. 受试者统计
        print("\n1. 受试者数据统计:")
        for subject in sorted(self.stats['subjects'].keys()):
            print(f"\n{subject}:")
            for split in ['train', 'test']:
                print(f"  {split}:")
                total_files = 0
                for emotion in self.emotion_labels.keys():
                    count = self.stats['subjects'][subject][split][emotion]
                    if count > 0:
                        print(f"    - {self.emotion_labels[emotion]}: {count} 个文件")
                        total_files += count
                print(f"    总计: {total_files} 个文件")
        
        # 2. 情绪类别统计
        print("\n2. 情绪类别统计:")
        for emotion in self.emotion_labels.keys():
            print(f"\n{self.emotion_labels[emotion]}:")
            for split in ['train', 'test']:
                total = sum(self.stats['emotions'][emotion][split].values())
                print(f"  {split}: {total} 个文件")
        
        # 3. 文件类型统计
        print("\n3. 文件类型统计:")
        for ftype in self.stats['file_types'].keys():
            print(f"\n{ftype}:")
            for split in ['train', 'test']:
                total = sum(self.stats['file_types'][ftype][split].values())
                print(f"  {split}: {total} 个文件")
                
    def generate_summary_dataframe(self):
        """生成汇总数据框"""
        # 创建多层索引的数据框
        data = []
        for subject in sorted(self.stats['subjects'].keys()):
            for split in ['train', 'test']:
                row = {
                    'subject': subject,
                    'split': split
                }
                # 添加每个情绪类别的文件数
                for emotion in self.emotion_labels.keys():
                    row[self.emotion_labels[emotion]] = self.stats['subjects'][subject][split][emotion]
                # 添加每种文件类型的数量
                for ftype in self.stats['file_types'].keys():
                    row[f'file_{ftype}'] = self.stats['file_types'][ftype][split][subject]
                data.append(row)
        
        return pd.DataFrame(data)

def main():
    # 设置数据集路径
    dataset_path = "CASME2_LOSO_full"
    
    try:
        # 创建分析器实例
        analyzer = CASME2DatasetAnalyzer(dataset_path)
        
        # 分析数据集
        analyzer.analyze_dataset()
        
        # 打印统计信息
        analyzer.print_statistics()
        
        # 生成汇总数据框
        df = analyzer.generate_summary_dataframe()
        
        # 保存到CSV文件
        output_file = "CASME2_LOSO_full_statistics.csv"
        df.to_csv(output_file, index=False)
        print(f"\n详细统计信息已保存到: {output_file}")
        
        print("\n分析完成!")
        
    except Exception as e:
        print(f"\n分析过程中出错: {str(e)}")

if __name__ == "__main__":
    main()
```

这个脚本的主要功能:

1. 创建了一个`CASME2DatasetAnalyzer`类来分析数据集:
   - 分析每个受试者(sub01-sub26)的数据
   - 统计训练集和测试集的分布
   - 统计每个情绪类别的样本数
   - 统计不同类型文件的数量

2. 提供了详细的统计信息:
   - 每个受试者的训练集和测试集数据分布
   - 每个情绪类别的样本分布
   - 不同类型文件(原始图像、光流图像、npy文件)的数量

3. 生成CSV格式的汇总报告,包含:
   - 受试者ID
   - 数据集划分(train/test)
   - 每个情绪类别的样本数
   - 每种文件类型的数量

使用这个脚本可以:
1. 全面了解CASME2_LOSO_full数据集的结构
2. 检查数据的完整性和分布
3. 获取详细的统计信息
4. 导出统计结果供进一步分析

---

_**User**_

@预处理5casme2class5loss数据分析.py 要输出出来 并且 分析sub的数量 等

---

_**Assistant**_

```thinking
我理解用户想要:
1. 运行这个分析脚本并输出结果
2. 对每个sub的数据进行更详细的分析
3. 添加更多统计维度

我将对原脚本进行扩展,添加:
1. 每个sub的样本数量统计
2. 每个情绪类别的分布情况
3. 样本不平衡分析
4. 可视化统计结果
5. 更详细的数据分析报告
```

```python
import os
import numpy as np
import pandas as pd
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns

class CASME2DatasetAnalyzer:
    """CASME2数据集分析器"""
    
    def __init__(self, dataset_path):
        self.dataset_path = dataset_path
        
        # 情绪标签映射
        self.emotion_labels = {
            '0': '快乐(happiness)',
            '1': '惊讶(surprise)', 
            '2': '厌恶(disgust)',
            '3': '压抑(repression)',
            '4': '其他(others)'
        }
        
        # 初始化统计数据结构
        self.stats = {
            'subjects': defaultdict(lambda: {
                'train': defaultdict(int),
                'test': defaultdict(int)
            }),
            'emotions': defaultdict(lambda: {
                'train': defaultdict(int),
                'test': defaultdict(int)
            }),
            'file_types': defaultdict(lambda: {
                'train': defaultdict(int),
                'test': defaultdict(int)
            }),
            'sample_counts': defaultdict(lambda: defaultdict(int))
        }

    def analyze_subject(self, subject):
        """分析单个受试者的数据"""
        subject_path = os.path.join(self.dataset_path, subject)
        
        # 分析训练集和测试集
        for split in ['train', 'test']:
            split_path = os.path.join(subject_path, split)
            if not os.path.exists(split_path):
                continue
                
            # 分析每个情绪类别
            for emotion in self.emotion_labels.keys():
                emotion_path = os.path.join(split_path, emotion)
                if not os.path.exists(emotion_path):
                    continue
                
                # 获取所有文件
                files = os.listdir(emotion_path)
                
                # 统计样本数(每3个文件算1个样本)
                sample_count = len([f for f in files if f.endswith('apex.jpg')])
                self.stats['sample_counts'][subject][emotion] = sample_count
                
                # 统计文件类型
                file_types = {
                    'onset': len([f for f in files if f.endswith('onset.jpg')]),
                    'apex': len([f for f in files if f.endswith('apex.jpg')]),
                    'offset': len([f for f in files if f.endswith('offset.jpg')]),
                    'flow_u': len([f for f in files if '_u.jpg' in f]),
                    'flow_v': len([f for f in files if '_v.jpg' in f]),
                    'flow_npy': len([f for f in files if f.endswith('.npy')])
                }
                
                # 更新统计信息
                self.stats['subjects'][subject][split][emotion] = len(files)
                self.stats['emotions'][emotion][split][subject] = len(files)
                for ftype, count in file_types.items():
                    self.stats['file_types'][ftype][split][subject] = count

    def analyze_dataset(self):
        """分析整个数据集"""
        print("\n开始分析CASME2_LOSO_full数据集...")
        
        # 获取所有受试者
        subjects = sorted([d for d in os.listdir(self.dataset_path) 
                         if os.path.isdir(os.path.join(self.dataset_path, d))])
        
        # 分析每个受试者
        for subject in subjects:
            print(f"\n分析受试者: {subject}")
            self.analyze_subject(subject)
            
    def print_statistics(self):
        """打印统计信息"""
        print("\n========== CASME2_LOSO_full 数据集统计 ==========")
        
        # 1. 总体统计
        print("\n1. 总体统计:")
        total_subjects = len(self.stats['subjects'])
        total_samples = sum(sum(counts.values()) for counts in self.stats['sample_counts'].values())
        print(f"总受试者数: {total_subjects}")
        print(f"总样本数: {total_samples}")
        
        # 2. 每个受试者的样本统计
        print("\n2. 受试者样本统计:")
        for subject in sorted(self.stats['sample_counts'].keys()):
            total_subject_samples = sum(self.stats['sample_counts'][subject].values())
            print(f"\n{subject}:")
            print(f"  总样本数: {total_subject_samples}")
            for emotion in self.emotion_labels.keys():
                count = self.stats['sample_counts'][subject][emotion]
                if count > 0:
                    print(f"  - {self.emotion_labels[emotion]}: {count} 个样本")
        
        # 3. 情绪类别统计
        print("\n3. 情绪类别统计:")
        emotion_totals = defaultdict(int)
        for subject in self.stats['sample_counts']:
            for emotion in self.emotion_labels:
                emotion_totals[emotion] += self.stats['sample_counts'][subject][emotion]
        
        for emotion in self.emotion_labels:
            print(f"\n{self.emotion_labels[emotion]}:")
            print(f"  总样本数: {emotion_totals[emotion]}")
            print(f"  占比: {emotion_totals[emotion]/total_samples*100:.2f}%")
        
        # 4. 样本分布分析
        print("\n4. 样本分布分析:")
        min_samples = min(emotion_totals.values())
        max_samples = max(emotion_totals.values())
        imbalance_ratio = max_samples / min_samples if min_samples > 0 else float('inf')
        print(f"最大类别样本数: {max_samples}")
        print(f"最小类别样本数: {min_samples}")
        print(f"不平衡比例: {imbalance_ratio:.2f}")
        
        # 5. 文件完整性检查
        print("\n5. 文件完整性检查:")
        for subject in sorted(self.stats['subjects'].keys()):
            for split in ['train', 'test']:
                for emotion in self.emotion_labels.keys():
                    expected_files = self.stats['subjects'][subject][split][emotion]
                    if expected_files > 0:
                        onset_files = self.stats['file_types']['onset'][split][subject]
                        apex_files = self.stats['file_types']['apex'][split][subject]
                        offset_files = self.stats['file_types']['offset'][split][subject]
                        flow_u_files = self.stats['file_types']['flow_u'][split][subject]
                        flow_v_files = self.stats['file_types']['flow_v'][split][subject]
                        npy_files = self.stats['file_types']['flow_npy'][split][subject]
                        
                        if not all(x == onset_files for x in [apex_files, offset_files]):
                            print(f"警告: {subject}/{split}/{emotion} 帧文件数量不一致")
                        if not flow_u_files == flow_v_files:
                            print(f"警告: {subject}/{split}/{emotion} 光流文件数量不一致")

    def plot_statistics(self):
        """绘制统计图表"""
        # 1. 样本分布柱状图
        plt.figure(figsize=(12, 6))
        emotion_totals = defaultdict(int)
        for subject in self.stats['sample_counts']:
            for emotion in self.emotion_labels:
                emotion_totals[self.emotion_labels[emotion]] += self.stats['sample_counts'][subject][emotion]
        
        plt.bar(emotion_totals.keys(), emotion_totals.values())
        plt.title('CASME2数据集情绪类别分布')
        plt.xticks(rotation=45)
        plt.ylabel('样本数量')
        plt.tight_layout()
        plt.savefig('emotion_distribution.png')
        plt.close()
        
        # 2. 受试者样本分布热力图
        plt.figure(figsize=(15, 8))
        heatmap_data = []
        for subject in sorted(self.stats['sample_counts'].keys()):
            row = []
            for emotion in self.emotion_labels.keys():
                row.append(self.stats['sample_counts'][subject][emotion])
            heatmap_data.append(row)
        
        sns.heatmap(heatmap_data, 
                   xticklabels=[self.emotion_labels[e] for e in self.emotion_labels],
                   yticklabels=sorted(self.stats['sample_counts'].keys()),
                   cmap='YlOrRd', annot=True, fmt='d')
        plt.title('受试者-情绪类别样本分布')
        plt.tight_layout()
        plt.savefig('subject_emotion_distribution.png')
        plt.close()

def main():
    # 设置数据集路径
    dataset_path = "CASME2_LOSO_full"
    
    try:
        # 创建分析器实例
        analyzer = CASME2DatasetAnalyzer(dataset_path)
        
        # 分析数据集
        analyzer.analyze_dataset()
        
        # 打印统计信息
        analyzer.print_statistics()
        
        # 绘制统计图表
        analyzer.plot_statistics()
        
        # 生成汇总数据框
        df = analyzer.generate_summary_dataframe()
        
        # 保存到CSV文件
        output_file = "CASME2_LOSO_full_statistics.csv"
        df.to_csv(output_file, index=False)
        print(f"\n详细统计信息已保存到: {output_file}")
        print("统计图表已保存为: emotion_distribution.png 和 subject_emotion_distribution.png")
        
        print("\n分析完成!")
        
    except Exception as e:
        print(f"\n分析过程中出错: {str(e)}")

if __name__ == "__main__":
    main()
```

这个增强版的分析脚本提供了:

1. 更详细的统计信息:
   - 总体数据统计
   - 每个受试者的样本分布
   - 情绪类别的分布情况
   - 样本不平衡分析
   - 文件完整性检查

2. 可视化分析:
   - 情绪类别分布柱状图
   - 受试者-情绪类别分布热力图

3. 数据质量检查:
   - 检查文件完整性
   - 检查数据一致性
   - 识别潜在问题

4. 输出多种格式:
   - 控制台打印详细统计
   - CSV文件保存数据
   - PNG图表保存可视化结果

使用这个脚本可以:
1. 全面了解数据集的结构和分布
2. 发现数据中的潜在问题
3. 为后续的模型训练提供参考
4. 帮助理解数据集的特点和挑战

---

_**User**_

@预处理5casme2class5loss数据分析.py 输出每个sub的test标签 加上总共的分析

---

_**Assistant**_

```thinking
我理解用户想要:
1. 分析每个sub的test集标签分布
2. 统计总体的test标签分布
3. 添加更多的分析维度

我将修改代码,重点关注test集的分析,并添加总体统计
```

```python
import os
import numpy as np
import pandas as pd
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns

class CASME2TestAnalyzer:
    """CASME2数据集测试集分析器"""
    
    def __init__(self, dataset_path):
        self.dataset_path = dataset_path
        
        # 情绪标签映射
        self.emotion_labels = {
            '0': '快乐(happiness)',
            '1': '惊讶(surprise)', 
            '2': '厌恶(disgust)',
            '3': '压抑(repression)',
            '4': '其他(others)'
        }
        
        # 初始化统计数据结构
        self.stats = {
            'test_samples': defaultdict(lambda: defaultdict(int)),
            'total_test': defaultdict(int),
            'total_train': defaultdict(int),
            'subject_distribution': defaultdict(lambda: defaultdict(int))
        }

    def analyze_test_set(self):
        """分析测试集数据"""
        print("\n开始分析CASME2_LOSO_full测试集...")
        
        # 获取所有受试者
        subjects = sorted([d for d in os.listdir(self.dataset_path) 
                         if os.path.isdir(os.path.join(self.dataset_path, d))])
        
        # 分析每个受试者
        for subject in subjects:
            test_path = os.path.join(self.dataset_path, subject, 'test')
            if not os.path.exists(test_path):
                continue
                
            print(f"\n分析受试者 {subject} 的测试集:")
            
            # 分析每个情绪类别
            for emotion in self.emotion_labels.keys():
                emotion_path = os.path.join(test_path, emotion)
                if not os.path.exists(emotion_path):
                    continue
                
                # 统计apex帧数量(每个样本有一个apex帧)
                sample_count = len([f for f in os.listdir(emotion_path) if f.endswith('apex.jpg')])
                if sample_count > 0:
                    self.stats['test_samples'][subject][emotion] = sample_count
                    self.stats['total_test'][emotion] += sample_count
                    print(f"  - {self.emotion_labels[emotion]}: {sample_count} 个样本")
            
            # 计算该受试者的总测试样本数
            total_subject_test = sum(self.stats['test_samples'][subject].values())
            print(f"  总计: {total_subject_test} 个测试样本")

    def print_test_statistics(self):
        """打印测试集统计信息"""
        print("\n========== CASME2_LOSO_full 测试集统计 ==========")
        
        # 1. 每个受试者的测试样本分布
        print("\n1. 受试者测试样本分布:")
        total_test_samples = 0
        for subject in sorted(self.stats['test_samples'].keys()):
            subject_total = sum(self.stats['test_samples'][subject].values())
            total_test_samples += subject_total
            print(f"\n{subject}:")
            for emotion in self.emotion_labels.keys():
                count = self.stats['test_samples'][subject][emotion]
                if count > 0:
                    print(f"  - {self.emotion_labels[emotion]}: {count} 个样本")
            print(f"  总计: {subject_total} 个测试样本")
        
        # 2. 情绪类别总体分布
        print("\n2. 情绪类别总体分布:")
        for emotion in self.emotion_labels.keys():
            total = self.stats['total_test'][emotion]
            if total > 0:
                percentage = (total / total_test_samples) * 100
                print(f"\n{self.emotion_labels[emotion]}:")
                print(f"  样本数: {total}")
                print(f"  占比: {percentage:.2f}%")
        
        # 3. 样本分布分析
        print("\n3. 测试集样本分布分析:")
        emotion_counts = [self.stats['total_test'][e] for e in self.emotion_labels.keys()]
        min_samples = min(c for c in emotion_counts if c > 0)
        max_samples = max(emotion_counts)
        imbalance_ratio = max_samples / min_samples if min_samples > 0 else float('inf')
        print(f"最大类别样本数: {max_samples}")
        print(f"最小类别样本数: {min_samples}")
        print(f"不平衡比例: {imbalance_ratio:.2f}")
        print(f"总测试样本数: {total_test_samples}")
        
        # 4. 每个受试者的主要测试类别
        print("\n4. 受试者主要测试类别:")
        for subject in sorted(self.stats['test_samples'].keys()):
            max_emotion = max(self.stats['test_samples'][subject].items(), 
                            key=lambda x: x[1], default=(None, 0))
            if max_emotion[0] is not None:
                print(f"{subject}: 主要测试类别 - {self.emotion_labels[max_emotion[0]]} "
                      f"({max_emotion[1]} 个样本)")

    def plot_test_distribution(self):
        """绘制测试集分布图"""
        # 1. 情绪类别分布柱状图
        plt.figure(figsize=(12, 6))
        emotions = [self.emotion_labels[e] for e in self.emotion_labels.keys()]
        counts = [self.stats['total_test'][e] for e in self.emotion_labels.keys()]
        
        plt.bar(emotions, counts)
        plt.title('CASME2测试集情绪类别分布')
        plt.xticks(rotation=45)
        plt.ylabel('样本数量')
        plt.tight_layout()
        plt.savefig('test_emotion_distribution.png')
        plt.close()
        
        # 2. 受试者测试样本分布热力图
        plt.figure(figsize=(15, 8))
        heatmap_data = []
        for subject in sorted(self.stats['test_samples'].keys()):
            row = []
            for emotion in self.emotion_labels.keys():
                row.append(self.stats['test_samples'][subject][emotion])
            heatmap_data.append(row)
        
        sns.heatmap(heatmap_data, 
                   xticklabels=[self.emotion_labels[e] for e in self.emotion_labels],
                   yticklabels=sorted(self.stats['test_samples'].keys()),
                   cmap='YlOrRd', annot=True, fmt='d')
        plt.title('受试者-情绪类别测试样本分布')
        plt.tight_layout()
        plt.savefig('test_subject_emotion_distribution.png')
        plt.close()

def main():
    # 设置数据集路径
    dataset_path = "CASME2_LOSO_full"
    
    try:
        # 创建分析器实例
        analyzer = CASME2TestAnalyzer(dataset_path)
        
        # 分析测试集
        analyzer.analyze_test_set()
        
        # 打印统计信息
        analyzer.print_test_statistics()
        
        # 绘制分布图
        analyzer.plot_test_distribution()
        
        print("\n分析完成!")
        print("统计图表已保存为: test_emotion_distribution.png 和 test_subject_emotion_distribution.png")
        
    except Exception as e:
        print(f"\n分析过程中出错: {str(e)}")

if __name__ == "__main__":
    main()
```

这个新的分析脚本专注于测试集分析,提供:

1. 详细的测试集统计:
   - 每个受试者的测试样本分布
   - 每个情绪类别的样本数量和占比
   - 测试集的不平衡程度分析
   - 每个受试者的主要测试类别

2. 可视化分析:
   - 测试集情绪类别分布柱状图
   - 受试者-情绪类别测试样本分布热力图

3. 重要统计指标:
   - 样本不平衡比例
   - 各类别占比
   - 每个受试者的测试样本构成

4. 输出格式:
   - 控制台打印详细统计
   - 保存分布图为PNG文件

这个分析可以帮助:
1. 了解每个受试者的测试数据特点
2. 评估测试集的类别平衡性
3. 识别可能的数据偏差
4. 为模型评估提供参考

---

_**User**_

标签是这个样子 01234 输出

---

_**Assistant**_

```thinking
我理解用户想要:
1. 按照CASME2数据集的标签映射(0-4)输出每个sub的test标签
2. 标签对应关系为:
   - 0: happiness(快乐)
   - 1: surprise(惊讶)
   - 2: disgust(厌恶)
   - 3: repression(压抑)
   - 4: others(其他)
3. 需要简单直观地输出每个sub的test标签分布

我将修改代码,专注于输出test标签信息
```

```python
import os
from collections import defaultdict

def analyze_casme2_test_labels():
    """分析CASME2数据集每个sub的test标签"""
    
    # 数据集路径
    dataset_path = "CASME2_LOSO_full"
    
    # 标签映射
    label_map = {
        '0': 'happiness',
        '1': 'surprise',
        '2': 'disgust', 
        '3': 'repression',
        '4': 'others'
    }
    
    # 存储统计信息
    stats = defaultdict(lambda: defaultdict(int))
    total_stats = defaultdict(int)
    
    print("\n========== CASME2 测试集标签分布 ==========")
    
    # 遍历所有受试者
    subjects = sorted([d for d in os.listdir(dataset_path) 
                      if os.path.isdir(os.path.join(dataset_path, d))])
    
    for subject in subjects:
        test_path = os.path.join(dataset_path, subject, 'test')
        if not os.path.exists(test_path):
            continue
            
        print(f"\n受试者 {subject} 的测试标签分布:")
        subject_total = 0
        
        # 统计每个标签的样本数
        for label in label_map.keys():
            label_path = os.path.join(test_path, label)
            if os.path.exists(label_path):
                # 统计apex帧数量(代表样本数)
                sample_count = len([f for f in os.listdir(label_path) if f.endswith('apex.jpg')])
                if sample_count > 0:
                    stats[subject][label] = sample_count
                    total_stats[label] += sample_count
                    subject_total += sample_count
                    print(f"标签 {label} ({label_map[label]}): {sample_count} 个样本")
        
        print(f"总计: {subject_total} 个测试样本")
    
    # 打印总体统计
    print("\n========== 总体标签分布 ==========")
    total_samples = sum(total_stats.values())
    print(f"总测试样本数: {total_samples}")
    
    for label in label_map.keys():
        count = total_stats[label]
        percentage = (count / total_samples * 100) if total_samples > 0 else 0
        print(f"标签 {label} ({label_map[label]}):")
        print(f"  样本数: {count}")
        print(f"  占比: {percentage:.2f}%")
    
    # 打印标签分布摘要
    print("\n========== 标签分布摘要 ==========")
    print("格式: [标签数量] (占比%)")
    for subject in sorted(stats.keys()):
        dist = []
        subject_total = sum(stats[subject].values())
        for label in label_map.keys():
            count = stats[subject][label]
            percentage = (count / subject_total * 100) if subject_total > 0 else 0
            if count > 0:
                dist.append(f"{label}:[{count}]({percentage:.1f}%)")
        print(f"{subject}: {' '.join(dist)}")

if __name__ == "__main__":
    try:
        analyze_casme2_test_labels()
    except Exception as e:
        print(f"分析过程中出错: {str(e)}")
```

这个脚本会:

1. 按受试者输出详细的测试标签分布:
   - 每个标签的样本数量
   - 标签对应的情绪类别
   - 每个受试者的总样本数

2. 输出总体标签分布:
   - 每个标签的总样本数
   - 每个标签的占比
   - 总测试样本数

3. 输出简洁的标签分布摘要:
   - 每个受试者的标签分布
   - 格式为: 标签:[样本数](占比%)
   - 只显示非空标签

输出格式示例:
```
受试者 sub01:
标签 0 (happiness): 2 个样本
标签 2 (disgust): 3 个样本
标签 4 (others): 1 个样本
总计: 6 个测试样本

标签分布摘要:
sub01: 0:[2](33.3%) 2:[3](50.0%) 4:[1](16.7%)
```

这样可以:
1. 清晰看到每个sub的test标签分布
2. 了解整个数据集的标签分布情况
3. 方便识别数据不平衡问题
4. 帮助理解每个受试者的测试数据特点

---

_**User**_

@预处理5casme2class5loss数据分析.py 输出这种

---

_**Assistant**_

```thinking
我理解用户想要:
1. 只输出每个sub的test标签序列
2. 格式类似于 "真实标签: [2, 2, 4, 4]" 这样的形式
3. 需要按照apex.jpg文件名的顺序输出标签

我将修改代码,专注于输出标签序列
```

```python
import os
from collections import defaultdict

def analyze_casme2_test_labels():
    """分析CASME2数据集每个sub的test标签序列"""
    
    # 数据集路径
    dataset_path = "CASME2_LOSO_full"
    
    # 存储每个受试者的标签序列
    subject_labels = defaultdict(list)
    
    # 遍历所有受试者
    subjects = sorted([d for d in os.listdir(dataset_path) 
                      if os.path.isdir(os.path.join(dataset_path, d))])
    
    print("\n========== CASME2 测试集标签序列 ==========")
    
    # 收集所有标签
    for subject in subjects:
        test_path = os.path.join(dataset_path, subject, 'test')
        if not os.path.exists(test_path):
            continue
            
        # 收集该受试者的所有apex文件和对应标签
        apex_files = []
        for label in range(5):  # 标签0-4
            label_path = os.path.join(test_path, str(label))
            if os.path.exists(label_path):
                for file in os.listdir(label_path):
                    if file.endswith('apex.jpg'):
                        apex_files.append((file, label))
        
        # 按文件名排序
        apex_files.sort(key=lambda x: x[0])
        
        # 提取标签序列
        labels = [label for _, label in apex_files]
        subject_labels[subject] = labels
        
        # 打印该受试者的标签序列
        print(f"\n{subject} 的测试标签序列:")
        print(f"标签: {labels}")
        print(f"样本数: {len(labels)}")
    
    # 打印总体统计
    print("\n========== 总体统计 ==========")
    total_samples = sum(len(labels) for labels in subject_labels.values())
    print(f"总测试样本数: {total_samples}")
    
    # 统计每个标签的数量
    label_counts = defaultdict(int)
    for labels in subject_labels.values():
        for label in labels:
            label_counts[label] += 1
    
    print("\n标签分布:")
    for label in sorted(label_counts.keys()):
        count = label_counts[label]
        percentage = (count / total_samples * 100)
        print(f"标签 {label}: {count} 个样本 ({percentage:.1f}%)")

if __name__ == "__main__":
    try:
        analyze_casme2_test_labels()
    except Exception as e:
        print(f"分析过程中出错: {str(e)}")
```

这个修改后的脚本会:

1. 按受试者输出标签序列:
```
sub01 的测试标签序列:
标签: [2, 2, 4, 4]
样本数: 4
```

2. 保持标签的原始顺序:
- 按照apex.jpg文件名排序
- 保持数据的时序性

3. 提供总体统计:
- 总样本数
- 每个标签的数量和占比

这样可以:
1. 清晰看到每个sub的test标签序列
2. 了解标签的顺序
3. 方便与预测结果对比
4. 保持数据的原始顺序

输出更加简洁直观,便于:
1. 查看每个受试者的测试标签
2. 分析标签分布
3. 进行结果对比
4. 理解数据结构

---

_**User**_

这不对啊 数据对不上啊 

---

_**Assistant**_

```thinking
我理解用户的问题:
1. 输出的数据与实际数据不匹配
2. 需要仔细检查数据读取和统计逻辑
3. 需要确保标签序列的准确性

让我修改代码,增加数据验证和详细输出:
1. 检查每个文件的完整路径
2. 验证数据读取过程
3. 输出更多调试信息
```

```python
import os
from collections import defaultdict

def analyze_casme2_test_labels():
    """分析CASME2数据集每个sub的test标签序列"""
    
    # 数据集路径
    dataset_path = "CASME2_LOSO_full"
    
    # 存储每个受试者的标签序列
    subject_labels = defaultdict(list)
    
    print("\n========== CASME2 测试集标签序列 ==========")
    
    # 遍历所有受试者
    subjects = sorted([d for d in os.listdir(dataset_path) 
                      if os.path.isdir(os.path.join(dataset_path, d))])
    
    for subject in subjects:
        test_path = os.path.join(dataset_path, subject, 'test')
        if not os.path.exists(test_path):
            print(f"警告: 测试目录不存在 - {test_path}")
            continue
            
        print(f"\n处理受试者: {subject}")
        
        # 收集该受试者的所有apex文件和对应标签
        apex_files = []
        for label in range(5):  # 标签0-4
            label_path = os.path.join(test_path, str(label))
            if not os.path.exists(label_path):
                print(f"  标签{label}目录不存在: {label_path}")
                continue
                
            print(f"\n  标签 {label} 的文件:")
            for file in sorted(os.listdir(label_path)):
                if file.endswith('apex.jpg'):
                    full_path = os.path.join(label_path, file)
                    print(f"    {file}")
                    apex_files.append((file, label, full_path))
        
        # 按文件名排序
        apex_files.sort(key=lambda x: x[0])
        
        # 提取标签序列
        labels = [label for _, label, _ in apex_files]
        subject_labels[subject] = labels
        
        # 打印该受试者的详细信息
        print(f"\n{subject} 的测试标签序列:")
        print(f"标签: {labels}")
        print(f"样本数: {len(labels)}")
        print("\n文件详情:")
        for file, label, path in apex_files:
            print(f"  {file} -> 标签 {label}")
    
    # 打印总体统计
    print("\n========== 总体统计 ==========")
    total_samples = sum(len(labels) for labels in subject_labels.values())
    print(f"总测试样本数: {total_samples}")
    
    # 统计每个标签的数量
    label_counts = defaultdict(int)
    for labels in subject_labels.values():
        for label in labels:
            label_counts[label] += 1
    
    print("\n标签分布:")
    for label in sorted(label_counts.keys()):
        count = label_counts[label]
        percentage = (count / total_samples * 100)
        print(f"标签 {label}: {count} 个样本 ({percentage:.1f}%)")

if __name__ == "__main__":
    try:
        analyze_casme2_test_labels()
    except Exception as e:
        print(f"分析过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
```

这个修改后的脚本会:

1. 输出详细的调试信息:
- 显示完整的文件路径
- 显示每个目录的处理状态
- 显示文件排序和标签提取过程

2. 验证数据读取:
- 检查目录是否存在
- 检查文件是否可访问
- 显示文件名和对应标签

3. 提供更多上下文:
- 显示处理过程中的每个步骤
- 输出文件和标签的对应关系
- 显示可能的错误或异常

这样可以:
1. 检查数据读取是否正确
2. 验证标签提取逻辑
3. 找出可能的问题
4. 确保输出数据的准确性

请运行这个版本,我们可以根据输出进一步诊断问题。

---

_**User**_

测试数据数目不对劲啊？

---

_**Assistant**_

```thinking
我看到问题了:
1. 总体统计显示的样本数不一致:
   - 测试集统计: 215个样本
   - 总体统计: 244个样本

2. 每个类别的数量也不一致:
   测试集vs总体:
   - 快乐(0): 30 vs 32
   - 惊讶(1): 21 vs 25
   - 厌恶(2): 56 vs 62
   - 压抑(3): 21 vs 26
   - 其他(4): 87 vs 99

3. 可能的原因:
   - 文件读取逻辑有问题
   - 统计方式不准确
   - 可能漏掉了某些样本

让我修改代码来仔细检查每个样本
```

```python
import os
from collections import defaultdict

def analyze_casme2_test_labels(verbose=True):
    """分析CASME2数据集每个sub的test标签序列"""
    
    # 数据集路径
    dataset_path = "CASME2_LOSO_full"
    
    # 存储统计信息
    subject_labels = defaultdict(list)  # 每个受试者的标签序列
    subject_files = defaultdict(list)   # 每个受试者的文件列表
    label_counts = defaultdict(int)     # 每个标签的总数
    
    print("\n========== CASME2 测试集标签序列 ==========")
    
    # 遍历所有受试者
    subjects = sorted([d for d in os.listdir(dataset_path) 
                      if os.path.isdir(os.path.join(dataset_path, d))])
    
    missing_files = []  # 记录缺失的文件
    
    for subject in subjects:
        test_path = os.path.join(dataset_path, subject, 'test')
        if not os.path.exists(test_path):
            print(f"警告: 测试目录不存在 - {test_path}")
            continue
            
        if verbose:
            print(f"\n处理受试者: {subject}")
        
        # 收集该受试者的所有样本
        subject_samples = []
        
        for label in range(5):  # 标签0-4
            label_path = os.path.join(test_path, str(label))
            if not os.path.exists(label_path):
                if verbose:
                    print(f"  标签{label}目录不存在: {label_path}")
                continue
            
            # 检查每个样本的完整性
            for file in sorted(os.listdir(label_path)):
                if file.endswith('apex.jpg'):
                    base_name = file[:-9]  # 移除'_apex.jpg'
                    
                    # 检查所需的所有文件是否存在
                    required_files = [
                        f"{base_name}_onset.jpg",
                        f"{base_name}_apex.jpg",
                        f"{base_name}_offset.jpg",
                        f"{base_name}_1_u.jpg",
                        f"{base_name}_1_v.jpg",
                        f"{base_name}_2_u.jpg",
                        f"{base_name}_2_v.jpg"
                    ]
                    
                    files_exist = True
                    for req_file in required_files:
                        if not os.path.exists(os.path.join(label_path, req_file)):
                            missing_files.append((subject, label, req_file))
                            files_exist = False
                    
                    if files_exist:
                        subject_samples.append((base_name, label))
                        label_counts[label] += 1
        
        # 按文件名排序
        subject_samples.sort(key=lambda x: x[0])
        
        # 提取标签序列
        labels = [label for _, label in subject_samples]
        files = [name for name, _ in subject_samples]
        
        subject_labels[subject] = labels
        subject_files[subject] = files
        
        # 打印该受试者的信息
        print(f"\n{subject} 的测试标签:")
        print(f"样本数: {len(labels)}")
        print(f"标签序列: {labels}")
        if verbose:
            print("\n文件列表:")
            for name, label in subject_samples:
                print(f"  {name} -> 标签 {label}")
    
    # 打印总体统计
    print("\n========== 总体统计 ==========")
    total_samples = sum(len(labels) for labels in subject_labels.values())
    print(f"总测试样本数: {total_samples}")
    
    print("\n标签分布:")
    emotion_names = ['快乐(happiness)', '惊讶(surprise)', '厌恶(disgust)', 
                    '压抑(repression)', '其他(others)']
    
    for label in range(5):
        count = label_counts[label]
        percentage = (count / total_samples * 100) if total_samples > 0 else 0
        print(f"标签 {label} ({emotion_names[label]}): {count} 个样本 ({percentage:.1f}%)")
    
    if missing_files:
        print("\n警告: 发现缺失文件:")
        for subject, label, file in missing_files:
            print(f"  {subject}/test/{label}/{file}")

    return subject_labels, label_counts

if __name__ == "__main__":
    try:
        analyze_casme2_test_labels()
    except Exception as e:
        print(f"分析过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
```

这个修改后的脚本:

1. 增加了完整性检查:
   - 检查每个样本的所有必需文件
   - 记录缺失的文件
   - 只统计完整的样本

2. 提供更详细的统计:
   - 每个受试者的样本数
   - 每个标签的详细分布
   - 文件名和标签的对应关系

3. 添加了验证步骤:
   - 检查文件完整性
   - 验证样本的所有组成部分
   - 输出可能的问题

这样可以:
1. 准确统计样本数量
2. 发现数据集中的问题
3. 确保统计的准确性
4. 找出数据不一致的原因

请运行这个版本,我们可以:
1. 检查是否有缺失文件
2. 验证样本统计的准确性
3. 找出数据不一致的具体原因

---

_**User**_

@预处理4loso.py 是不是loso代码的问题？检查一下

---

_**Assistant**_

```thinking
让我分析一下LOSO数据集构建的代码:

1. 检查LOSO构建逻辑:
   - 每个sub作为测试集时,其他sub作为训练集
   - 需要保持原始数据的完整性
   - 需要正确复制所有相关文件

2. 可能的问题:
   - 文件复制逻辑
   - 目录结构创建
   - 数据分割方式

3. 需要修改的地方:
   - 添加详细的验证步骤
   - 确保数据完整性
   - 输出详细的处理信息
```

```python
import os
import shutil
import time
from collections import defaultdict

def verify_loso_splits(input_folders, output_base):
    """验证LOSO数据集的构建结果"""
    print("\n开始验证LOSO数据集...")
    
    stats = {
        'subjects': defaultdict(lambda: {'test': 0, 'train': 0}),
        'emotions': defaultdict(lambda: {'test': 0, 'train': 0}),
        'files': defaultdict(lambda: {'test': 0, 'train': 0})
    }
    
    # 检查每个受试者的数据
    for subject in sorted(os.listdir(output_base)):
        subject_path = os.path.join(output_base, subject)
        if not os.path.isdir(subject_path):
            continue
            
        print(f"\n验证受试者 {subject}:")
        
        # 检查训练集和测试集
        for split in ['train', 'test']:
            split_path = os.path.join(subject_path, split)
            if not os.path.exists(split_path):
                print(f"警告: {split} 目录不存在")
                continue
                
            # 检查每个情绪类别
            for emotion in range(5):
                emotion_path = os.path.join(split_path, str(emotion))
                if not os.path.exists(emotion_path):
                    continue
                
                # 统计文件
                files = os.listdir(emotion_path)
                apex_files = [f for f in files if f.endswith('apex.jpg')]
                onset_files = [f for f in files if f.endswith('onset.jpg')]
                offset_files = [f for f in files if f.endswith('offset.jpg')]
                flow_u_files = [f for f in files if '_u.jpg' in f]
                flow_v_files = [f for f in files if '_v.jpg' in f]
                
                # 验证文件完整性
                if len(apex_files) != len(onset_files) or len(apex_files) != len(offset_files):
                    print(f"警告: {subject}/{split}/{emotion} 帧文件数量不一致")
                    print(f"  apex: {len(apex_files)}, onset: {len(onset_files)}, offset: {len(offset_files)}")
                
                if len(flow_u_files) != len(flow_v_files):
                    print(f"警告: {subject}/{split}/{emotion} 光流文件数量不一致")
                    print(f"  flow_u: {len(flow_u_files)}, flow_v: {len(flow_v_files)}")
                
                # 更新统计信息
                stats['subjects'][subject][split] += len(apex_files)
                stats['emotions'][emotion][split] += len(apex_files)
                stats['files'][split]['total'] = stats['files'][split].get('total', 0) + len(files)
                stats['files'][split]['apex'] = stats['files'][split].get('apex', 0) + len(apex_files)
    
    # 打印验证结果
    print("\n========== LOSO数据集验证结果 ==========")
    
    print("\n1. 受试者数据分布:")
    for subject in sorted(stats['subjects'].keys()):
        print(f"\n{subject}:")
        for split in ['train', 'test']:
            print(f"  {split}: {stats['subjects'][subject][split]} 个样本")
    
    print("\n2. 情绪类别分布:")
    for emotion in sorted(stats['emotions'].keys()):
        print(f"\n情绪 {emotion}:")
        for split in ['train', 'test']:
            print(f"  {split}: {stats['emotions'][emotion][split]} 个样本")
    
    print("\n3. 文件统计:")
    for split in ['train', 'test']:
        print(f"\n{split}:")
        print(f"  总文件数: {stats['files'][split]['total']}")
        print(f"  样本数(apex): {stats['files'][split]['apex']}")
    
    return stats

def create_loso_splits(input_folders, output_base, file_types=None):
    """创建LOSO交叉验证数据集"""
    if file_types is None:
        file_types = ['.jpg', '.npy']
    
    print("\n开始构建LOSO数据集...")
    start_time = time.time()
    
    # 清理并创建输出目录
    if os.path.exists(output_base):
        print(f"清理输出目录: {output_base}")
        shutil.rmtree(output_base)
    os.makedirs(output_base)
    
    # 遍历每个受试者
    for sub_num in range(1, 27):
        sub_prefix = f'sub{sub_num:02d}'
        print(f"\n处理受试者 {sub_prefix}...")
        
        # 创建当前受试者的目录
        sub_folder = os.path.join(output_base, sub_prefix)
        
        # 遍历每个情绪类别
        for emotion in range(5):
            print(f"  处理情绪类别: {emotion}")
            
            # 遍历每个输入目录
            for input_folder in input_folders:
                emotion_path = os.path.join(input_folder, str(emotion))
                if not os.path.exists(emotion_path):
                    print(f"    警告: 目录不存在 {emotion_path}")
                    continue
                
                # 获取所有文件
                all_files = [f for f in os.listdir(emotion_path) 
                           if any(f.endswith(ft) for ft in file_types)]
                
                # 分离当前受试者和其他受试者的文件
                test_files = [f for f in all_files if f.startswith(sub_prefix)]
                train_files = [f for f in all_files if not f.startswith(sub_prefix)]
                
                # 创建测试集目录
                if test_files:
                    test_folder = os.path.join(sub_folder, 'test', str(emotion))
                    os.makedirs(test_folder, exist_ok=True)
                    
                    # 复制测试文件
                    for file in test_files:
                        src = os.path.join(emotion_path, file)
                        dst = os.path.join(test_folder, file)
                        shutil.copy2(src, dst)
                
                # 创建训练集目录
                if train_files:
                    train_folder = os.path.join(sub_folder, 'train', str(emotion))
                    os.makedirs(train_folder, exist_ok=True)
                    
                    # 复制训练文件
                    for file in train_files:
                        src = os.path.join(emotion_path, file)
                        dst = os.path.join(train_folder, file)
                        shutil.copy2(src, dst)
    
    # 验证数据集
    stats = verify_loso_splits(input_folders, output_base)
    
    # 计算处理时间
    end_time = time.time()
    print(f"\n数据集构建完成! 用时: {end_time - start_time:.2f} 秒")
    
    return stats

def main():
    # 设置输入输出路径
    input_folders = [
        "CASME2_onset_apex_offset_retinaface",  # 原始图像
        "CASME2_optflow_retinaface",          # jpg格式的光流图像
        "CASME2_optflow_retinaface_npy"       # npy格式的原始数据
    ]
    
    output_base = "CASME2_LOSO_full"
    file_types = ['.jpg', '.npy']
    
    try:
        # 构建并验证LOSO数据集
        stats = create_loso_splits(input_folders, output_base, file_types)
        
    except Exception as e:
        print(f"\n错误: {str(e)}")
        print("LOSO数据集构建失败！")
        return

if __name__ == "__main__":
    main()
```

这个修改后的脚本:

1. 添加了详细的验证步骤:
   - 检查目录结构
   - 验证文件完整性
   - 统计样本分布

2. 增强了数据分割逻辑:
   - 确保正确分离训练和测试数据
   - 验证文件复制过程
   - 保持数据完整性

3. 提供详细的统计信息:
   - 每个受试者的样本分布
   - 每个情绪类别的分布
   - 文件数量统计

4. 添加了错误检查:
   - 检查目录存在性
   - 验证文件复制
   - 输出警告信息

建议:
1. 先删除现有的CASME2_LOSO_full目录
2. 重新运行这个脚本构建数据集
3. 检查验证结果
4. 确保数据完整性

这样可以:
1. 确保LOSO数据集构建正确
2. 验证数据完整性
3. 找出潜在问题
4. 保证实验数据的质量

---

