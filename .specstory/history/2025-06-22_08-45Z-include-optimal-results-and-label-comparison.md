<!-- Generated by SpecStory -->

# Include optimal results and label comparison (2025-06-22 08:45Z)

_**User**_

@train_classify_SKD_TSTSAN_functions.py @train_classify_SKD_TSTSAN.py 

邮件里加上 最优预测结果 和 原始标签的对比 


---

_**Assistant**_

<thinking>
我需要修改邮件通知功能，在邮件中添加最优预测结果和原始标签的对比。首先，我需要理解当前的邮件通知实现，以及预测结果和标签的存储方式。

从提供的代码文件中，我可以看到：

1. `train_classify_SKD_TSTSAN.py` 文件中有一个 `send_training_results` 函数，负责发送训练结果邮件。
2. `format_subject_results` 函数用于格式化受试者的结果，包括简要结果和详细结果。
3. 训练结果存储在 `results_dict` 中，其中包含了每个受试者的预测结果和真实标签。

我需要修改 `send_training_results` 函数或 `format_subject_results` 函数，确保邮件中包含最优预测结果和原始标签的对比。

具体来说，我需要：
1. 确认 `results_dict` 中是否已经包含了最优预测结果和原始标签
2. 修改 `format_subject_results` 函数，确保它返回的详细结果包含最优预测结果和原始标签的对比
3. 确保这些信息被正确地添加到邮件内容中

从 `train_classify_SKD_TSTSAN_functions.py` 中可以看到，`results_dict` 的 `subject_results` 字段中包含了每个受试者的 `pred` 和 `truth`，这正是我们需要的最优预测结果和原始标签。

在 `format_subject_results` 函数中，已经有一部分代码在生成标签对比：
```python
detailed_results += f"\n标签对比:\n"
detailed_results += f"预测标签: {data['pred']}\n"
detailed_results += f"真实标签: {data['truth']}\n"
```

但是，这个信息似乎只在详细结果部分，而不是在简要结果部分。而且，根据 `send_training_results` 函数的逻辑，只有在单个受试者的情况下才会添加详细结果到邮件中。

我需要修改代码，确保无论是单个受试者还是多个受试者的情况，邮件中都包含最优预测结果和原始标签的对比。

让我检查一下 `send_training_results` 函数的逻辑：

```python
def send_training_results(config, results_dict, total_time):
    # ...
    brief_results, detailed_results = format_subject_results(results_dict)
    # ...
    content = f"""
    # ...
    """
    
    # 添加受试者结果
    content += f"\n\n{brief_results}"
    
    # 如果是单个受试者的结果，添加详细结果
    if is_single_subject:
        content += f"\n{detailed_results}"
    # ...
```

可以看到，只有在单个受试者的情况下，才会添加 `detailed_results` 到邮件内容中。我需要修改这部分逻辑，确保无论是单个受试者还是多个受试者，都能在邮件中看到最优预测结果和原始标签的对比。

我的修改策略是：
1. 在 `format_subject_results` 函数中，将标签对比信息也添加到简要结果中
2. 或者，修改 `send_training_results` 函数，确保无论是单个受试者还是多个受试者，都添加标签对比信息到邮件内容中

考虑到简要结果应该保持简洁，我选择修改 `send_training_results` 函数，添加一个新的部分专门用于显示最优预测结果和原始标签的对比。

现在，我来修改 `send_training_results` 函数：
</thinking>

我需要修改邮件通知功能，添加最优预测结果和原始标签的对比。我将修改 `send_training_results` 函数，确保邮件中包含这些信息。

---

```diff
          content += f"\n\n{brief_results}"
          
+         # 添加最优预测结果和原始标签对比
+         content += "\n\n【最优预测结果与原始标签对比】\n"
+         content += "-" * 50 + "\n"
+         
+         for subject_id, data in sorted(results_dict.get('subject_results', {}).items()):
+             content += f"\n受试者 {subject_id}:\n"
+             content += f"预测标签: {data['pred']}\n"
+             content += f"真实标签: {data['truth']}\n"
+             content += "-" * 50 + "\n"
+         
          # 如果是单个受试者的结果，添加详细结果
          if is_single_subject:
```

---

现在我需要确保在终端输出中也能显示相同的信息，让我修改 `print_final_results` 函数：

---

```diff
          print(f"预测标签: {data['pred']}")
          print(f"真实标签: {data['truth']}")
+         
+         # 添加预测与真实标签的详细对比
+         print("\n预测与真实标签的详细对比:")
+         print(f"{'索引':^6}|{'预测':^6}|{'真实':^6}|{'是否正确':^8}")
+         print("-"*30)
+         correct_count = 0
+         for i, (pred, truth) in enumerate(zip(data['pred'], data['truth'])):
+             is_correct = pred == truth
+             if is_correct:
+                 correct_count += 1
+             print(f"{i:^6}|{pred:^6}|{truth:^6}|{'✓' if is_correct else '✗':^8}")
+         
+         accuracy = correct_count / len(data['pred']) if len(data['pred']) > 0 else 0
+         print(f"\n准确率: {accuracy:.4f} ({correct_count}/{len(data['pred'])})")
          print("-"*50)
      
```

---

我已经成功修改了两个函数：

1. `send_training_results` 函数：添加了一个新的部分，显示每个受试者的最优预测结果和原始标签的对比，无论是单个受试者还是多个受试者的情况。

2. `print_final_results` 函数：添加了一个更详细的预测与真实标签对比表格，包括每个样本的预测结果、真实标签以及是否预测正确的标记，并计算准确率。

这些修改确保了在邮件通知和终端输出中都能清晰地看到最优预测结果与原始标签的对比，帮助您更好地分析模型的表现。

---

_**User**_

欢迎使用微表情识别模型训练系统
==================================================

请输入本次实验的主题名称（将用作实验结果文件夹名称）：
ces 

实验名称已设置为：ces

========== 训练配置信息 ==========
实验名称: ces
模型名称: SKD_TSTSAN
损失函数: FocalLoss_weighted
学习率: 0.001
批次大小: 32
最大迭代次数: 20
分类类别数: 5

数据扩充配置:
旋转角度范围: 3,8
使用镜像: True
各标签扩充倍数: 6,8,3,8,2
================================

开始训练...

训练过程出错: [Errno 2] No such file or directory: './Experiment_for_recognize/ces/log.txt'
/home/<USER>/data/miniconda3/envs/auwgcn/lib/python3.10/site-packages/numpy/core/fromnumeric.py:3504: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/home/<USER>/data/miniconda3/envs/auwgcn/lib/python3.10/site-packages/numpy/core/_methods.py:129: RuntimeWarning: invalid value encountered in scalar divide
  ret = ret.dtype.type(ret / rcount)

【实验结果报告】
================================================================================

================================================================================
【实验结果报告】
================================================================================

【基本信息】
实验名称: ces
时间: 2025-06-22 16:49:09
总训练时间: 00:00:00
数据集: CASME2_LOSO_full


【系统环境】
操作系统: Linux 6.8.0-1028-nvidia
处理器: 32核
内存: 63.3GB
GPU: NVIDIA A800 80GB PCIe (79.3GB)

【模型配置】
模型架构: SKD_TSTSAN
是否使用预训练: 是
预训练模型: SKD-TSTSAN.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
批次大小: 32
最大迭代次数: 20
损失函数: FocalLoss_weighted
随机种子: 1337

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: N/A
- UAR分数: N/A

【各表情类别准确率】
--------------------------------------------------


【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
    平均        nan        nan        nan    


【最优预测结果与原始标签对比】
--------------------------------------------------

================================================================================

================================================================================

【邮件通知】正在发送训练结果...

【邮件通知】邮件发送成功!
- 发件人: <EMAIL>
- 收件人: <EMAIL>
- 主题: [实验总结] ces - UF1=N/A, UAR=N/A
Traceback (most recent call last):
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN.py", line 424, in <module>
    logger = Logger(log_file_path)
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN_functions.py", line 270, in __init__
    self.log = open(filename, "a")
FileNotFoundError: [Errno 2] No such file or directory: './Experiment_for_recognize/ces/log.txt'



为什么


---

_**Assistant**_

<thinking>
从错误信息来看，问题出在日志文件的创建上。错误信息显示：

```
训练过程出错: [Errno 2] No such file or directory: './Experiment_for_recognize/ces/log.txt'
```

以及最后的错误堆栈：

```
Traceback (most recent call last):
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN.py", line 424, in <module>
    logger = Logger(log_file_path)
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN_functions.py", line 270, in __init__
    self.log = open(filename, "a")
FileNotFoundError: [Errno 2] No such file or directory: './Experiment_for_recognize/ces/log.txt'
```

这表明程序无法打开或创建日志文件 `./Experiment_for_recognize/ces/log.txt`，因为该路径不存在。

问题可能在于：
1. 目录 `./Experiment_for_recognize/ces/` 不存在
2. 程序没有权限在该目录下创建文件
3. 创建目录的代码没有正确执行

让我检查一下代码中创建实验目录的部分：

在 `train_classify_SKD_TSTSAN_functions.py` 的 `main_SKD_TSTSAN_with_Aug_with_SKD` 函数中有以下代码：

```python
# 创建实验目录
if (config.train):
    if not path.exists('./Experiment_for_recognize/' + config.exp_name):
        os.makedirs('./Experiment_for_recognize/' + config.exp_name)
        print(f'创建实验目录: {config.exp_name}')
```

但是，在 `train_classify_SKD_TSTSAN.py` 中，日志文件的路径是：

```python
log_file_path = './Experiment_for_recognize/' + config.exp_name + "/log.txt"
```

这里的问题可能是：
1. 创建目录的代码在 `main_SKD_TSTSAN_with_Aug_with_SKD` 函数内部
2. 但是日志文件的创建在调用该函数之前

查看 `train_classify_SKD_TSTSAN.py` 中的代码流程：

```python
try:
    # 设置日志文件
    log_file_path = './Experiment_for_recognize/' + config.exp_name + "/log.txt"
    logger = Logger(log_file_path)
    sys.stdout = logger
    
    results_dict = main_SKD_TSTSAN_with_Aug_with_SKD(config)
    # ...
```

可以看到，程序先尝试创建日志文件，然后再调用 `main_SKD_TSTSAN_with_Aug_with_SKD` 函数，而创建目录的代码在该函数内部。这就导致了尝试在不存在的目录中创建日志文件。

解决方案是：
1. 在创建日志文件之前，先检查并创建必要的目录
2. 或者将创建日志文件的代码移到 `main_SKD_TSTSAN_with_Aug_with_SKD` 函数内部，在创建目录之后

我将修改 `train_classify_SKD_TSTSAN.py` 文件，在创建日志文件之前先创建必要的目录。
</thinking>

问题出在代码执行顺序上。错误信息显示：

```
FileNotFoundError: [Errno 2] No such file or directory: './Experiment_for_recognize/ces/log.txt'
```

这是因为程序尝试在不存在的目录中创建日志文件。具体来说，程序先尝试创建日志文件，然后才创建实验目录，这导致了错误。

我需要修改代码，确保在创建日志文件之前先创建必要的目录：

---

```diff
      
      try:
+         # 先创建实验目录
+         exp_dir = './Experiment_for_recognize/' + config.exp_name
+         if not os.path.exists(exp_dir):
+             os.makedirs(exp_dir)
+             print(f'创建实验目录: {config.exp_name}')
+         
          # 设置日志文件
-         log_file_path = './Experiment_for_recognize/' + config.exp_name + "/log.txt"
+         log_file_path = exp_dir + "/log.txt"
          logger = Logger(log_file_path)
          sys.stdout = logger
```

---

现在我需要检查一下 `train_classify_SKD_TSTSAN_functions.py` 中的相关代码，确保不会有冲突：

---

Read file: train_classify_SKD_TSTSAN_functions.py

---

Read file: train_classify_SKD_TSTSAN_functions.py

---

我还需要修改 `train_classify_SKD_TSTSAN_functions.py` 中的代码，确保不会重复创建日志文件：

---

```diff
          print(f"\n【损失函数配置】使用{config.loss_function}")
  
-     # 创建实验目录
-     if (config.train):
-         if not path.exists('./Experiment_for_recognize/' + config.exp_name):
-             os.makedirs('./Experiment_for_recognize/' + config.exp_name)
-             print(f'创建实验目录: {config.exp_name}')
- 
      # 复制相关代码文件到实验目录
      current_file = os.path.abspath(__file__)
      shutil.copy(current_file, './Experiment_for_recognize/' + config.exp_name)
      shutil.copy("./all_model.py", './Experiment_for_recognize/' + config.exp_name)
  
-     # 设置日志文件
-     log_file_path = './Experiment_for_recognize/' + config.exp_name + "/log.txt"
-     sys.stdout = Logger(log_file_path)
      print('开始训练...')
  
```

---

现在我还需要修改 `main_SKD_TSTSAN_with_Aug_with_SKD` 函数，确保为每个受试者创建必要的目录：

---

```diff
              log_path = './Experiment_for_recognize/' + config.exp_name + '/' + n_subName + '/' + "logs"
  
+             # 确保受试者目录存在
+             subject_dir = './Experiment_for_recognize/' + config.exp_name + '/' + n_subName
+             if not os.path.exists(subject_dir):
+                 os.makedirs(subject_dir)
+                 print(f'创建受试者目录: {n_subName}')
+             
+             # 确保日志目录存在
+             if not os.path.exists(log_path):
+                 os.makedirs(log_path)
+                 print(f'创建日志目录: {log_path}')
+ 
              writer = SummaryWriter(log_path)
  
```

---

我已经修复了导致错误的问题：

1. 在 `train_classify_SKD_TSTSAN.py` 中：
   - 添加了代码在创建日志文件之前先创建实验目录
   - 这确保了在尝试创建日志文件之前，目录已经存在

2. 在 `train_classify_SKD_TSTSAN_functions.py` 中：
   - 移除了重复的日志文件创建代码，避免冲突
   - 添加了代码确保为每个受试者创建必要的目录

这些修改解决了 "No such file or directory" 错误，确保程序能够正确创建和访问日志文件。现在，当你运行程序时，它应该能够正确创建目录结构并写入日志文件，而不会出现之前的错误。

错误发生的原因是代码执行顺序问题：原来的代码先尝试创建日志文件，然后才创建目录，导致了路径不存在的错误。现在的修改确保了在创建任何文件之前，必要的目录结构已经存在。

---

_**User**_

================================================================================
【实验结果报告】
================================================================================

【基本信息】
实验名称: ceshi
时间: 2025-06-22 17:00:18
总训练时间: 00:09:53
数据集: CASME2_LOSO_full


【系统环境】
操作系统: Linux 6.8.0-1028-nvidia
处理器: 32核
内存: 63.3GB
GPU: NVIDIA A800 80GB PCIe (79.3GB)

【模型配置】
模型架构: SKD_TSTSAN
是否使用预训练: 是
预训练模型: SKD-TSTSAN.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
批次大小: 32
最大迭代次数: 20
损失函数: FocalLoss_weighted
随机种子: 1337

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: 0.4327
- UAR分数: 0.5407

【各表情类别准确率】
--------------------------------------------------
- 快乐: 0.2703
- 惊讶: 0.9333
- 厌恶: 0.6538
- 压抑: 0.8462
- 其他: 0.0000


【各受试者评估结果】
--------------------------------------------------
受试者 sub01: UF1=0.1333, UAR=0.2000
受试者 sub02: UF1=0.3455, UAR=0.3600
受试者 sub03: UF1=0.2400, UAR=0.2500
受试者 sub04: UF1=0.1333, UAR=0.2000
受试者 sub05: UF1=0.1600, UAR=0.1714
受试者 sub06: UF1=0.1818, UAR=0.2000
受试者 sub07: UF1=0.1333, UAR=0.1200
受试者 sub08: UF1=0.1333, UAR=0.2000
受试者 sub09: UF1=0.2338, UAR=0.2500
受试者 sub10: UF1=0.0000, UAR=0.0000
受试者 sub11: UF1=0.1200, UAR=0.1500
受试者 sub12: UF1=0.4985, UAR=0.5000
受试者 sub13: UF1=0.1333, UAR=0.1000
受试者 sub15: UF1=0.3600, UAR=0.4000
受试者 sub16: UF1=0.0000, UAR=0.0000
受试者 sub17: UF1=0.2241, UAR=0.2667
受试者 sub18: UF1=0.0000, UAR=0.0000
受试者 sub19: UF1=0.3946, UAR=0.4667
受试者 sub20: UF1=0.0000, UAR=0.0000
受试者 sub21: UF1=0.0000, UAR=0.0000
受试者 sub22: UF1=0.2000, UAR=0.2000
受试者 sub23: UF1=0.3800, UAR=0.3667
受试者 sub24: UF1=0.1600, UAR=0.1333
受试者 sub25: UF1=0.3818, UAR=0.4000
受试者 sub26: UF1=0.2300, UAR=0.2500
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率   
--------------------------------------------------
  sub01      0.1333     0.2000     0.2500 
  sub02      0.3455     0.3600     0.7000 
  sub03      0.2400     0.2500     0.3750 
  sub04      0.1333     0.2000     0.5000 
  sub05      0.1600     0.1714     0.4800 
  sub06      0.1818     0.2000     0.5556 
  sub07      0.1333     0.1200     0.3750 
  sub08      0.1333     0.2000     0.5000 
  sub09      0.2338     0.2500     0.6000 
  sub10      0.0000     0.0000     0.0000 
  sub11      0.1200     0.1500     0.3750 
  sub12      0.4985     0.5000     0.8571 
  sub13      0.1333     0.1000     0.2500 
  sub15      0.3600     0.4000     0.8333 
  sub16      0.0000     0.0000     0.0000 
  sub17      0.2241     0.2667     0.5000 
  sub18      0.0000     0.0000     0.0000 
  sub19      0.3946     0.4667     0.7200 
  sub20      0.0000     0.0000     0.0000 
  sub21      0.0000     0.0000     0.0000 
  sub22      0.2000     0.2000     1.0000 
  sub23      0.3800     0.3667     0.4286 
  sub24      0.1600     0.1333     0.2500 
  sub25      0.3818     0.4000     0.8000 
  sub26      0.2300     0.2500     0.5882 
--------------------------------------------------
    平均       0.1911     0.2074     0.4375 


【最优预测结果与原始标签对比】
--------------------------------------------------

受试者 sub01:
预测标签: [3, 3, 2, 2, 3, 3, 2, 2]
真实标签: [4, 4, 4, 4, 0, 0, 2, 2]
--------------------------------------------------

受试者 sub02:
预测标签: [3, 1, 1, 1, 3, 3, 3, 0, 3, 3]
真实标签: [4, 1, 1, 1, 0, 3, 3, 3, 3, 3]
--------------------------------------------------

受试者 sub03:
预测标签: [3, 1, 1, 1, 3, 0, 3, 2]
真实标签: [4, 4, 1, 1, 2, 2, 2, 2]
--------------------------------------------------

受试者 sub04:
预测标签: [2, 2, 2, 2]
真实标签: [4, 4, 2, 2]
--------------------------------------------------

受试者 sub05:
预测标签: [2, 1, 2, 2, 3, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 3, 1, 1, 1, 1]
真实标签: [4, 4, 4, 4, 4, 4, 4, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 2]
--------------------------------------------------

受试者 sub06:
预测标签: [1, 1, 1, 1, 1, 1, 3, 3, 0]
真实标签: [4, 1, 1, 1, 1, 1, 0, 0, 2]
--------------------------------------------------

受试者 sub07:
预测标签: [2, 0, 0, 2, 2, 2, 0, 0]
真实标签: [4, 4, 4, 2, 2, 2, 2, 2]
--------------------------------------------------

受试者 sub08:
预测标签: [3, 3]
真实标签: [4, 3]
--------------------------------------------------

受试者 sub09:
预测标签: [2, 3, 0, 3, 3, 3, 3, 3, 3, 3]
真实标签: [4, 0, 0, 0, 0, 3, 3, 3, 3, 3]
--------------------------------------------------

受试者 sub10:
预测标签: [2, 2, 2, 0, 2, 2, 2, 2, 2, 2, 2, 0, 3]
真实标签: [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4]
--------------------------------------------------

受试者 sub11:
预测标签: [2, 2, 3, 2, 2, 0, 2, 2]
真实标签: [4, 4, 4, 4, 2, 2, 2, 2]
--------------------------------------------------

受试者 sub12:
预测标签: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 2, 2, 2, 2, 2, 2]
真实标签: [4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 2, 2, 2, 2, 2]
--------------------------------------------------

受试者 sub13:
预测标签: [1, 2, 0, 1]
真实标签: [4, 4, 0, 0]
--------------------------------------------------

受试者 sub15:
预测标签: [1, 1, 1, 0, 0, 0]
真实标签: [1, 1, 1, 0, 0, 2]
--------------------------------------------------

受试者 sub16:
预测标签: [3, 3, 1]
真实标签: [4, 0, 0]
--------------------------------------------------

受试者 sub17:
预测标签: [3, 3, 3, 3, 3, 3, 3, 3, 3, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
真实标签: [4, 1, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
--------------------------------------------------

受试者 sub18:
预测标签: [2, 2, 3]
真实标签: [4, 4, 4]
--------------------------------------------------

受试者 sub19:
预测标签: [2, 0, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 2, 2, 1, 1, 2, 2, 2]
真实标签: [4, 4, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 2, 2, 2]
--------------------------------------------------

受试者 sub20:
预测标签: [3, 0, 0, 0, 0, 0, 0, 0]
真实标签: [4, 4, 4, 4, 4, 4, 2, 2]
--------------------------------------------------

受试者 sub21:
预测标签: [0, 0]
真实标签: [4, 3]
--------------------------------------------------

受试者 sub22:
预测标签: [3, 3]
真实标签: [3, 3]
--------------------------------------------------

受试者 sub23:
预测标签: [2, 0, 2, 3, 3, 1, 1]
真实标签: [4, 0, 2, 2, 3, 3, 3]
--------------------------------------------------

受试者 sub24:
预测标签: [3, 3, 3, 1, 1, 3, 0, 3]
真实标签: [4, 4, 4, 1, 1, 1, 2, 2]
--------------------------------------------------

受试者 sub25:
预测标签: [0, 1, 1, 1, 1, 1, 1, 2, 2, 2]
真实标签: [4, 4, 1, 1, 1, 1, 1, 2, 2, 2]
--------------------------------------------------

受试者 sub26:
预测标签: [0, 2, 0, 0, 0, 3, 3, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]
真实标签: [4, 4, 4, 4, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2]
--------------------------------------------------


看一下整体上评价指标计算是否正确

---

_**Assistant**_

<thinking>
我需要检查评价指标的计算是否正确。从实验结果报告中，我可以看到以下关键信息：

1. 总体性能：
   - UF1分数: 0.4327
   - UAR分数: 0.5407

2. 各受试者的UF1和UAR值，以及它们的平均值：
   - 平均UF1: 0.1911
   - 平均UAR: 0.2074

3. 各表情类别准确率：
   - 快乐: 0.2703
   - 惊讶: 0.9333
   - 厌恶: 0.6538
   - 压抑: 0.8462
   - 其他: 0.0000

这里存在一个明显的不一致：总体性能的UF1和UAR值（0.4327和0.5407）与各受试者平均值（0.1911和0.2074）相差较大。这可能表明计算方法存在问题。

让我检查一下代码中的评价指标计算逻辑：

在`train_classify_SKD_TSTSAN_functions.py`中，`recognition_evaluation`函数负责计算UF1和UAR。这个函数将多分类问题转换为多个二分类问题（one-vs-rest），然后对每个类别计算F1分数和召回率，最后取平均值。

在`main_SKD_TSTSAN_with_Aug_with_SKD`函数中，有两种计算方式：
1. 对每个受试者单独计算UF1和UAR
2. 将所有受试者的预测结果和真实标签合并后，再计算总体的UF1和UAR

让我检查一下这两种计算方式是否一致：

```python
# 对每个受试者单独计算
current_UF1, current_UAR = recognition_evaluation(dataset_name, temp_y, temp_best_each_subject_pred, show=True)

# 更新累积结果
total_pred.extend(temp_best_each_subject_pred)
total_gt.extend(temp_y)

# 计算总体评估结果
UF1, UAR = recognition_evaluation(dataset_name, total_gt, total_pred, show=False)
```

这两种计算方式在数学上是不同的：
- 第一种是先计算每个受试者的指标，然后取平均
- 第二种是先合并所有受试者的数据，然后计算总

---

