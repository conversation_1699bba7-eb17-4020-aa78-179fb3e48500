<!-- Generated by SpecStory -->

# Untitled (2025-07-08 01:50Z)

_**User**_

train_classify_SKD_TSTSAN.py
train_classify_SKD_TSTSAN_functions.py
evaluate_results.py
casme2csv统计.py
casme2真实标签.py
casme2数据提取.py
all_model.py
预处理5casme2class5loss数据分析.py
预处理4loso.py
预处理3统计数据.py
预处理2光流optflow_for_classify.py
预处理1prepare_casme2_data数据准备.py
预处理0裁切.py
后处理评价指标计算evaluate_results.py
文件树.py


压缩到预处理文件夹

---

_**Assistant**_

我将帮助您将预处理相关的文件整理到一个预处理文件夹中。让我们先创建文件夹并移动相关文件。

首先，我会创建一个预处理文件夹：

---

