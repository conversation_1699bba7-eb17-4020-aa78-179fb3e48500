<!-- Generated by SpecStory -->

# Micro-expression recognition training framework overview (2025-06-21 13:37:48)

_**User**_

@train_classify_SKD_TSTSAN_functions.py @train_classify_SKD_TSTSAN.py 

---

_**Assistant**_

```thinking
让我分析一下这两个文件的主要功能和结构:

1. 整体架构:
- train_classify_SKD_TSTSAN.py 是主入口文件,负责参数解析和训练流程控制
- train_classify_SKD_TSTSAN_functions.py 包含具体的训练和评估函数实现

2. 主要功能:
- 这是一个用于微表情识别的深度学习训练框架
- 使用SKD-TSTSAN模型架构
- 支持知识蒸馏训练
- 包含数据增强功能
- 支持LOSO(Leave-One-Subject-Out)交叉验证
- 提供可视化和邮件通知功能

3. 关键组件:
- 数据增强模块:支持旋转、镜像等增强方式
- 知识蒸馏:使用温度参数和多个损失函数
- 评估指标:UF1和UAR分数
- 可视化:支持Visdom可视化训练过程
- 日志记录:支持终端和文件双重输出

4. 训练流程:
- 按照预定义顺序处理每个受试者
- 对每个受试者进行训练和评估
- 支持预训练模型加载
- 提供详细的训练状态监控

让我系统地介绍这个框架的核心功能和特点。
```

这是一个用于微表情识别的深度学习训练框架,具有以下主要特点和功能:

1. 系统架构设计
- 采用模块化设计,将参数配置、训练逻辑、工具函数等清晰分离
- 主文件负责参数解析和训练流程控制,函数文件实现具体算法
- 支持灵活的配置管理,包括训练参数、模型参数、数据增强参数等

2. 核心训练功能
- 实现了基于SKD-TSTSAN的微表情识别模型
- 支持知识蒸馏训练策略,通过温度参数和多个损失函数优化模型
- 采用LOSO(Leave-One-Subject-Out)交叉验证评估模型性能
- 提供多种损失函数选择,包括交叉熵损失和Focal Loss

3. 数据增强机制
- 支持多种数据增强策略:旋转、镜像等
- 可针对不同情绪类别设置不同的增强倍数
- 提供测试时数据增强和结果投票机制
- 包含详细的增强效果统计功能

4. 评估与监控
- 使用UF1和UAR作为主要评估指标
- 支持混淆矩阵分析和各类别准确率统计
- 提供训练过程可视化(Visdom)
- 包含详细的日志记录系统

5. 辅助功能
- 支持模型权重的保存与加载
- 提供邮件通知功能,可发送训练结果
- 包含系统信息收集和环境检测
- 支持训练过程的断点续训

6. 特色功能
- 支持COCO预训练模型加载
- 实现了自适应的数据增强策略
- 提供详细的受试者级别评估结果
- 包含丰富的调试和监控工具

这个框架设计完整,功能丰富,特别适合进行微表情识别相关的研究工作。它不仅提供了核心的训练功能,还包含了大量辅助工具,可以帮助研究人员更好地开展实验和分析结果。

需要注意的几个关键点:
1. 数据增强策略需要根据具体任务调整
2. 知识蒸馏参数的选择对模型性能有重要影响
3. 评估指标的计算需要确保数据分布的平衡
4. 可视化和监控功能可以帮助及时发现训练问题

这个框架体现了严谨的学术研究特点,同时也具有良好的实用性和可扩展性。

---

_**User**_

什么情况

---

_**Assistant**_

```thinking
让我分析一下这个错误情况:

1. 错误现象:
- 每个情绪类别(happiness, surprise, disgust, repression, others)都报同样的错误
- 错误信息是"not enough values to unpack (expected 4, got 1)"
- 这个错误出现在计算混淆矩阵时

2. 错误原因分析:
- 这个错误发生在recognition_evaluation函数中
- 具体是在计算混淆矩阵时,confusion_matrix.ravel()无法获得预期的4个值(TN, FP, FN, TP)
- 这说明混淆矩阵的维度不是2x2,而是1x1
- 这种情况通常发生在某个类别的样本数量过少或预测结果过于单一时

3. 代码中的相关逻辑:
- 函数会将多分类问题转换为二分类(one-vs-rest)来评估每个类别
- 如果某个类别的样本太少或预测结果单一,就会导致混淆矩阵退化为1x1

4. 有趣的现象:
- 尽管每个类别单独计算时都报错
- 但最终还是得到了整体的UF1(0.4555)和UAR(0.5176)
- 这说明错误被正确捕获和处理了
```

这个错误情况并不会影响最终的训练结果,因为代码中已经包含了错误处理机制。但它确实反映了一些潜在的问题:

1. 数据分布问题
- 可能某些类别的样本数量太少
- 或者模型对某些类别的预测过于集中

2. 评估方法的局限
- 当前的评估方法在处理极不平衡的数据时可能不够稳健
- 二分类转换策略在某些情况下可能不太合适

建议采取以下改进措施:

1. 数据层面:
- 检查各个类别的样本分布
- 考虑使用更均衡的数据增强策略
- 可能需要调整数据采样方式

2. 评估方法:
- 考虑添加更多的评估指标
- 改进混淆矩阵的计算方法
- 增加对极端情况的处理

3. 代码优化:
- 增加更详细的错误信息输出
- 添加数据分布的监控
- 完善评估过程的日志记录

这个错误本身不会影响模型的训练和整体性能评估,但建议进行上述改进以提高系统的稳定性和可靠性。要不要我帮你具体分析一下代码中的相关部分,看看如何优化?

---

_**User**_

@train_classify_SKD_TSTSAN.py @train_classify_SKD_TSTSAN_functions.py email发送了吗？输出提升

---

_**Assistant**_

```thinking
让我分析一下邮件发送和输出相关的代码:

1. 邮件发送功能:
- 在train_classify_SKD_TSTSAN.py中通过send_training_results函数实现
- 需要配置以下参数:
  - email_notify: 是否启用邮件通知(默认False)
  - email_sender: 发件人邮箱(默认为空)
  - email_receiver: 收件人邮箱(默认为空)
  - email_password: 发件人邮箱密码(默认为空)

2. 输出提升:
- 目前的输出主要包括:
  - 训练过程信息
  - 评估结果
  - 系统信息
  - 配置信息
- 可以优化输出格式,使其更清晰易读

让我帮你改进这两个方面。
```

1. 要启用邮件发送功能,需要在运行时添加以下参数:

```bash
python train_classify_SKD_TSTSAN.py \
  --email_notify=True \
  --email_sender="<EMAIL>" \
  --email_receiver="<EMAIL>" \
  --email_password="your_password" \
  --smtp_server="smtp.example.com" \
  --smtp_port=587
```

2. 为了提升输出的可读性,我建议添加以下代码:

---

```diff
          results_dict: 包含训练结果的字典
      """
+     print("\n" + "="*80)
+     print("【训练开始】")
+     print("="*80)
+     
      learning_rate = config.learning_rate
      batch_size = config.batch_size
```

```diff
      if is_cuda:
          device = torch.device('cuda')
-         print('使用GPU进行训练')
+         print('\n【硬件信息】使用GPU进行训练')
      else:
          device = torch.device('cpu')
-         print('使用CPU进行训练')
+         print('\n【硬件信息】使用CPU进行训练')
  
      # 根据配置选择损失函数
```

```diff
          sum_reciprocal = sum(1 / num for num in numbers)
          weights = [(1 / num) / sum_reciprocal for num in numbers]
+         print("\n【损失函数配置】")
+         print(f"使用加权Focal Loss")
+         print(f"类别权重: {[f'{w:.4f}' for w in weights]}")
  
          loss_fn = get_loss_function(config.loss_function, torch.tensor(weights).to(device))
      else:
          loss_fn = get_loss_function(config.loss_function)
+         print(f"\n【损失函数配置】使用{config.loss_function}")
  
      # 创建实验目录
```

```diff
                  continue
              
-             print(f'========== 当前处理主体: {n_subName} ==========')
+             print(f"\n{'='*40}")
+             print(f"【当前处理受试者】: {n_subName}")
+             print(f"{'='*40}")
+             
              # 确定当前主体所属的组
              if n_subName in LARGE_SUBJECTS:
-                 print('大样本组(>12个样本)')
+                 print('组别: 大样本组(>12个样本)')
              elif n_subName in MEDIUM_SUBJECTS:
-                 print('中等样本组(7-12个样本)')
+                 print('组别: 中等样本组(7-12个样本)')
              elif n_subName in SMALL_SUBJECTS:
-                 print('小样本组(3-6个样本)')
+                 print('组别: 小样本组(3-6个样本)')
              else:
-                 print('极小样本组(<=3个样本)')
+                 print('组别: 极小样本组(<=3个样本)')
  
              X_train = []
```

```diff
  
              # 在每个subject处理完后打印统计
-             print("\n数据增强统计:")
+             print("\n【数据增强统计】")
              for label, count in aug_stats.items():
                  print(f"标签 {label}: 增强了 {count} 个样本")
```

---

3. 为了进一步优化邮件发送功能,我们再添加一些改进:

---

```diff
  def send_training_results(config, results_dict, total_time):
      """发送训练结果邮件"""
-     try:
+     if not config.email_notify:
+         print("\n【邮件通知】邮件通知功能未启用")
+         return
+         
+     if not all([config.email_sender, config.email_receiver, config.email_password]):
+         print("\n【邮件通知】邮件配置不完整,请检查以下参数:")
+         print("- email_sender")
+         print("- email_receiver")
+         print("- email_password")
+         return
+         
+     try:
+         print("\n【邮件通知】正在发送训练结果...")
          sys_info = get_system_info()
          brief_results, detailed_results = format_subject_results(results_dict)
```

```diff
          smtp = smtplib.SMTP(config.smtp_server, config.smtp_port)
          smtp.starttls()
-         smtp.login(config.email_sender, config.email_password)
-         smtp.sendmail(config.email_sender, [config.email_receiver], message.as_string())
-         smtp.quit()
-         
-         print(f"\n邮件发送成功! 接收邮箱: {config.email_receiver}")
-         
+         
+         try:
+             smtp.login(config.email_sender, config.email_password)
+         except smtplib.SMTPAuthenticationError:
+             print("【错误】邮箱登录失败,请检查邮箱和密码是否正确")
+             return
+         except Exception as e:
+             print(f"【错误】邮箱登录时发生未知错误: {str(e)}")
+             return
+             
+         try:
+             smtp.sendmail(config.email_sender, [config.email_receiver], message.as_string())
+             print(f"\n【邮件通知】邮件发送成功!")
+             print(f"- 发件人: {config.email_sender}")
+             print(f"- 收件人: {config.email_receiver}")
+             print(f"- 主题: {subject}")
+         except Exception as e:
+             print(f"【错误】邮件发送失败: {str(e)}")
+         finally:
+             smtp.quit()
+             
      except Exception as e:
-         print(f"\n邮件发送失败: {str(e)}")
+         print(f"\n【错误】邮件处理过程出错: {str(e)}")
  
  
```

---

现在,要使用邮件功能和改进的输出,你需要:

1. 运行时添加邮件配置:
```bash
python train_classify_SKD_TSTSAN.py \
  --email_notify=True

---

_**User**_

    parser.add_argument('--email_notify', type=strtobool, default=True, help='是否发送邮件通知')
    parser.add_argument('--email_sender', type=str, default="<EMAIL>", help='发件人邮箱')
    parser.add_argument('--email_password', type=str, default="njnqnnamaddpbdhb", help='发件人邮箱密码/授权码')
    parser.add_argument('--email_receiver', type=str, default="<EMAIL>", help='收件人邮箱')
    parser.add_argument('--smtp_server', type=str, default="smtp.qq.com", help='SMTP服务器')
    parser.add_argument('--smtp_port', type=int, default=587, help='SMTP端口')

---

_**Assistant**_

```thinking
我明白了,你想要修改邮件配置的默认参数。这些参数是针对QQ邮箱的配置:

1. 邮件参数分析:
- email_sender: QQ邮箱地址(<EMAIL>)
- email_receiver: 同样是QQ邮箱
- email_password: 这是QQ邮箱的授权码而不是登录密码
- smtp_server: QQ邮箱的SMTP服务器(smtp.qq.com)
- smtp_port: 587端口(QQ邮箱支持)

2. 需要修改的内容:
- 将这些参数设置为默认值
- 确保email_notify默认为True
- 使用QQ邮箱的SMTP配置

让我帮你修改代码。
```

让我帮你修改邮件配置参数:

---

```diff
  
      # 添加邮件通知相关参数
-     parser.add_argument('--email_notify', type=strtobool, default=False, help='是否启用邮件通知')
-     parser.add_argument('--email_sender', type=str, default='', help='发件人邮箱')
-     parser.add_argument('--email_password', type=str, default='', help='发件人邮箱密码')
-     parser.add_argument('--email_receiver', type=str, default='', help='收件人邮箱')
-     parser.add_argument('--smtp_server', type=str, default='smtp.gmail.com', help='SMTP服务器地址')
-     parser.add_argument('--smtp_port', type=int, default=587, help='SMTP服务器端口')
+     parser.add_argument('--email_notify', type=strtobool, default=True, help='是否发送邮件通知')
+     parser.add_argument('--email_sender', type=str, default="<EMAIL>", help='发件人邮箱')
+     parser.add_argument('--email_password', type=str, default="njnqnnamaddpbdhb", help='发件人邮箱密码/授权码')
+     parser.add_argument('--email_receiver', type=str, default="<EMAIL>", help='收件人邮箱')
+     parser.add_argument('--smtp_server', type=str, default="smtp.qq.com", help='SMTP服务器')
+     parser.add_argument('--smtp_port', type=int, default=587, help='SMTP端口')
  
      # 解析命令行参数
```

---

现在邮件配置已经更新为QQ邮箱的默认设置:

1. 默认启用邮件通知(email_notify=True)
2. 使用QQ邮箱的SMTP服务器配置:
   - smtp_server: smtp.qq.com
   - smtp_port: 587
3. 设置了默认的发件人和收件人邮箱
4. 配置了QQ邮箱的授权码

这样配置后,程序会自动:
1. 启用邮件通知功能
2. 使用配置的QQ邮箱发送训练结果
3. 在训练完成或发生错误时自动发送通知邮件

你不需要在运行时额外指定这些参数,除非你想要使用不同的配置。如果想要使用不同的邮箱或配置,可以在运行时通过命令行参数覆盖这些默认值。

需要注意的是:
1. 确保QQ邮箱已开启SMTP服务
2. 授权码需要在QQ邮箱的设置中获取
3. 如果更换邮箱,需要相应修改SMTP服务器和端口配置

现在你可以直接运行程序,它会自动使用这些邮件配置。要测试一下吗?

---

_**User**_

@train_classify_SKD_TSTSAN.py @train_classify_SKD_TSTSAN_functions.py 
邮件发送的内容 终端也全部显示一遍

---

_**Assistant**_

```thinking
我理解你想要在终端也显示邮件发送的内容。我们需要:

1. 修改send_training_results函数:
- 先生成邮件内容
- 在发送邮件前,在终端显示完整内容
- 保持邮件发送功能不变

2. 输出格式:
- 使用清晰的分隔符
- 添加时间戳
- 保持格式一致性

3. 关键点:
- 复用已有的format_subject_results函数
- 确保终端输出和邮件内容完全一致
- 使用更友好的格式化显示
```

让我修改代码来实现这个功能:

---

```diff
  
  def send_training_results(config, results_dict, total_time):
-     """发送训练结果邮件"""
+     """发送训练结果邮件并在终端显示内容"""
      if not config.email_notify:
          print("\n【邮件通知】邮件通知功能未启用")
```

```diff
          
      try:
-         print("\n【邮件通知】正在发送训练结果...")
          sys_info = get_system_info()
          brief_results, detailed_results = format_subject_results(results_dict)
```

```diff
          uar_value = format_metric(results_dict.get('UAR'))
          
+         # 生成内容
          content = f"""
  {'='*80}
- 实验名称: {config.exp_name}
+ 【实验结果报告】
  {'='*80}
  
- 【实验时间信息】
+ 【基本信息】
+ 实验名称: {config.exp_name}
  时间: {current_time}
  {'受试者训练时间' if is_single_subject else '总训练时间'}: {format_time(total_time)}
```

```diff
          
          content += f"\n{'='*80}\n"
-         content += "注: 此为自动发送的实验结果通知邮件,请勿回复。"
+         
+         # 在终端显示内容
+         print("\n【实验结果报告】")
+         print("="*80)
+         print(content)
+         print("="*80)
          
          # 构建邮件
```

```diff
          message['From'] = config.email_sender
          message['To'] = config.email_receiver
+         
+         print("\n【邮件通知】正在发送训练结果...")
          
          # 发送邮件
```

---



---

