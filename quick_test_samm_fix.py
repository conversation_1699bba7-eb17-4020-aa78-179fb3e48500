#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SAMM路径修复快速验证脚本
======================

验证SAMM数据集路径修复是否成功

使用方法：
python quick_test_samm_fix.py

作者: Augment Agent
日期: 2025-08-03
"""

import os
import sys
import subprocess

def test_samm_path_fix():
    """测试SAMM路径修复"""
    print("🔍 测试SAMM数据集路径修复...")
    
    # 检查数据路径
    samm_path = "/home/<USER>/data/ajq/SKD-TSTSAN-data/SAMM_LOSO_full"
    if not os.path.exists(samm_path):
        print(f"❌ SAMM数据路径不存在: {samm_path}")
        return False
    
    print(f"✅ SAMM数据路径存在: {samm_path}")
    
    # 检查受试者数据
    subjects = [d for d in os.listdir(samm_path) if d.isdigit()]
    subjects.sort()
    print(f"✅ 找到 {len(subjects)} 个受试者")
    print(f"✅ 前5个受试者: {subjects[:5]}")
    
    # 检查受试者011的数据结构
    subject_011_path = os.path.join(samm_path, "011")
    if os.path.exists(subject_011_path):
        print(f"✅ 受试者011数据存在: {subject_011_path}")
        
        # 检查train和test目录
        train_path = os.path.join(subject_011_path, "train")
        test_path = os.path.join(subject_011_path, "test")
        
        if os.path.exists(train_path):
            print(f"✅ 训练数据目录存在")
            train_emotions = os.listdir(train_path)
            print(f"   训练情绪类别: {sorted(train_emotions)}")
        
        if os.path.exists(test_path):
            print(f"✅ 测试数据目录存在")
            test_emotions = os.listdir(test_path)
            print(f"   测试情绪类别: {sorted(test_emotions)}")
    else:
        print(f"❌ 受试者011数据不存在")
        return False
    
    return True

def test_gpu_preloader_import():
    """测试GPU预加载器导入"""
    print("\n🚀 测试GPU预加载器导入...")
    
    try:
        sys.path.append('.')
        from train_classify_SKD_TSTSAN_functions_SAMM import GPUDataPreloader
        print("✅ GPU预加载器导入成功")
        
        # 测试初始化
        samm_path = "/home/<USER>/data/ajq/SKD-TSTSAN-data/SAMM_LOSO_full"
        preloader = GPUDataPreloader(samm_path, class_num=3, device='cpu')  # 使用CPU避免GPU占用
        print("✅ GPU预加载器初始化成功")
        
        return True
    except ImportError as e:
        print(f"❌ GPU预加载器导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ GPU预加载器测试失败: {e}")
        return False

def test_training_script_syntax():
    """测试训练脚本语法"""
    print("\n📝 测试训练脚本语法...")
    
    try:
        # 使用python -m py_compile检查语法
        result = subprocess.run([
            sys.executable, "-m", "py_compile", "train_classify_SKD_TSTSANSAMM.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 主训练脚本语法正确")
        else:
            print(f"❌ 主训练脚本语法错误: {result.stderr}")
            return False
        
        # 检查functions文件
        result = subprocess.run([
            sys.executable, "-m", "py_compile", "train_classify_SKD_TSTSAN_functions_SAMM.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 训练函数文件语法正确")
        else:
            print(f"❌ 训练函数文件语法错误: {result.stderr}")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 语法检查失败: {e}")
        return False

def test_argument_parsing():
    """测试参数解析"""
    print("\n⚙️ 测试参数解析...")
    
    try:
        # 测试help参数
        result = subprocess.run([
            sys.executable, "train_classify_SKD_TSTSANSAMM.py", "--help"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and "--use_gpu_preload" in result.stdout:
            print("✅ 参数解析正常，GPU预加载参数存在")
            return True
        else:
            print(f"❌ 参数解析异常")
            return False
    except subprocess.TimeoutExpired:
        print("⚠️ 参数解析测试超时，但这可能是正常的")
        return True
    except Exception as e:
        print(f"❌ 参数解析测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 SAMM路径修复验证测试")
    print("=" * 50)
    
    tests = [
        ("SAMM数据路径", test_samm_path_fix),
        ("GPU预加载器导入", test_gpu_preloader_import),
        ("训练脚本语法", test_training_script_syntax),
        ("参数解析", test_argument_parsing),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 结果汇总
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总结: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！SAMM路径修复成功")
        print("\n📋 下一步:")
        print("  1. 可以正常运行SAMM训练脚本")
        print("  2. GPU预加载功能已可用")
        print("  3. 所有新增功能已就绪")
    else:
        print(f"⚠️ {total - passed} 个测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
