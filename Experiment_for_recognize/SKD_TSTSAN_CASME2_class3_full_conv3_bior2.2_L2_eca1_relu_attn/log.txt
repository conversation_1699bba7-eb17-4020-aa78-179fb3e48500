日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (3分类)[0m
[1;96m🎯 类别权重: ['0.3794', '0.1349', '0.4857'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub17测试标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m🔥 启用三模态特征融合:[0m
  [1;96m多头注意力融合[0m 🧠
  [96m- 建立跨模态注意力关系[0m 🔗
  [96m- 动态关注重要特征[0m 🎯
  [1;96m- 注意力头数: 8[0m 🎯
[93m⏰ 使用固定训练轮数[0m

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

[1m[1;96m📊 损失输出配置[0m
[1;96m----------[0m
[1;96m🔍 详细损失信息: 禁用[0m
[1;96m📈 Epoch损失统计: 禁用[0m
[1;96m📉 验证损失统计: 禁用[0m
[1;96m🚨 过拟合监控: 禁用[0m
[1;96m📦 批次损失详情: 禁用[0m
[1;96m❌ Epoch损失统计将显示: 否[0m
[1;96m❌ 验证损失统计将显示: 否[0m
[1;96m❌ 过拟合监控将显示: 否[0m

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 953 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/953[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 1 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7061[0m (466/660)
   • 中间层1准确率: [96m0.6697[0m (442/660)
   • 中间层2准确率: [96m0.6803[0m (449/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 1 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6774[0m (21/31)
🏆 [1m最佳准确率:[0m [1;93m0.0000[0m
📊 [1mUF1:[0m [1;92m0.7500[0m | [1mUAR:[0m [1;92m0.7888[0m
🥇 [1m最佳UF1:[0m [1;93m0.0000[0m | [1m最佳UAR:[0m [1;93m0.0000[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.6774 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.7500 | UAR: 0.7888 | 准确率: 0.6774[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth

[1m[1;96m📅 Epoch 2/953[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 2 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9152[0m (604/660)
   • 中间层1准确率: [96m0.8833[0m (583/660)
   • 中间层2准确率: [96m0.9167[0m (605/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 2 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5484[0m (17/31)
🏆 [1m最佳准确率:[0m [1;93m0.6774[0m
📊 [1mUF1:[0m [1;96m0.4128[0m | [1mUAR:[0m [1;96m0.6315[0m
🥇 [1m最佳UF1:[0m [1;93m0.7500[0m | [1m最佳UAR:[0m [1;93m0.7888[0m
============================================================


[1m[1;96m📅 Epoch 3/953[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 3 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9424[0m (622/660)
   • 中间层1准确率: [96m0.9106[0m (601/660)
   • 中间层2准确率: [96m0.9364[0m (618/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 3 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.8065[0m (25/31)
🏆 [1m最佳准确率:[0m [1;93m0.6774[0m
📊 [1mUF1:[0m [1;96m0.5121[0m | [1mUAR:[0m [1;96m0.5280[0m
🥇 [1m最佳UF1:[0m [1;93m0.7500[0m | [1m最佳UAR:[0m [1;93m0.7888[0m
============================================================

[1;93m⚠️ ❌ 准确率提升(0.8065)但UF1(0.5121↓)和UAR(0.5280↓)都下降，不保存[0m

[1m[1;96m📅 Epoch 4/953[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 4 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9455[0m (624/660)
   • 中间层1准确率: [96m0.9530[0m (629/660)
   • 中间层2准确率: [96m0.9545[0m (630/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 4 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5806[0m (18/31)
🏆 [1m最佳准确率:[0m [1;93m0.6774[0m
📊 [1mUF1:[0m [1;96m0.4381[0m | [1mUAR:[0m [1;96m0.6460[0m
🥇 [1m最佳UF1:[0m [1;93m0.7500[0m | [1m最佳UAR:[0m [1;93m0.7888[0m
============================================================


[1m[1;96m📅 Epoch 5/953[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 5 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9258[0m (611/660)
   • 中间层1准确率: [96m0.9106[0m (601/660)
   • 中间层2准确率: [96m0.8924[0m (589/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 5 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6774[0m (21/31)
🏆 [1m最佳准确率:[0m [1;93m0.6774[0m
📊 [1mUF1:[0m [1;96m0.5873[0m | [1mUAR:[0m [1;96m0.7557[0m
🥇 [1m最佳UF1:[0m [1;93m0.7500[0m | [1m最佳UAR:[0m [1;93m0.7888[0m
============================================================


[1m[1;96m📅 Epoch 6/953[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 6 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9818[0m (648/660)
   • 中间层1准确率: [96m0.9727[0m (642/660)
   • 中间层2准确率: [96m0.9818[0m (648/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 6 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7419[0m (23/31)
🏆 [1m最佳准确率:[0m [1;93m0.6774[0m
📊 [1mUF1:[0m [1;96m0.4316[0m | [1mUAR:[0m [1;96m0.4327[0m
🥇 [1m最佳UF1:[0m [1;93m0.7500[0m | [1m最佳UAR:[0m [1;93m0.7888[0m
============================================================

[1;93m⚠️ ❌ 准确率提升(0.7419)但UF1(0.4316↓)和UAR(0.4327↓)都下降，不保存[0m

[1m[1;96m📅 Epoch 7/953[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 7 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9864[0m (651/660)
   • 中间层1准确率: [96m0.9788[0m (646/660)
   • 中间层2准确率: [96m0.9864[0m (651/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 7 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6774[0m (21/31)
🏆 [1m最佳准确率:[0m [1;93m0.6774[0m
📊 [1mUF1:[0m [1;96m0.4382[0m | [1mUAR:[0m [1;96m0.5031[0m
🥇 [1m最佳UF1:[0m [1;93m0.7500[0m | [1m最佳UAR:[0m [1;93m0.7888[0m
============================================================


[1m[1;96m📅 Epoch 8/953[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 8 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9909[0m (654/660)
   • 中间层1准确率: [96m0.9924[0m (655/660)
   • 中间层2准确率: [96m0.9894[0m (653/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 8 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.8065[0m (25/31)
🏆 [1m最佳准确率:[0m [1;93m0.6774[0m
📊 [1mUF1:[0m [1;96m0.7432[0m | [1mUAR:[0m [1;92m0.8799[0m
🥇 [1m最佳UF1:[0m [1;93m0.7500[0m | [1m最佳UAR:[0m [1;93m0.7888[0m
============================================================

[1;93m⚠️ ❌ 准确率提升(0.8065)但UF1下降(0.7432↓)，不保存[0m

[1m[1;96m📅 Epoch 9/953[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 9 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9955[0m (657/660)
   • 中间层1准确率: [96m0.9955[0m (657/660)
   • 中间层2准确率: [96m0.9970[0m (658/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 9 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.8710[0m (27/31)
🏆 [1m最佳准确率:[0m [1;93m0.6774[0m
📊 [1mUF1:[0m [1;92m0.7702[0m | [1mUAR:[0m [1;92m0.8427[0m
🥇 [1m最佳UF1:[0m [1;93m0.7500[0m | [1m最佳UAR:[0m [1;93m0.7888[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.8710 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.7702 | UAR: 0.8427 | 准确率: 0.8710[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth

[1m[1;96m📅 Epoch 10/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 10 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9970[0m (658/660)
   • 中间层1准确率: [96m0.9970[0m (658/660)
   • 中间层2准确率: [96m0.9955[0m (657/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 10 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.9032[0m (28/31)
🏆 [1m最佳准确率:[0m [1;93m0.8710[0m
📊 [1mUF1:[0m [1;92m0.9111[0m | [1mUAR:[0m [1;92m0.9234[0m
🥇 [1m最佳UF1:[0m [1;93m0.7702[0m | [1m最佳UAR:[0m [1;93m0.8427[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.9032 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.9111 | UAR: 0.9234 | 准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth

[1m[1;96m📅 Epoch 11/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 11 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9818[0m (648/660)
   • 中间层1准确率: [96m0.9894[0m (653/660)
   • 中间层2准确率: [96m0.9894[0m (653/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 11 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8387[0m (26/31)
🏆 [1m最佳准确率:[0m [1;93m0.9032[0m
📊 [1mUF1:[0m [1;96m0.5215[0m | [1mUAR:[0m [1;96m0.5093[0m
🥇 [1m最佳UF1:[0m [1;93m0.9111[0m | [1m最佳UAR:[0m [1;93m0.9234[0m
============================================================


[1m[1;96m📅 Epoch 12/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 12 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9924[0m (655/660)
   • 中间层1准确率: [96m0.9879[0m (652/660)
   • 中间层2准确率: [96m0.9909[0m (654/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 12 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.9032[0m (28/31)
🏆 [1m最佳准确率:[0m [1;93m0.9032[0m
📊 [1mUF1:[0m [1;96m0.9018[0m | [1mUAR:[0m [1;96m0.8903[0m
🥇 [1m最佳UF1:[0m [1;93m0.9111[0m | [1m最佳UAR:[0m [1;93m0.9234[0m
============================================================


[1m[1;96m📅 Epoch 13/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 13 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9924[0m (655/660)
   • 中间层1准确率: [96m0.9879[0m (652/660)
   • 中间层2准确率: [96m0.9955[0m (657/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 13 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.9355[0m (29/31)
🏆 [1m最佳准确率:[0m [1;93m0.9032[0m
📊 [1mUF1:[0m [1;92m0.9379[0m | [1mUAR:[0m [1;92m0.9379[0m
🥇 [1m最佳UF1:[0m [1;93m0.9111[0m | [1m最佳UAR:[0m [1;93m0.9234[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.9355 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.9379 | UAR: 0.9379 | 准确率: 0.9355[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth

[1m[1;96m📅 Epoch 14/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 14 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9212[0m (608/660)
   • 中间层1准确率: [96m0.9561[0m (631/660)
   • 中间层2准确率: [96m0.9348[0m (617/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 14 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6129[0m (19/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.3452[0m | [1mUAR:[0m [1;96m0.5942[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 15/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 15 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9727[0m (642/660)
   • 中间层1准确率: [96m0.9818[0m (648/660)
   • 中间层2准确率: [96m0.9818[0m (648/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 15 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8387[0m (26/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8632[0m | [1mUAR:[0m [1;96m0.8944[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 16/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 16 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (656/660)
   • 中间层1准确率: [96m0.9955[0m (657/660)
   • 中间层2准确率: [96m0.9955[0m (657/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 16 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8710[0m (27/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8864[0m | [1mUAR:[0m [1;96m0.9089[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 17/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 17 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9985[0m (659/660)
   • 中间层1准确率: [96m0.9985[0m (659/660)
   • 中间层2准确率: [96m0.9985[0m (659/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 17 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8710[0m (27/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8758[0m | [1mUAR:[0m [1;96m0.8758[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 18/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 18 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9985[0m (659/660)
   • 中间层1准确率: [96m0.9985[0m (659/660)
   • 中间层2准确率: [96m0.9970[0m (658/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 18 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8387[0m (26/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8145[0m | [1mUAR:[0m [1;96m0.7950[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 19/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 19 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9894[0m (653/660)
   • 中间层1准确率: [96m0.9909[0m (654/660)
   • 中间层2准确率: [96m0.9848[0m (650/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 19 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8710[0m (27/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8864[0m | [1mUAR:[0m [1;96m0.9089[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 20/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 20 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9970[0m (658/660)
   • 中间层1准确率: [96m0.9970[0m (658/660)
   • 中间层2准确率: [96m0.9955[0m (657/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 20 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8387[0m (26/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8519[0m | [1mUAR:[0m [1;96m0.8613[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 21/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 21 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9985[0m (659/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m0.9985[0m (659/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 21 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.9032[0m (28/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.9018[0m | [1mUAR:[0m [1;96m0.8903[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 22/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 22 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (660/660)
   • 中间层1准确率: [96m0.9985[0m (659/660)
   • 中间层2准确率: [96m0.9985[0m (659/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 22 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8710[0m (27/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8758[0m | [1mUAR:[0m [1;96m0.8758[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 23/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 23 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (656/660)
   • 中间层1准确率: [96m0.9742[0m (643/660)
   • 中间层2准确率: [96m0.9864[0m (651/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 23 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7419[0m (23/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.3654[0m | [1mUAR:[0m [1;96m0.3665[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 24/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 24 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9894[0m (653/660)
   • 中间层1准确率: [96m0.9561[0m (631/660)
   • 中间层2准确率: [96m0.9909[0m (654/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 24 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7742[0m (24/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8291[0m | [1mUAR:[0m [1;96m0.8986[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 25/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 25 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9424[0m (622/660)
   • 中间层1准确率: [96m0.9379[0m (619/660)
   • 中间层2准确率: [96m0.9394[0m (620/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 25 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7419[0m (23/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.6310[0m | [1mUAR:[0m [1;96m0.8178[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 26/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 26 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9712[0m (641/660)
   • 中间层1准确率: [96m0.9515[0m (628/660)
   • 中间层2准确率: [96m0.9697[0m (640/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 26 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6774[0m (21/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.5543[0m | [1mUAR:[0m [1;96m0.7557[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 27/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 27 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9924[0m (655/660)
   • 中间层1准确率: [96m0.9864[0m (651/660)
   • 中间层2准确率: [96m0.9909[0m (654/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 27 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8710[0m (27/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8758[0m | [1mUAR:[0m [1;96m0.8758[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 28/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 28 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9955[0m (657/660)
   • 中间层1准确率: [96m0.9970[0m (658/660)
   • 中间层2准确率: [96m0.9924[0m (655/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 28 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8710[0m (27/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8758[0m | [1mUAR:[0m [1;96m0.8758[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 29/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 29 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (660/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 29 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8387[0m (26/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8363[0m | [1mUAR:[0m [1;96m0.8282[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 30/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 30 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (660/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 30 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8387[0m (26/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.7423[0m | [1mUAR:[0m [1;96m0.8282[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 31/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 31 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (660/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 31 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8710[0m (27/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8611[0m | [1mUAR:[0m [1;96m0.8427[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 32/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 32 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (660/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 32 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8710[0m (27/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8611[0m | [1mUAR:[0m [1;96m0.8427[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 33/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 33 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9924[0m (655/660)
   • 中间层1准确率: [96m0.9985[0m (659/660)
   • 中间层2准确率: [96m0.9939[0m (656/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 33 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8387[0m (26/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8632[0m | [1mUAR:[0m [1;96m0.8944[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 34/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 34 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (656/660)
   • 中间层1准确率: [96m0.9939[0m (656/660)
   • 中间层2准确率: [96m0.9939[0m (656/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 34 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8065[0m (25/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8137[0m | [1mUAR:[0m [1;96m0.8137[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 35/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 35 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9727[0m (642/660)
   • 中间层1准确率: [96m0.9773[0m (645/660)
   • 中间层2准确率: [96m0.9697[0m (640/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 35 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7419[0m (23/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.3561[0m | [1mUAR:[0m [1;96m0.3665[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 36/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 36 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9712[0m (641/660)
   • 中间层1准确率: [96m0.9652[0m (637/660)
   • 中间层2准确率: [96m0.9758[0m (644/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 36 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6774[0m (21/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.5354[0m | [1mUAR:[0m [1;96m0.7226[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 37/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 37 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9833[0m (649/660)
   • 中间层1准确率: [96m0.9879[0m (652/660)
   • 中间层2准确率: [96m0.9773[0m (645/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 37 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7419[0m (23/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.4804[0m | [1mUAR:[0m [1;96m0.5321[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 38/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 38 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9848[0m (650/660)
   • 中间层1准确率: [96m0.9894[0m (653/660)
   • 中间层2准确率: [96m0.9712[0m (641/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 38 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8387[0m (26/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8632[0m | [1mUAR:[0m [1;96m0.8944[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 39/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 39 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9818[0m (648/660)
   • 中间层1准确率: [96m0.9803[0m (647/660)
   • 中间层2准确率: [96m0.9773[0m (645/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 39 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8710[0m (27/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8611[0m | [1mUAR:[0m [1;96m0.8427[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 40/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 40 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9970[0m (658/660)
   • 中间层1准确率: [96m0.9909[0m (654/660)
   • 中间层2准确率: [96m0.9985[0m (659/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 40 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8065[0m (25/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.6926[0m | [1mUAR:[0m [1;96m0.8468[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 41/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 41 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9985[0m (659/660)
   • 中间层1准确率: [96m0.9955[0m (657/660)
   • 中间层2准确率: [96m0.9985[0m (659/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 41 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8065[0m (25/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8295[0m | [1mUAR:[0m [1;96m0.8468[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 42/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 42 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9955[0m (657/660)
   • 中间层1准确率: [96m0.9985[0m (659/660)
   • 中间层2准确率: [96m0.9955[0m (657/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 42 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8387[0m (26/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8145[0m | [1mUAR:[0m [1;96m0.7950[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 43/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 43 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9985[0m (659/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m0.9985[0m (659/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 43 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7742[0m (24/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.6170[0m | [1mUAR:[0m [1;96m0.7660[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 44/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 44 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9970[0m (658/660)
   • 中间层1准确率: [96m0.9955[0m (657/660)
   • 中间层2准确率: [96m0.9970[0m (658/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 44 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8710[0m (27/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8611[0m | [1mUAR:[0m [1;96m0.8427[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 45/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 45 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9985[0m (659/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 45 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8710[0m (27/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8611[0m | [1mUAR:[0m [1;96m0.8427[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 46/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 46 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (660/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 46 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8387[0m (26/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8145[0m | [1mUAR:[0m [1;96m0.7950[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 47/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 47 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (660/660)
   • 中间层1准确率: [96m0.9985[0m (659/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 47 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8065[0m (25/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.6957[0m | [1mUAR:[0m [1;96m0.7805[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 48/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 48 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (660/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 48 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8387[0m (26/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8145[0m | [1mUAR:[0m [1;96m0.7950[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 49/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 49 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9955[0m (657/660)
   • 中间层1准确率: [96m0.9985[0m (659/660)
   • 中间层2准确率: [96m0.9985[0m (659/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 49 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8710[0m (27/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8758[0m | [1mUAR:[0m [1;96m0.8758[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 50/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 50 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9879[0m (652/660)
   • 中间层1准确率: [96m0.9955[0m (657/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 50 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8065[0m (25/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.7917[0m | [1mUAR:[0m [1;96m0.7805[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 51/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 51 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9773[0m (645/660)
   • 中间层1准确率: [96m0.9864[0m (651/660)
   • 中间层2准确率: [96m0.9773[0m (645/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 51 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8065[0m (25/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.7115[0m | [1mUAR:[0m [1;96m0.7143[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 52/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 52 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9621[0m (635/660)
   • 中间层1准确率: [96m0.9348[0m (617/660)
   • 中间层2准确率: [96m0.9621[0m (635/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 52 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8387[0m (26/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8363[0m | [1mUAR:[0m [1;96m0.8282[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 53/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 53 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9864[0m (651/660)
   • 中间层1准确率: [96m0.9712[0m (641/660)
   • 中间层2准确率: [96m0.9864[0m (651/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 53 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8065[0m (25/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8295[0m | [1mUAR:[0m [1;96m0.8468[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 54/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 54 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9970[0m (658/660)
   • 中间层1准确率: [96m0.9955[0m (657/660)
   • 中间层2准确率: [96m0.9985[0m (659/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 54 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8387[0m (26/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8632[0m | [1mUAR:[0m [1;96m0.8944[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 55/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 55 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9985[0m (659/660)
   • 中间层1准确率: [96m0.9939[0m (656/660)
   • 中间层2准确率: [96m0.9955[0m (657/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 55 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7742[0m (24/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.6413[0m | [1mUAR:[0m [1;96m0.7329[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 56/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 56 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (656/660)
   • 中间层1准确率: [96m0.9955[0m (657/660)
   • 中间层2准确率: [96m0.9955[0m (657/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 56 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8710[0m (27/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.7830[0m | [1mUAR:[0m [1;96m0.8758[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 57/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 57 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9955[0m (657/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m0.9985[0m (659/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 57 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.9032[0m (28/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.9018[0m | [1mUAR:[0m [1;96m0.8903[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 58/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 58 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9985[0m (659/660)
   • 中间层1准确率: [96m0.9970[0m (658/660)
   • 中间层2准确率: [96m0.9985[0m (659/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 58 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8387[0m (26/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8145[0m | [1mUAR:[0m [1;96m0.7950[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 59/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 59 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9909[0m (654/660)
   • 中间层1准确率: [96m0.9894[0m (653/660)
   • 中间层2准确率: [96m0.9924[0m (655/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 59 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8710[0m (27/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8611[0m | [1mUAR:[0m [1;96m0.8427[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 60/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 60 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9985[0m (659/660)
   • 中间层1准确率: [96m0.9985[0m (659/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 60 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.9032[0m (28/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.9018[0m | [1mUAR:[0m [1;96m0.8903[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 61/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 61 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (660/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 61 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.9032[0m (28/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.9018[0m | [1mUAR:[0m [1;96m0.8903[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 62/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 62 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (660/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 62 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.9032[0m (28/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.9018[0m | [1mUAR:[0m [1;96m0.8903[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 63/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 63 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (660/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 63 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8710[0m (27/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8758[0m | [1mUAR:[0m [1;96m0.8758[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 64/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 64 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (660/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 64 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.9032[0m (28/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.9018[0m | [1mUAR:[0m [1;96m0.8903[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 65/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 65 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (660/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 65 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8710[0m (27/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8758[0m | [1mUAR:[0m [1;96m0.8758[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 66/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 66 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (660/660)
   • 中间层1准确率: [96m0.9939[0m (656/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 66 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6774[0m (21/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.4981[0m | [1mUAR:[0m [1;96m0.6894[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 67/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 67 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9985[0m (659/660)
   • 中间层1准确率: [96m0.9864[0m (651/660)
   • 中间层2准确率: [96m0.9985[0m (659/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 67 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8065[0m (25/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.7917[0m | [1mUAR:[0m [1;96m0.7805[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 68/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 68 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9894[0m (653/660)
   • 中间层1准确率: [96m0.9803[0m (647/660)
   • 中间层2准确率: [96m0.9879[0m (652/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 68 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5161[0m (16/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.2894[0m | [1mUAR:[0m [1;96m0.5507[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 69/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 69 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9848[0m (650/660)
   • 中间层1准确率: [96m0.9924[0m (655/660)
   • 中间层2准确率: [96m0.9879[0m (652/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 69 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7419[0m (23/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8086[0m | [1mUAR:[0m [1;96m0.8841[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 70/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 70 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9879[0m (652/660)
   • 中间层1准确率: [96m0.9924[0m (655/660)
   • 中间层2准确率: [96m0.9879[0m (652/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 70 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6774[0m (21/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.5346[0m | [1mUAR:[0m [1;96m0.7226[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 71/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 71 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9758[0m (644/660)
   • 中间层1准确率: [96m0.9848[0m (650/660)
   • 中间层2准确率: [96m0.9712[0m (641/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 71 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8387[0m (26/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8632[0m | [1mUAR:[0m [1;96m0.8944[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 72/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 72 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9985[0m (659/660)
   • 中间层1准确率: [96m0.9924[0m (655/660)
   • 中间层2准确率: [96m0.9985[0m (659/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 72 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.9032[0m (28/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.9018[0m | [1mUAR:[0m [1;96m0.8903[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 73/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 73 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9985[0m (659/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 73 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8065[0m (25/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8295[0m | [1mUAR:[0m [1;96m0.8468[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 74/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 74 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (660/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m0.9985[0m (659/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 74 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8387[0m (26/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8145[0m | [1mUAR:[0m [1;96m0.7950[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 75/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 75 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (660/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 75 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8710[0m (27/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8611[0m | [1mUAR:[0m [1;96m0.8427[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 76/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 76 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (660/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 76 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.9032[0m (28/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.9018[0m | [1mUAR:[0m [1;96m0.8903[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 77/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 77 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (660/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 77 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.9032[0m (28/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.9018[0m | [1mUAR:[0m [1;96m0.8903[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 78/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 78 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (660/660)
   • 中间层1准确率: [96m0.9985[0m (659/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 78 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8387[0m (26/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8519[0m | [1mUAR:[0m [1;96m0.8613[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 79/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 79 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9985[0m (659/660)
   • 中间层1准确率: [96m1.0000[0m (660/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 79 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7097[0m (22/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.5596[0m | [1mUAR:[0m [1;96m0.7371[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 80/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 80 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9879[0m (652/660)
   • 中间层1准确率: [96m0.9970[0m (658/660)
   • 中间层2准确率: [96m0.9879[0m (652/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 80 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8387[0m (26/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8145[0m | [1mUAR:[0m [1;96m0.7950[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 81/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 81 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9758[0m (644/660)
   • 中间层1准确率: [96m0.9742[0m (643/660)
   • 中间层2准确率: [96m0.9727[0m (642/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 81 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8710[0m (27/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8611[0m | [1mUAR:[0m [1;96m0.8427[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 82/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 82 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9636[0m (636/660)
   • 中间层1准确率: [96m0.9652[0m (637/660)
   • 中间层2准确率: [96m0.9667[0m (638/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 82 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8387[0m (26/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.7215[0m | [1mUAR:[0m [1;96m0.7950[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 83/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 83 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9894[0m (653/660)
   • 中间层1准确率: [96m0.9864[0m (651/660)
   • 中间层2准确率: [96m0.9864[0m (651/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 83 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7742[0m (24/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.6680[0m | [1mUAR:[0m [1;96m0.8323[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 84/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 84 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9924[0m (655/660)
   • 中间层1准确率: [96m0.9970[0m (658/660)
   • 中间层2准确率: [96m0.9924[0m (655/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 84 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7419[0m (23/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.6215[0m | [1mUAR:[0m [1;96m0.8178[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 85/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 85 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9985[0m (659/660)
   • 中间层1准确率: [96m0.9985[0m (659/660)
   • 中间层2准确率: [96m0.9955[0m (657/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 85 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7742[0m (24/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.6322[0m | [1mUAR:[0m [1;96m0.7660[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 86/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 86 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9985[0m (659/660)
   • 中间层1准确率: [96m0.9970[0m (658/660)
   • 中间层2准确率: [96m0.9985[0m (659/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 86 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6774[0m (21/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.5043[0m | [1mUAR:[0m [1;96m0.6894[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 87/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 87 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9985[0m (659/660)
   • 中间层1准确率: [96m0.9985[0m (659/660)
   • 中间层2准确率: [96m0.9985[0m (659/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 87 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7419[0m (23/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.5865[0m | [1mUAR:[0m [1;96m0.7516[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 88/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 88 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9955[0m (657/660)
   • 中间层1准确率: [96m0.9955[0m (657/660)
   • 中间层2准确率: [96m0.9955[0m (657/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 88 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8387[0m (26/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.8145[0m | [1mUAR:[0m [1;96m0.7950[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 89/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 89 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9955[0m (657/660)
   • 中间层1准确率: [96m0.9955[0m (657/660)
   • 中间层2准确率: [96m0.9970[0m (658/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 89 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7742[0m (24/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.6350[0m | [1mUAR:[0m [1;96m0.7992[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 90/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 90 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9985[0m (659/660)
   • 中间层1准确率: [96m0.9985[0m (659/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 90 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8065[0m (25/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.7172[0m | [1mUAR:[0m [1;96m0.8137[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 91/953[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 91 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (660/660)
   • 中间层1准确率: [96m0.9985[0m (659/660)
   • 中间层2准确率: [96m1.0000[0m (660/660)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 91 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8065[0m (25/31)
🏆 [1m最佳准确率:[0m [1;93m0.9355[0m
📊 [1mUF1:[0m [1;96m0.6637[0m | [1mUAR:[0m [1;96m0.7474[0m
🥇 [1m最佳UF1:[0m [1;93m0.9379[0m | [1m最佳UAR:[0m [1;93m0.9379[0m
============================================================


[1m[1;96m📅 Epoch 92/953[0m
[1;96m----------------[0m
正在关闭TensorBoard写入器...
TensorBoard写入器已关闭

正在关闭日志系统...
