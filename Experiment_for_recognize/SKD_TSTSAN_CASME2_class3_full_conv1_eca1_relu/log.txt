日志系统初始化成功

================================================================================
【训练开始 - GPU预加载 + 并行训练模式】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (3分类)[0m
[1;96m🎯 类别权重: ['0.3794', '0.1349', '0.4857'][0m
开始训练...

[1m[1;94m⚙️ 训练模式配置[0m
[1;94m----------[0m
[1;96mℹ️ GPU数据预加载: ✅ 启用[0m
[1;96mℹ️ 并行训练: ✅ 启用[0m
[1;96mℹ️ 并行工作线程数: 4[0m

[1m[1;96m🚀 GPU数据预加载器初始化[0m
[1;96m----------------[0m
[1;96mℹ️ 数据集路径: ./CASME2_LOSO_full[0m
[1;96mℹ️ 分类数量: 3[0m
[1;96mℹ️ 目标设备: cuda[0m

[1;92m========================[0m
[1m[1;92m🚀 开始预加载CASME2数据集到GPU显存 🚀[0m
[1;92m========================[0m

[1;96m📊 正在估算数据集显存需求...[0m
[1;92m📈 估算结果: 6344 个样本[0m
[1;92m💾 预计显存需求: 2.07 GB[0m
[1;96mℹ️ GPU总显存: 79.25 GB[0m
[1;96mℹ️ 可用显存: 63.40 GB[0m
[1;96mℹ️ 发现 26 个受试者: ['sub01', 'sub02', 'sub03', 'sub04', 'sub05', 'sub06', 'sub07', 'sub08', 'sub09', 'sub10', 'sub11', 'sub12', 'sub13', 'sub14', 'sub15', 'sub16', 'sub17', 'sub18', 'sub19', 'sub20', 'sub21', 'sub22', 'sub23', 'sub24', 'sub25', 'sub26'][0m
[1;96m📊 预加载进度: 1/26 - sub01[0m
[1;96m📥 正在预加载受试者 sub01 的数据...[0m
[1;92m✅ 受试者 sub01 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 0.05 GB[0m
[1;96m📊 预加载进度: 2/26 - sub02[0m
[1;96m📥 正在预加载受试者 sub02 的数据...[0m
[1;92m✅ 受试者 sub02 数据预加载完成[0m
[1;96mℹ️ 训练样本: 136, 测试样本: 9[0m
[1;96m💾 当前显存使用: 0.10 GB[0m
[1;96m📊 预加载进度: 3/26 - sub03[0m
[1;96m📥 正在预加载受试者 sub03 的数据...[0m
[1;92m✅ 受试者 sub03 数据预加载完成[0m
[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96m💾 当前显存使用: 0.14 GB[0m
[1;96m📊 预加载进度: 4/26 - sub04[0m
[1;96m📥 正在预加载受试者 sub04 的数据...[0m
[1;92m✅ 受试者 sub04 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.19 GB[0m
[1;96m📊 预加载进度: 5/26 - sub05[0m
[1;96m📥 正在预加载受试者 sub05 的数据...[0m
[1;92m✅ 受试者 sub05 数据预加载完成[0m
[1;96mℹ️ 训练样本: 138, 测试样本: 7[0m
[1;96m💾 当前显存使用: 0.24 GB[0m
[1;96m📊 预加载进度: 6/26 - sub06[0m
[1;96m📥 正在预加载受试者 sub06 的数据...[0m
[1;92m✅ 受试者 sub06 数据预加载完成[0m
[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m
[1;96m💾 当前显存使用: 0.29 GB[0m
[1;96m📊 预加载进度: 7/26 - sub07[0m
[1;96m📥 正在预加载受试者 sub07 的数据...[0m
[1;92m✅ 受试者 sub07 数据预加载完成[0m
[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96m💾 当前显存使用: 0.33 GB[0m
[1;96m📊 预加载进度: 8/26 - sub08[0m
[1;96m📥 正在预加载受试者 sub08 的数据...[0m
[1;92m✅ 受试者 sub08 数据预加载完成[0m
[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m
[1;96m💾 当前显存使用: 0.38 GB[0m
[1;96m📊 预加载进度: 9/26 - sub09[0m
[1;96m📥 正在预加载受试者 sub09 的数据...[0m
[1;92m✅ 受试者 sub09 数据预加载完成[0m
[1;96mℹ️ 训练样本: 135, 测试样本: 10[0m
[1;96m💾 当前显存使用: 0.43 GB[0m
[1;96m📊 预加载进度: 10/26 - sub10[0m
[1;96m📥 正在预加载受试者 sub10 的数据...[0m
[1;92m✅ 受试者 sub10 数据预加载完成[0m
[1;96mℹ️ 训练样本: 145, 测试样本: 0[0m
[1;96m💾 当前显存使用: 0.48 GB[0m
[1;96m📊 预加载进度: 11/26 - sub11[0m
[1;96m📥 正在预加载受试者 sub11 的数据...[0m
[1;92m✅ 受试者 sub11 数据预加载完成[0m
[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m
[1;96m💾 当前显存使用: 0.53 GB[0m
[1;96m📊 预加载进度: 12/26 - sub12[0m
[1;96m📥 正在预加载受试者 sub12 的数据...[0m
[1;92m✅ 受试者 sub12 数据预加载完成[0m
[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m
[1;96m💾 当前显存使用: 0.57 GB[0m
[1;96m📊 预加载进度: 13/26 - sub13[0m
[1;96m📥 正在预加载受试者 sub13 的数据...[0m
[1;92m✅ 受试者 sub13 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.62 GB[0m
[1;96m📊 预加载进度: 14/26 - sub14[0m
[1;96m📥 正在预加载受试者 sub14 的数据...[0m
[1;92m✅ 受试者 sub14 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 0.67 GB[0m
[1;96m📊 预加载进度: 15/26 - sub15[0m
[1;96m📥 正在预加载受试者 sub15 的数据...[0m
[1;92m✅ 受试者 sub15 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 0.72 GB[0m
[1;96m📊 预加载进度: 16/26 - sub16[0m
[1;96m📥 正在预加载受试者 sub16 的数据...[0m
[1;92m✅ 受试者 sub16 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.76 GB[0m
[1;96m📊 预加载进度: 17/26 - sub17[0m
[1;96m📥 正在预加载受试者 sub17 的数据...[0m
[1;92m✅ 受试者 sub17 数据预加载完成[0m
[1;96mℹ️ 训练样本: 114, 测试样本: 31[0m
[1;96m💾 当前显存使用: 0.81 GB[0m
[1;96m📊 预加载进度: 18/26 - sub18[0m
[1;96m📥 正在预加载受试者 sub18 的数据...[0m
[1;92m✅ 受试者 sub18 数据预加载完成[0m
[1;96mℹ️ 训练样本: 145, 测试样本: 0[0m
[1;96m💾 当前显存使用: 0.86 GB[0m
[1;96m📊 预加载进度: 19/26 - sub19[0m
[1;96m📥 正在预加载受试者 sub19 的数据...[0m
[1;92m✅ 受试者 sub19 数据预加载完成[0m
[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m
[1;96m💾 当前显存使用: 0.91 GB[0m
[1;96m📊 预加载进度: 20/26 - sub20[0m
[1;96m📥 正在预加载受试者 sub20 的数据...[0m
[1;92m✅ 受试者 sub20 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.95 GB[0m
[1;96m📊 预加载进度: 21/26 - sub21[0m
[1;96m📥 正在预加载受试者 sub21 的数据...[0m
[1;92m✅ 受试者 sub21 数据预加载完成[0m
[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m
[1;96m💾 当前显存使用: 1.00 GB[0m
[1;96m📊 预加载进度: 22/26 - sub22[0m
[1;96m📥 正在预加载受试者 sub22 的数据...[0m
[1;92m✅ 受试者 sub22 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 1.05 GB[0m
[1;96m📊 预加载进度: 23/26 - sub23[0m
[1;96m📥 正在预加载受试者 sub23 的数据...[0m
[1;92m✅ 受试者 sub23 数据预加载完成[0m
[1;96mℹ️ 训练样本: 137, 测试样本: 8[0m
[1;96m💾 当前显存使用: 1.10 GB[0m
[1;96m📊 预加载进度: 24/26 - sub24[0m
[1;96m📥 正在预加载受试者 sub24 的数据...[0m
[1;92m✅ 受试者 sub24 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 1.14 GB[0m
[1;96m📊 预加载进度: 25/26 - sub25[0m
[1;96m📥 正在预加载受试者 sub25 的数据...[0m
[1;92m✅ 受试者 sub25 数据预加载完成[0m
[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96m💾 当前显存使用: 1.19 GB[0m
[1;96m📊 预加载进度: 26/26 - sub26[0m
[1;96m📥 正在预加载受试者 sub26 的数据...[0m
[1;92m✅ 受试者 sub26 数据预加载完成[0m
[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m
[1;96m💾 当前显存使用: 1.24 GB[0m

[1;92m===========[0m
[1m[1;92m✅ 数据预加载完成 ✅[0m
[1;92m===========[0m

[1;92m✅ 成功预加载: 26 个受试者[0m
[1m[1;95m💾 最终显存使用: 1.24 GB[0m
根据配置,不使用Visdom可视化

[1;95m=====================[0m
[1m[1;95m🚀 使用GPU预加载 + 并行训练模式 🚀[0m
[1;95m=====================[0m

[1;96mℹ️ 可用受试者: 23 个[0m
[1;96mℹ️ 受试者列表: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub23', 'sub12', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub06', 'sub15', 'sub20', 'sub04', 'sub13', 'sub08', 'sub16', 'sub21', 'sub22'][0m

[1;95m==================[0m
[1m[1;95m⚡ 开始并行训练 23 个受试者 ⚡[0m
[1;95m==================[0m

[1;96mℹ️ 并行工作线程数: 4[0m
[1;96m🎯 开始训练受试者: sub17[0m
[1;96m🎯 开始训练受试者: sub05[0m[1;96mℹ️ 训练样本: 114, 测试样本: 31[0m[1;96m🎯 开始训练受试者: sub26[0m

[1;96mℹ️ 训练样本: 138, 测试样本: 7[0m[1;96m🎯 开始训练受试者: sub19[0m


[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m[1;96mℹ️ 训练数据形状: torch.Size([114, 38, 48, 48]), 测试数据形状: torch.Size([31, 38, 48, 48])[0m[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m[1;96mℹ️ 训练数据形状: torch.Size([138, 38, 48, 48]), 测试数据形状: torch.Size([7, 38, 48, 48])[0m



[1;96mℹ️ 训练数据形状: torch.Size([134, 38, 48, 48]), 测试数据形状: torch.Size([11, 38, 48, 48])[0m
[1;96mℹ️ 训练数据形状: torch.Size([134, 38, 48, 48]), 测试数据形状: torch.Size([11, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8=> 使用折叠除法: 8
=> 使用折叠除法: 8


=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式=> 使用折叠除法: 8

📝 使用传统特征拼接方式
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;91m❌ 训练受试者 sub26 时出错: 'tuple' object has no attribute 'log_softmax'[0m[1;91m❌ 训练受试者 sub17 时出错: 'tuple' object has no attribute 'log_softmax'[0m

[1;91m❌ 训练受试者 sub19 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub05 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;96m🎯 开始训练受试者: sub09[0m[1;91m❌ ❌ sub17 训练失败[0m

[1;96mℹ️ 训练样本: 135, 测试样本: 10[0m
[1;96mℹ️ 训练数据形状: torch.Size([135, 38, 48, 48]), 测试数据形状: torch.Size([10, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub02[0m[1;91m❌ ❌ sub26 训练失败[0m
[1;96mℹ️ 训练样本: 136, 测试样本: 9[0m

[1;96m🎯 开始训练受试者: sub23[0m[1;96mℹ️ 训练数据形状: torch.Size([136, 38, 48, 48]), 测试数据形状: torch.Size([9, 38, 48, 48])[0m
[1;91m❌ ❌ sub05 训练失败[0m
[1;96mℹ️ 训练样本: 137, 测试样本: 8[0m

[1;96mℹ️ 训练数据形状: torch.Size([137, 38, 48, 48]), 测试数据形状: torch.Size([8, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub12[0m[1;91m❌ ❌ sub19 训练失败[0m

[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m
[1;96mℹ️ 训练数据形状: torch.Size([134, 38, 48, 48]), 测试数据形状: torch.Size([11, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8🔧 激活函数配置: 标准ReLU

🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8📝 使用传统特征拼接方式=> 使用折叠除法: 8


=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

📝 使用传统特征拼接方式
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;91m❌ 训练受试者 sub23 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub09 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub12 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub02 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;96m🎯 开始训练受试者: sub11[0m[1;91m❌ ❌ sub23 训练失败[0m

[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m
[1;96mℹ️ 训练数据形状: torch.Size([141, 38, 48, 48]), 测试数据形状: torch.Size([4, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
[1;96m🎯 开始训练受试者: sub01[0m[1;91m❌ ❌ sub09 训练失败[0m

[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96mℹ️ 训练数据形状: torch.Size([142, 38, 48, 48]), 测试数据形状: torch.Size([3, 38, 48, 48])[0m
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub07[0m[1;91m❌ ❌ sub12 训练失败[0m

[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96mℹ️ 训练数据形状: torch.Size([140, 38, 48, 48]), 测试数据形状: torch.Size([5, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub24[0m[1;91m❌ ❌ sub02 训练失败[0m

[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96mℹ️ 训练数据形状: torch.Size([142, 38, 48, 48]), 测试数据形状: torch.Size([3, 38, 48, 48])[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU=> 使用折叠除法: 8🔧 激活函数配置: 标准ReLU


🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

📝 使用传统特征拼接方式=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8
=> 使用折叠除法: 8

=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
📝 使用传统特征拼接方式
=> 使用折叠除法: 8
=> 使用折叠除法: 8📝 使用传统特征拼接方式

📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m[1;96mℹ️ 加载预训练模型...[0m

[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;91m❌ 训练受试者 sub11 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub24 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub07 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub01 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;96m🎯 开始训练受试者: sub25[0m[1;91m❌ ❌ sub11 训练失败[0m

[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96mℹ️ 训练数据形状: torch.Size([140, 38, 48, 48]), 测试数据形状: torch.Size([5, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub03[0m[1;91m❌ ❌ sub24 训练失败[0m

[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m=> 使用折叠除法: 8

[1;96mℹ️ 训练数据形状: torch.Size([140, 38, 48, 48]), 测试数据形状: torch.Size([5, 38, 48, 48])[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub06[0m[1;91m❌ ❌ sub07 训练失败[0m

[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m
[1;96mℹ️ 训练数据形状: torch.Size([141, 38, 48, 48]), 测试数据形状: torch.Size([4, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub15[0m[1;91m❌ ❌ sub01 训练失败[0m

[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96mℹ️ 训练数据形状: torch.Size([142, 38, 48, 48]), 测试数据形状: torch.Size([3, 38, 48, 48])[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU📝 使用传统特征拼接方式

🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8=> 使用折叠除法: 8

🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8=> 使用折叠除法: 8


=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8📝 使用传统特征拼接方式
📝 使用传统特征拼接方式

📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;91m❌ 训练受试者 sub25 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub06 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub03 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub15 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;96m🎯 开始训练受试者: sub20[0m
[1;91m❌ ❌ sub25 训练失败[0m[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m

[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8[1;96m🎯 开始训练受试者: sub04[0m
[1;91m❌ ❌ sub06 训练失败[0m

[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub13[0m[1;91m❌ ❌ sub03 训练失败[0m

[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub08[0m[1;91m❌ ❌ sub15 训练失败[0m

[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m
[1;96mℹ️ 训练数据形状: torch.Size([144, 38, 48, 48]), 测试数据形状: torch.Size([1, 38, 48, 48])[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
📝 使用传统特征拼接方式
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
=> 使用折叠除法: 8
=> 使用折叠除法: 8📝 使用传统特征拼接方式

=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;91m❌ 训练受试者 sub20 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub04 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub08 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;96m🎯 开始训练受试者: sub16[0m
[1;91m❌ ❌ sub20 训练失败[0m[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m

[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
[1;91m❌ 训练受试者 sub13 时出错: 'tuple' object has no attribute 'log_softmax'[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub21[0m[1;91m❌ ❌ sub08 训练失败[0m

[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m
[1;96mℹ️ 训练数据形状: torch.Size([144, 38, 48, 48]), 测试数据形状: torch.Size([1, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub22[0m[1;91m❌ ❌ sub04 训练失败[0m

[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
=> 使用折叠除法: 8
[1;91m❌ ❌ sub13 训练失败[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
📝 使用传统特征拼接方式
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式=> 使用折叠除法: 8

📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m[1;96mℹ️ 加载预训练模型...[0m

[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;92m✅ 成功加载预训练模型: SKD-TSTSAN.pth[0m
[1;96mℹ️ 加载了 249/255 个层[0m
[1;91m❌ 训练受试者 sub21 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub16 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub22 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ ❌ sub21 训练失败[0m
[1;91m❌ ❌ sub16 训练失败[0m
[1;91m❌ ❌ sub22 训练失败[0m

[1;92m==========[0m
[1m[1;92m🎉 并行训练完成 🎉[0m
[1;92m==========[0m

[1;92m✅ 成功训练: 0 个受试者[0m

[1;92m===================[0m
[1m[1;92m📊 并行训练完成，正在计算总体指标 📊[0m
[1;92m===================[0m


================================================================================
【实验结果报告】
================================================================================

【基本信息】
实验名称: SKD_TSTSAN_CASME2_class3_full_conv1_eca1_relu
时间: 2025-07-28 22:50:54
总训练时间: 00:00:29
数据集: CASME2_LOSO_full

【系统环境】
操作系统: 💻 Linux 6.8.0-1030-nvidia (x86_64)
处理器: ⚡ 32核心/32线程 @ 2195MHz
内存: 🧠 62.7GB RAM (已用: 22.9GB, 36.5%)
GPU: 🚀 NVIDIA A800 80GB PCIe (79.3GB)

【模型配置】
模型架构: SKD_TSTSAN
是否使用预训练: 是
预训练模型: SKD-TSTSAN.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
批次大小: 32
最大迭代次数: 20
损失函数: FocalLoss_weighted
随机种子: 2577

【GPU性能配置】
CUDNN Benchmark: 禁用
CUDNN Deterministic: 禁用
混合精度训练: 禁用
TF32加速: 启用
BatchNorm修复: 禁用
卷积类型: 标准卷积

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: N/A
- UAR分数: N/A

【各表情类别准确率】
--------------------------------------------------

【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
    平均        nan        nan        nan    


【各受试者详细预测结果】
==================================================

================================================================================

【错误】邮件处理过程出错: '>=' not supported between instances of 'NoneType' and 'float'
详细错误信息: Traceback (most recent call last):
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSANCASME2.py", line 757, in send_training_results
    performance_recommendations = generate_performance_recommendations(results_dict, config, total_time)
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSANCASME2.py", line 498, in generate_performance_recommendations
    if overall_uf1 >= 0.8 and overall_uar >= 0.8:
TypeError: '>=' not supported between instances of 'NoneType' and 'float'


训练完成!

正在关闭日志系统...
日志系统初始化成功

================================================================================
【训练开始 - GPU预加载 + 并行训练模式】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (3分类)[0m
[1;96m🎯 类别权重: ['0.3794', '0.1349', '0.4857'][0m
开始训练...

[1m[1;94m⚙️ 训练模式配置[0m
[1;94m----------[0m
[1;96mℹ️ GPU数据预加载: ✅ 启用[0m
[1;96mℹ️ 并行训练: ✅ 启用[0m
[1;96mℹ️ 并行工作线程数: 3[0m

[1m[1;96m🚀 GPU数据预加载器初始化[0m
[1;96m----------------[0m
[1;96mℹ️ 数据集路径: ./CASME2_LOSO_full[0m
[1;96mℹ️ 分类数量: 3[0m
[1;96mℹ️ 目标设备: cuda[0m

[1;92m========================[0m
[1m[1;92m🚀 开始预加载CASME2数据集到GPU显存 🚀[0m
[1;92m========================[0m

[1;96m📊 正在估算数据集显存需求...[0m
[1;92m📈 估算结果: 6344 个样本[0m
[1;92m💾 预计显存需求: 2.07 GB[0m
[1;96mℹ️ GPU总显存: 79.25 GB[0m
[1;96mℹ️ 可用显存: 63.40 GB[0m
[1;96mℹ️ 发现 26 个受试者: ['sub01', 'sub02', 'sub03', 'sub04', 'sub05', 'sub06', 'sub07', 'sub08', 'sub09', 'sub10', 'sub11', 'sub12', 'sub13', 'sub14', 'sub15', 'sub16', 'sub17', 'sub18', 'sub19', 'sub20', 'sub21', 'sub22', 'sub23', 'sub24', 'sub25', 'sub26'][0m
[1;96m📊 预加载进度: 1/26 - sub01[0m
[1;96m📥 正在预加载受试者 sub01 的数据...[0m
[1;92m✅ 受试者 sub01 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 0.05 GB[0m
[1;96m📊 预加载进度: 2/26 - sub02[0m
[1;96m📥 正在预加载受试者 sub02 的数据...[0m
[1;92m✅ 受试者 sub02 数据预加载完成[0m
[1;96mℹ️ 训练样本: 136, 测试样本: 9[0m
[1;96m💾 当前显存使用: 0.10 GB[0m
[1;96m📊 预加载进度: 3/26 - sub03[0m
[1;96m📥 正在预加载受试者 sub03 的数据...[0m
[1;92m✅ 受试者 sub03 数据预加载完成[0m
[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96m💾 当前显存使用: 0.14 GB[0m
[1;96m📊 预加载进度: 4/26 - sub04[0m
[1;96m📥 正在预加载受试者 sub04 的数据...[0m
[1;92m✅ 受试者 sub04 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.19 GB[0m
[1;96m📊 预加载进度: 5/26 - sub05[0m
[1;96m📥 正在预加载受试者 sub05 的数据...[0m
[1;92m✅ 受试者 sub05 数据预加载完成[0m
[1;96mℹ️ 训练样本: 138, 测试样本: 7[0m
[1;96m💾 当前显存使用: 0.24 GB[0m
[1;96m📊 预加载进度: 6/26 - sub06[0m
[1;96m📥 正在预加载受试者 sub06 的数据...[0m
[1;92m✅ 受试者 sub06 数据预加载完成[0m
[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m
[1;96m💾 当前显存使用: 0.29 GB[0m
[1;96m📊 预加载进度: 7/26 - sub07[0m
[1;96m📥 正在预加载受试者 sub07 的数据...[0m
[1;92m✅ 受试者 sub07 数据预加载完成[0m
[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96m💾 当前显存使用: 0.33 GB[0m
[1;96m📊 预加载进度: 8/26 - sub08[0m
[1;96m📥 正在预加载受试者 sub08 的数据...[0m
[1;92m✅ 受试者 sub08 数据预加载完成[0m
[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m
[1;96m💾 当前显存使用: 0.38 GB[0m
[1;96m📊 预加载进度: 9/26 - sub09[0m
[1;96m📥 正在预加载受试者 sub09 的数据...[0m
[1;92m✅ 受试者 sub09 数据预加载完成[0m
[1;96mℹ️ 训练样本: 135, 测试样本: 10[0m
[1;96m💾 当前显存使用: 0.43 GB[0m
[1;96m📊 预加载进度: 10/26 - sub10[0m
[1;96m📥 正在预加载受试者 sub10 的数据...[0m
[1;92m✅ 受试者 sub10 数据预加载完成[0m
[1;96mℹ️ 训练样本: 145, 测试样本: 0[0m
[1;96m💾 当前显存使用: 0.48 GB[0m
[1;96m📊 预加载进度: 11/26 - sub11[0m
[1;96m📥 正在预加载受试者 sub11 的数据...[0m
[1;92m✅ 受试者 sub11 数据预加载完成[0m
[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m
[1;96m💾 当前显存使用: 0.53 GB[0m
[1;96m📊 预加载进度: 12/26 - sub12[0m
[1;96m📥 正在预加载受试者 sub12 的数据...[0m
[1;92m✅ 受试者 sub12 数据预加载完成[0m
[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m
[1;96m💾 当前显存使用: 0.57 GB[0m
[1;96m📊 预加载进度: 13/26 - sub13[0m
[1;96m📥 正在预加载受试者 sub13 的数据...[0m
[1;92m✅ 受试者 sub13 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.62 GB[0m
[1;96m📊 预加载进度: 14/26 - sub14[0m
[1;96m📥 正在预加载受试者 sub14 的数据...[0m
[1;92m✅ 受试者 sub14 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 0.67 GB[0m
[1;96m📊 预加载进度: 15/26 - sub15[0m
[1;96m📥 正在预加载受试者 sub15 的数据...[0m
[1;92m✅ 受试者 sub15 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 0.72 GB[0m
[1;96m📊 预加载进度: 16/26 - sub16[0m
[1;96m📥 正在预加载受试者 sub16 的数据...[0m
[1;92m✅ 受试者 sub16 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.76 GB[0m
[1;96m📊 预加载进度: 17/26 - sub17[0m
[1;96m📥 正在预加载受试者 sub17 的数据...[0m
[1;92m✅ 受试者 sub17 数据预加载完成[0m
[1;96mℹ️ 训练样本: 114, 测试样本: 31[0m
[1;96m💾 当前显存使用: 0.81 GB[0m
[1;96m📊 预加载进度: 18/26 - sub18[0m
[1;96m📥 正在预加载受试者 sub18 的数据...[0m

正在关闭日志系统...
