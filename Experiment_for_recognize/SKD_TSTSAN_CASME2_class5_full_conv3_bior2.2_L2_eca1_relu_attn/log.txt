日志系统初始化成功

================================================================================
【训练开始 - GPU预加载 + 并行训练模式】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...

[1m[1;94m⚙️ 训练模式配置[0m
[1;94m----------[0m
[1;96mℹ️ GPU数据预加载: ✅ 启用[0m
[1;96mℹ️ 并行训练: ✅ 启用[0m
[1;96mℹ️ 并行工作线程数: 4[0m

[1m[1;96m🚀 GPU数据预加载器初始化[0m
[1;96m----------------[0m
[1;96mℹ️ 数据集路径: ./CASME2_LOSO_full[0m
[1;96mℹ️ 分类数量: 5[0m
[1;96mℹ️ 目标设备: cuda[0m

[1;92m========================[0m
[1m[1;92m🚀 开始预加载CASME2数据集到GPU显存 🚀[0m
[1;92m========================[0m

[1;96m📊 正在估算数据集显存需求...[0m
[1;92m📈 估算结果: 6344 个样本[0m
[1;92m💾 预计显存需求: 2.07 GB[0m
[1;96mℹ️ GPU总显存: 79.25 GB[0m
[1;96mℹ️ 可用显存: 63.40 GB[0m
[1;96mℹ️ 发现 26 个受试者: ['sub01', 'sub02', 'sub03', 'sub04', 'sub05', 'sub06', 'sub07', 'sub08', 'sub09', 'sub10', 'sub11', 'sub12', 'sub13', 'sub14', 'sub15', 'sub16', 'sub17', 'sub18', 'sub19', 'sub20', 'sub21', 'sub22', 'sub23', 'sub24', 'sub25', 'sub26'][0m
[1;96m📊 预加载进度: 1/26 - sub01[0m
[1;96m📥 正在预加载受试者 sub01 的数据...[0m
[1;92m✅ 受试者 sub01 数据预加载完成[0m
[1;96mℹ️ 训练样本: 235, 测试样本: 9[0m
[1;96m💾 当前显存使用: 0.08 GB[0m
[1;96m📊 预加载进度: 2/26 - sub02[0m
[1;96m📥 正在预加载受试者 sub02 的数据...[0m
[1;92m✅ 受试者 sub02 数据预加载完成[0m
[1;96mℹ️ 训练样本: 231, 测试样本: 13[0m
[1;96m💾 当前显存使用: 0.16 GB[0m
[1;96m📊 预加载进度: 3/26 - sub03[0m
[1;96m📥 正在预加载受试者 sub03 的数据...[0m
[1;92m✅ 受试者 sub03 数据预加载完成[0m
[1;96mℹ️ 训练样本: 237, 测试样本: 7[0m
[1;96m💾 当前显存使用: 0.24 GB[0m
[1;96m📊 预加载进度: 4/26 - sub04[0m
[1;96m📥 正在预加载受试者 sub04 的数据...[0m
[1;92m✅ 受试者 sub04 数据预加载完成[0m
[1;96mℹ️ 训练样本: 240, 测试样本: 4[0m
[1;96m💾 当前显存使用: 0.32 GB[0m
[1;96m📊 预加载进度: 5/26 - sub05[0m
[1;96m📥 正在预加载受试者 sub05 的数据...[0m
[1;92m✅ 受试者 sub05 数据预加载完成[0m
[1;96mℹ️ 训练样本: 225, 测试样本: 19[0m
[1;96m💾 当前显存使用: 0.40 GB[0m
[1;96m📊 预加载进度: 6/26 - sub06[0m
[1;96m📥 正在预加载受试者 sub06 的数据...[0m
[1;92m✅ 受试者 sub06 数据预加载完成[0m
[1;96mℹ️ 训练样本: 239, 测试样本: 5[0m
[1;96m💾 当前显存使用: 0.48 GB[0m
[1;96m📊 预加载进度: 7/26 - sub07[0m
[1;96m📥 正在预加载受试者 sub07 的数据...[0m
[1;92m✅ 受试者 sub07 数据预加载完成[0m
[1;96mℹ️ 训练样本: 235, 测试样本: 9[0m
[1;96m💾 当前显存使用: 0.56 GB[0m
[1;96m📊 预加载进度: 8/26 - sub08[0m
[1;96m📥 正在预加载受试者 sub08 的数据...[0m
[1;92m✅ 受试者 sub08 数据预加载完成[0m
[1;96mℹ️ 训练样本: 241, 测试样本: 3[0m
[1;96m💾 当前显存使用: 0.64 GB[0m
[1;96m📊 预加载进度: 9/26 - sub09[0m
[1;96m📥 正在预加载受试者 sub09 的数据...[0m
[1;92m✅ 受试者 sub09 数据预加载完成[0m
[1;96mℹ️ 训练样本: 231, 测试样本: 13[0m
[1;96m💾 当前显存使用: 0.72 GB[0m
[1;96m📊 预加载进度: 10/26 - sub10[0m
[1;96m📥 正在预加载受试者 sub10 的数据...[0m
[1;92m✅ 受试者 sub10 数据预加载完成[0m
[1;96mℹ️ 训练样本: 231, 测试样本: 13[0m
[1;96m💾 当前显存使用: 0.80 GB[0m
[1;96m📊 预加载进度: 11/26 - sub11[0m
[1;96m📥 正在预加载受试者 sub11 的数据...[0m
[1;92m✅ 受试者 sub11 数据预加载完成[0m
[1;96mℹ️ 训练样本: 234, 测试样本: 10[0m
[1;96m💾 当前显存使用: 0.88 GB[0m
[1;96m📊 预加载进度: 12/26 - sub12[0m
[1;96m📥 正在预加载受试者 sub12 的数据...[0m
[1;92m✅ 受试者 sub12 数据预加载完成[0m
[1;96mℹ️ 训练样本: 232, 测试样本: 12[0m
[1;96m💾 当前显存使用: 0.96 GB[0m
[1;96m📊 预加载进度: 13/26 - sub13[0m
[1;96m📥 正在预加载受试者 sub13 的数据...[0m
[1;92m✅ 受试者 sub13 数据预加载完成[0m
[1;96mℹ️ 训练样本: 236, 测试样本: 8[0m
[1;96m💾 当前显存使用: 1.04 GB[0m
[1;96m📊 预加载进度: 14/26 - sub14[0m
[1;96m📥 正在预加载受试者 sub14 的数据...[0m
[1;92m✅ 受试者 sub14 数据预加载完成[0m
[1;96mℹ️ 训练样本: 240, 测试样本: 4[0m
[1;96m💾 当前显存使用: 1.12 GB[0m
[1;96m📊 预加载进度: 15/26 - sub15[0m
[1;96m📥 正在预加载受试者 sub15 的数据...[0m
[1;92m✅ 受试者 sub15 数据预加载完成[0m
[1;96mℹ️ 训练样本: 241, 测试样本: 3[0m
[1;96m💾 当前显存使用: 1.20 GB[0m
[1;96m📊 预加载进度: 16/26 - sub16[0m
[1;96m📥 正在预加载受试者 sub16 的数据...[0m
[1;92m✅ 受试者 sub16 数据预加载完成[0m
[1;96mℹ️ 训练样本: 241, 测试样本: 3[0m
[1;96m💾 当前显存使用: 1.28 GB[0m
[1;96m📊 预加载进度: 17/26 - sub17[0m
[1;96m📥 正在预加载受试者 sub17 的数据...[0m
[1;92m✅ 受试者 sub17 数据预加载完成[0m
[1;96mℹ️ 训练样本: 210, 测试样本: 34[0m
[1;96m💾 当前显存使用: 1.36 GB[0m
[1;96m📊 预加载进度: 18/26 - sub18[0m
[1;96m📥 正在预加载受试者 sub18 的数据...[0m
[1;92m✅ 受试者 sub18 数据预加载完成[0m
[1;96mℹ️ 训练样本: 241, 测试样本: 3[0m
[1;96m💾 当前显存使用: 1.44 GB[0m
[1;96m📊 预加载进度: 19/26 - sub19[0m
[1;96m📥 正在预加载受试者 sub19 的数据...[0m
[1;92m✅ 受试者 sub19 数据预加载完成[0m
[1;96mℹ️ 训练样本: 229, 测试样本: 15[0m
[1;96m💾 当前显存使用: 1.52 GB[0m
[1;96m📊 预加载进度: 20/26 - sub20[0m
[1;96m📥 正在预加载受试者 sub20 的数据...[0m
[1;92m✅ 受试者 sub20 数据预加载完成[0m
[1;96mℹ️ 训练样本: 233, 测试样本: 11[0m
[1;96m💾 当前显存使用: 1.60 GB[0m
[1;96m📊 预加载进度: 21/26 - sub21[0m
[1;96m📥 正在预加载受试者 sub21 的数据...[0m
[1;92m✅ 受试者 sub21 数据预加载完成[0m
[1;96mℹ️ 训练样本: 242, 测试样本: 2[0m
[1;96m💾 当前显存使用: 1.68 GB[0m
[1;96m📊 预加载进度: 22/26 - sub22[0m
[1;96m📥 正在预加载受试者 sub22 的数据...[0m
[1;92m✅ 受试者 sub22 数据预加载完成[0m
[1;96mℹ️ 训练样本: 242, 测试样本: 2[0m
[1;96m💾 当前显存使用: 1.76 GB[0m
[1;96m📊 预加载进度: 23/26 - sub23[0m
[1;96m📥 正在预加载受试者 sub23 的数据...[0m
[1;92m✅ 受试者 sub23 数据预加载完成[0m
[1;96mℹ️ 训练样本: 232, 测试样本: 12[0m
[1;96m💾 当前显存使用: 1.84 GB[0m
[1;96m📊 预加载进度: 24/26 - sub24[0m
[1;96m📥 正在预加载受试者 sub24 的数据...[0m
[1;92m✅ 受试者 sub24 数据预加载完成[0m
[1;96mℹ️ 训练样本: 237, 测试样本: 7[0m
[1;96m💾 当前显存使用: 1.92 GB[0m
[1;96m📊 预加载进度: 25/26 - sub25[0m
[1;96m📥 正在预加载受试者 sub25 的数据...[0m
[1;92m✅ 受试者 sub25 数据预加载完成[0m
[1;96mℹ️ 训练样本: 237, 测试样本: 7[0m
[1;96m💾 当前显存使用: 2.00 GB[0m
[1;96m📊 预加载进度: 26/26 - sub26[0m
[1;96m📥 正在预加载受试者 sub26 的数据...[0m
[1;92m✅ 受试者 sub26 数据预加载完成[0m
[1;96mℹ️ 训练样本: 228, 测试样本: 16[0m
[1;96m💾 当前显存使用: 2.08 GB[0m

[1;92m===========[0m
[1m[1;92m✅ 数据预加载完成 ✅[0m
[1;92m===========[0m

[1;92m✅ 成功预加载: 26 个受试者[0m
[1m[1;95m💾 最终显存使用: 2.08 GB[0m
根据配置,不使用Visdom可视化

[1;95m=====================[0m
[1m[1;95m🚀 使用GPU预加载 + 并行训练模式 🚀[0m
[1;95m=====================[0m

[1;96mℹ️ 可用受试者: 25 个[0m
[1;96mℹ️ 受试者列表: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub10', 'sub23', 'sub12', 'sub20', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub04', 'sub06', 'sub13', 'sub08', 'sub15', 'sub16', 'sub18', 'sub21', 'sub22'][0m

[1;95m==================[0m
[1m[1;95m⚡ 开始并行训练 25 个受试者 ⚡[0m
[1;95m==================[0m

[1;96mℹ️ 并行工作线程数: 4[0m
[1;96m🎯 开始训练受试者: sub17[0m
[1;96mℹ️ 训练样本: 210, 测试样本: 34[0m[1;96m🎯 开始训练受试者: sub05[0m

[1;96mℹ️ 训练样本: 225, 测试样本: 19[0m[1;96m🎯 开始训练受试者: sub26[0m

[1;96mℹ️ 训练样本: 228, 测试样本: 16[0m[1;96m🎯 开始训练受试者: sub19[0m

[1;96mℹ️ 训练样本: 229, 测试样本: 15[0m
[1;96mℹ️ 训练数据形状: torch.Size([210, 38, 48, 48]), 测试数据形状: torch.Size([34, 38, 48, 48])[0m[1;96mℹ️ 训练数据形状: torch.Size([225, 38, 48, 48]), 测试数据形状: torch.Size([19, 38, 48, 48])[0m[1;96mℹ️ 训练数据形状: torch.Size([229, 38, 48, 48]), 测试数据形状: torch.Size([15, 38, 48, 48])[0m

[1;96mℹ️ 训练数据形状: torch.Size([228, 38, 48, 48]), 测试数据形状: torch.Size([16, 38, 48, 48])[0m
🌊 小波配置: 类型=bior2.2, 层数=2🌊 小波配置: 类型=bior2.2, 层数=2


🌊 小波配置: 类型=bior2.2, 层数=2🌊 小波配置: 类型=bior2.2, 层数=2

🔧 激活函数配置: 标准ReLU
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8=> 使用折叠除法: 8


=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8
=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8=> 使用折叠除法: 8


=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

🔥 启用三模态特征融合: attention🔥 启用三模态特征融合: attention

🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
✅ 三模态融合模块初始化完成
✅ 三模态融合模块初始化完成
✅ 三模态融合模块初始化完成
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck注意力融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_attn_h8.pth[0m
[1;96mℹ️ 加载了 350/356 个层[0m
[1;92m✅ 成功加载预训练模型: ck注意力融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_attn_h8.pth[0m
[1;96mℹ️ 加载了 350/356 个层[0m
[1;92m✅ 成功加载预训练模型: ck注意力融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_attn_h8.pth[0m
[1;96mℹ️ 加载了 350/356 个层[0m
[1;92m✅ 成功加载预训练模型: ck注意力融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_attn_h8.pth[0m
[1;96mℹ️ 加载了 350/356 个层[0m
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化
警告: 找不到主体 sub17 的数据目录,跳过
警告: 找不到主体 sub05 的数据目录,跳过
警告: 找不到主体 sub26 的数据目录,跳过
警告: 找不到主体 sub19 的数据目录,跳过
警告: 找不到主体 sub09 的数据目录,跳过
警告: 找不到主体 sub02 的数据目录,跳过
警告: 找不到主体 sub10 的数据目录,跳过
警告: 找不到主体 sub23 的数据目录,跳过
警告: 找不到主体 sub12 的数据目录,跳过
警告: 找不到主体 sub20 的数据目录,跳过
警告: 找不到主体 sub11 的数据目录,跳过
警告: 找不到主体 sub01 的数据目录,跳过
警告: 找不到主体 sub07 的数据目录,跳过
警告: 找不到主体 sub24 的数据目录,跳过
警告: 找不到主体 sub25 的数据目录,跳过
警告: 找不到主体 sub03 的数据目录,跳过
警告: 找不到主体 sub04 的数据目录,跳过
警告: 找不到主体 sub06 的数据目录,跳过
警告: 找不到主体 sub13 的数据目录,跳过
警告: 找不到主体 sub08 的数据目录,跳过
警告: 找不到主体 sub15 的数据目录,跳过
警告: 找不到主体 sub16 的数据目录,跳过
警告: 找不到主体 sub18 的数据目录,跳过
警告: 找不到主体 sub21 的数据目录,跳过
警告: 找不到主体 sub22 的数据目录,跳过

================================================================================
【实验结果报告】
================================================================================

【基本信息】
实验名称: SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn
时间: 2025-07-29 15:06:46
总训练时间: 00:00:00
数据集: CASME2_LOSO_full

【系统环境】
操作系统: 💻 Linux 6.8.0-1031-nvidia (x86_64)
处理器: ⚡ 32核心/32线程 @ 2195MHz
内存: 🧠 62.7GB RAM (已用: 22.9GB, 36.5%)
GPU: 🚀 NVIDIA A800 80GB PCIe (79.3GB)

【模型配置】
模型架构: SKD_TSTSAN
是否使用预训练: 是
预训练模型: ck注意力融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_attn_h8.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
批次大小: 32
最大迭代次数: 20000
损失函数: FocalLoss_weighted
随机种子: 2577

【GPU性能配置】
CUDNN Benchmark: 禁用
CUDNN Deterministic: 禁用
混合精度训练: 禁用
TF32加速: 启用
BatchNorm修复: 禁用
卷积类型: 小波变换卷积

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: N/A
- UAR分数: N/A

【各表情类别准确率】
--------------------------------------------------

【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
    平均        nan        nan        nan    


【各受试者详细预测结果】
==================================================

================================================================================

【错误】邮件处理过程出错: '>=' not supported between instances of 'NoneType' and 'float'
详细错误信息: Traceback (most recent call last):
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSANCASME2.py", line 757, in send_training_results
    performance_recommendations = generate_performance_recommendations(results_dict, config, total_time)
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSANCASME2.py", line 498, in generate_performance_recommendations
    if overall_uf1 >= 0.8 and overall_uar >= 0.8:
TypeError: '>=' not supported between instances of 'NoneType' and 'float'


训练完成!

正在关闭日志系统...
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签: [4, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m🔥 启用三模态特征融合:[0m
  [1;96m多头注意力融合[0m 🧠
  [96m- 建立跨模态注意力关系[0m 🔗
  [96m- 动态关注重要特征[0m 🎯
  [1;96m- 注意力头数: 8[0m 🎯
[93m⏰ 使用固定训练轮数[0m

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

[1m[1;92m🚀 GPU数据预加载[0m
[1;92m------------[0m
[1;96m📤 正在将所有数据预加载到GPU内存...[0m
[1;96m📊 训练数据大小: 0.27 GB[0m
[1;96m📊 测试数据大小: 0.06 GB[0m
[1;96m📊 总数据大小: 0.32 GB[0m
[1;96m⬆️ 正在上传训练数据到GPU...[0m
[1;96m⬆️ 正在上传测试数据到GPU...[0m
[1;92m✅ 数据预加载到GPU完成！[0m
[1;96m💾 GPU内存使用: GPU0: 0.3GB/79.3GB (0.4%)[0m
[1;96m⚡ GPU预加载模式：数据传输将极大加速！[0m

[1m[1;96m📊 损失输出配置[0m
[1;96m----------[0m
[1;96m🔍 详细损失信息: 禁用[0m
[1;96m📈 Epoch损失统计: 禁用[0m
[1;96m📉 验证损失统计: 禁用[0m
[1;96m🚨 过拟合监控: 禁用[0m
[1;96m📦 批次损失详情: 禁用[0m
[1;96m❌ Epoch损失统计将显示: 否[0m
[1;96m❌ 验证损失统计将显示: 否[0m
[1;96m❌ 过拟合监控将显示: 否[0m

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m
正在关闭TensorBoard写入器...
TensorBoard写入器已关闭

训练过程出错: Caught RuntimeError in DataLoader worker process 0.
Original Traceback (most recent call last):
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/utils/data/_utils/worker.py", line 309, in _worker_loop
    data = fetcher.fetch(index)  # type: ignore[possibly-undefined]
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/utils/data/_utils/fetch.py", line 52, in fetch
    data = [self.dataset[idx] for idx in possibly_batched_index]
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/utils/data/_utils/fetch.py", line 52, in <listcomp>
    data = [self.dataset[idx] for idx in possibly_batched_index]
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/utils/data/dataset.py", line 211, in __getitem__
    return tuple(tensor[index] for tensor in self.tensors)
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/utils/data/dataset.py", line 211, in <genexpr>
    return tuple(tensor[index] for tensor in self.tensors)
RuntimeError: CUDA error: initialization error
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.


详细错误信息: Traceback (most recent call last):
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSANCASME2.py", line 1938, in <module>
    results_dict = main_SKD_TSTSAN_with_Aug_with_SKD(config)
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN_functions_CASME2.py", line 1860, in main_SKD_TSTSAN_with_Aug_with_SKD
    for batch_idx, batch in enumerate(train_dl):
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 630, in __next__
    data = self._next_data()
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1344, in _next_data
    return self._process_data(data)
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1370, in _process_data
    data.reraise()
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/_utils.py", line 706, in reraise
    raise exception
RuntimeError: Caught RuntimeError in DataLoader worker process 0.
Original Traceback (most recent call last):
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/utils/data/_utils/worker.py", line 309, in _worker_loop
    data = fetcher.fetch(index)  # type: ignore[possibly-undefined]
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/utils/data/_utils/fetch.py", line 52, in fetch
    data = [self.dataset[idx] for idx in possibly_batched_index]
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/utils/data/_utils/fetch.py", line 52, in <listcomp>
    data = [self.dataset[idx] for idx in possibly_batched_index]
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/utils/data/dataset.py", line 211, in __getitem__
    return tuple(tensor[index] for tensor in self.tensors)
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/utils/data/dataset.py", line 211, in <genexpr>
    return tuple(tensor[index] for tensor in self.tensors)
RuntimeError: CUDA error: initialization error
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.




【实验结果报告】
================================================================================

================================================================================
【🎯 深度学习微表情识别实验报告 - 5分类 (快乐、惊讶、厌恶、压抑、其他)】
================================================================================

【📋 基本信息】
实验名称: SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn
分类方案: 5分类 (快乐、惊讶、厌恶、压抑、其他)
完成时间: 2025-07-29 15:10:23
总训练时间: 00:00:20
数据集路径: /home/<USER>/data/ajq/SKD-TSTSAN-data/CASME2_LOSO_full
数据集名称: CASME2_LOSO_full


【💻 详细系统环境】
操作系统: 💻 Linux 6.8.0-1031-nvidia (x86_64)
处理器: ⚡ 32核心/32线程 @ 2195MHz
内存状态: 🧠 62.7GB RAM (已用: 23.6GB, 37.6%)
GPU信息: 🚀 NVIDIA A800 80GB PCIe (79.3GB)
GPU利用率: 99%
磁盘状态: 💾 97.4GB 总计 (可用: 51.0GB, 已用: 42.5%)
Python版本: 🐍 Python 3.8.20
PyTorch版本: 🔥 PyTorch 2.4.1+cu121
CUDA版本: ⚡ CUDA 12.1
cuDNN版本: 🧠 cuDNN 90100

【📊 训练统计信息】
受试者数量: 0
平均每受试者训练时间: N/A
总迭代次数: 20000
理论样本处理量: N/A
GPU加速: ✅ 启用
多GPU训练: ❌ 单GPU
混合精度: ❌ 禁用

【🤖 模型架构配置】
模型名称: SKD_TSTSAN
分类类别数: 5
预训练模型: ✅ ck注意力融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_attn_h8.pth
COCO预训练: ✅ 启用
模型保存: ✅ 启用
卷积类型: 小波变换卷积

【⚙️ 训练超参数】
学习率: 0.001
基础批次大小: 32
最大迭代次数: 20000
损失函数: FocalLoss_weighted
随机种子: 2577
温度参数: 3
蒸馏α值: 0.1
蒸馏β值: 1e-06
数据增强系数: 2

【🚀 GPU性能优化配置】
CUDNN Benchmark: ❌ 禁用
CUDNN Deterministic: ❌ 禁用
混合精度训练: ❌ 禁用
TF32加速: ✅ 启用
BatchNorm修复: ❌ 禁用

【🔄 数据增强策略】
训练数据增强: ✅ 启用
测试数据增强: ✅ 启用
旋转角度范围: 3,8
训练增强倍数: 6,8,3,8,2
测试增强倍数: 6,8,3,8,2
测试镜像训练: ✅ 启用
镜像训练受试者: sub02,sub05,sub26

【🎯 核心性能指标】
总体性能:
- 🏆 UF1分数: 0.0000
- 📈 UAR分数: 0.0000

【📊 各表情类别准确率】
--------------------------------------------------
- 暂无各类别准确率数据


【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
    平均        nan        nan        nan    

❌ 无法生成混淆矩阵：缺少预测或真实标签数据

【性能分析与改进建议】
============================================================
📊 总体性能评估:
   ❌ 较差: 模型性能需要显著改进

⏱️ 训练效率分析:

💡 改进建议:
   🔧 模型性能优化:
      - 尝试调整学习率 (当前: 0.0010)
      - 增加训练迭代次数 (当前: 20000)
      - 考虑使用数据增强技术
      - 尝试不同的预训练模型
   ⚡ 性能优化:
      - 启用混合精度训练以提高速度

============================================================

【📝 实验总结】
============================================================
本次实验采用5分类 (快乐、惊讶、厌恶、压抑、其他)方案，在CASME2_LOSO_full数据集上进行训练。
总体UF1得分: 0.0000, UAR得分: 0.0000
训练耗时: 00:00:20
受试者数量: 0

实验配置摘要:
- 模型: SKD_TSTSAN
- 学习率: 0.001
- 批次大小: 32
- 迭代次数: 20000
- 预训练: 是

================================================================================

================================================================================

【邮件通知】正在发送训练结果...

【邮件通知】邮件发送成功!
- 发件人: <EMAIL>
- 收件人: <EMAIL>
- 主题: [5分类 (快乐、惊讶、厌恶、压抑、其他)实验总结] SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn - UF1=0.0000, UAR=0.0000

正在关闭日志系统...
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签: [4, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
TensorBoard日志目录已存在: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m🔥 启用三模态特征融合:[0m
  [1;96m多头注意力融合[0m 🧠
  [96m- 建立跨模态注意力关系[0m 🔗
  [96m- 动态关注重要特征[0m 🎯
  [1;96m- 注意力头数: 8[0m 🎯
[93m⏰ 使用固定训练轮数[0m

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

[1m[1;92m🚀 GPU数据预加载[0m
[1;92m------------[0m
[1;96m📤 正在将所有数据预加载到GPU内存...[0m
[1;96m📊 训练数据大小: 0.27 GB[0m
[1;96m📊 测试数据大小: 0.06 GB[0m
[1;96m📊 总数据大小: 0.32 GB[0m
[1;96m⬆️ 正在上传训练数据到GPU...[0m
[1;96m⬆️ 正在上传测试数据到GPU...[0m
[1;92m✅ 数据预加载到GPU完成！[0m
[1;96m💾 GPU内存使用: GPU0: 0.3GB/79.3GB (0.4%)[0m
[1;96m⚡ GPU预加载模式：数据传输将极大加速！[0m

[1m[1;96m📊 损失输出配置[0m
[1;96m----------[0m
[1;96m🔍 详细损失信息: 禁用[0m
[1;96m📈 Epoch损失统计: 禁用[0m
[1;96m📉 验证损失统计: 禁用[0m
[1;96m🚨 过拟合监控: 禁用[0m
[1;96m📦 批次损失详情: 禁用[0m
[1;96m❌ Epoch损失统计将显示: 否[0m
[1;96m❌ 验证损失统计将显示: 否[0m
[1;96m❌ 过拟合监控将显示: 否[0m

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 1 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.5160[0m (420/814)
   • 中间层1准确率: [96m0.4840[0m (394/814)
   • 中间层2准确率: [96m0.5221[0m (425/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 1 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.0000[0m
📊 [1mUF1:[0m [1;92m0.2892[0m | [1mUAR:[0m [1;92m0.3571[0m
🥇 [1m最佳UF1:[0m [1;93m0.0000[0m | [1m最佳UAR:[0m [1;93m0.0000[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.5882 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.2892 | UAR: 0.3571 | 准确率: 0.5882[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth

[1m[1;96m📅 Epoch 2/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 2 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7113[0m (579/814)
   • 中间层1准确率: [96m0.6732[0m (548/814)
   • 中间层2准确率: [96m0.7039[0m (573/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 2 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.5882[0m
📊 [1mUF1:[0m [1;92m0.3412[0m | [1mUAR:[0m [1;92m0.3619[0m
🥇 [1m最佳UF1:[0m [1;93m0.2892[0m | [1m最佳UAR:[0m [1;93m0.3571[0m
============================================================

[1;92m💾 保存最佳模型，相同准确率下UF1和UAR都提升: UF1(0.3412↑) UAR(0.3619↑)[0m
[1;96m📊 UF1: 0.3412 | UAR: 0.3619 | 准确率: 0.5882[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth

[1m[1;96m📅 Epoch 3/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 3 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7494[0m (610/814)
   • 中间层1准确率: [96m0.7088[0m (577/814)
   • 中间层2准确率: [96m0.7469[0m (608/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 3 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.5882[0m
📊 [1mUF1:[0m [1;92m0.5603[0m | [1mUAR:[0m [1;92m0.6286[0m
🥇 [1m最佳UF1:[0m [1;93m0.3412[0m | [1m最佳UAR:[0m [1;93m0.3619[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.6765 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.5603 | UAR: 0.6286 | 准确率: 0.6765[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth

[1m[1;96m📅 Epoch 4/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 4 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8157[0m (664/814)
   • 中间层1准确率: [96m0.8071[0m (657/814)
   • 中间层2准确率: [96m0.8391[0m (683/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 4 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.4251[0m | [1mUAR:[0m [1;96m0.4667[0m
🥇 [1m最佳UF1:[0m [1;93m0.5603[0m | [1m最佳UAR:[0m [1;93m0.6286[0m
============================================================

[1;93m⚠️ ❌ 准确率提升(0.7059)但UF1(0.4251↓)和UAR(0.4667↓)都下降，不保存[0m

[1m[1;96m📅 Epoch 5/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 5 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8563[0m (697/814)
   • 中间层1准确率: [96m0.8428[0m (686/814)
   • 中间层2准确率: [96m0.8563[0m (697/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 5 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7353[0m (25/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;92m0.6344[0m | [1mUAR:[0m [1;92m0.6937[0m
🥇 [1m最佳UF1:[0m [1;93m0.5603[0m | [1m最佳UAR:[0m [1;93m0.6286[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.7353 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.6344 | UAR: 0.6937 | 准确率: 0.7353[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth

[1m[1;96m📅 Epoch 6/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 6 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8698[0m (708/814)
   • 中间层1准确率: [96m0.8243[0m (671/814)
   • 中间层2准确率: [96m0.8833[0m (719/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 6 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7353[0m (25/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.5131[0m | [1mUAR:[0m [1;96m0.5095[0m
🥇 [1m最佳UF1:[0m [1;93m0.6344[0m | [1m最佳UAR:[0m [1;93m0.6937[0m
============================================================


[1m[1;96m📅 Epoch 7/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 7 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8575[0m (698/814)
   • 中间层1准确率: [96m0.8489[0m (691/814)
   • 中间层2准确率: [96m0.8575[0m (698/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 7 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7353[0m (25/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;92m0.6560[0m | [1mUAR:[0m [1;92m0.7524[0m
🥇 [1m最佳UF1:[0m [1;93m0.6344[0m | [1m最佳UAR:[0m [1;93m0.6937[0m
============================================================

[1;92m💾 保存最佳模型，相同准确率下UF1和UAR都提升: UF1(0.6560↑) UAR(0.7524↑)[0m
[1;96m📊 UF1: 0.6560 | UAR: 0.7524 | 准确率: 0.7353[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth

[1m[1;96m📅 Epoch 8/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 8 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9029[0m (735/814)
   • 中间层1准确率: [96m0.8563[0m (697/814)
   • 中间层2准确率: [96m0.9029[0m (735/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 8 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;92m0.6980[0m | [1mUAR:[0m [1;96m0.7333[0m
🥇 [1m最佳UF1:[0m [1;93m0.6560[0m | [1m最佳UAR:[0m [1;93m0.7524[0m
============================================================


[1m[1;96m📅 Epoch 9/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 9 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8931[0m (727/814)
   • 中间层1准确率: [96m0.8931[0m (727/814)
   • 中间层2准确率: [96m0.8943[0m (728/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 9 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.4667[0m | [1mUAR:[0m [1;96m0.4873[0m
🥇 [1m最佳UF1:[0m [1;93m0.6560[0m | [1m最佳UAR:[0m [1;93m0.7524[0m
============================================================


[1m[1;96m📅 Epoch 10/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 10 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8956[0m (729/814)
   • 中间层1准确率: [96m0.8931[0m (727/814)
   • 中间层2准确率: [96m0.8857[0m (721/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 10 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.5402[0m | [1mUAR:[0m [1;96m0.6143[0m
🥇 [1m最佳UF1:[0m [1;93m0.6560[0m | [1m最佳UAR:[0m [1;93m0.7524[0m
============================================================


[1m[1;96m📅 Epoch 11/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 11 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9128[0m (743/814)
   • 中间层1准确率: [96m0.9251[0m (753/814)
   • 中间层2准确率: [96m0.9386[0m (764/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 11 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.3690[0m | [1mUAR:[0m [1;96m0.4143[0m
🥇 [1m最佳UF1:[0m [1;93m0.6560[0m | [1m最佳UAR:[0m [1;93m0.7524[0m
============================================================


[1m[1;96m📅 Epoch 12/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 12 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9361[0m (762/814)
   • 中间层1准确率: [96m0.9361[0m (762/814)
   • 中间层2准确率: [96m0.9423[0m (767/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 12 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.5426[0m | [1mUAR:[0m [1;96m0.6603[0m
🥇 [1m最佳UF1:[0m [1;93m0.6560[0m | [1m最佳UAR:[0m [1;93m0.7524[0m
============================================================


[1m[1;96m📅 Epoch 13/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 13 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9214[0m (750/814)
   • 中间层1准确率: [96m0.9103[0m (741/814)
   • 中间层2准确率: [96m0.9189[0m (748/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 13 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.3141[0m | [1mUAR:[0m [1;96m0.4571[0m
🥇 [1m最佳UF1:[0m [1;93m0.6560[0m | [1m最佳UAR:[0m [1;93m0.7524[0m
============================================================


[1m[1;96m📅 Epoch 14/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 14 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9349[0m (761/814)
   • 中间层1准确率: [96m0.9373[0m (763/814)
   • 中间层2准确率: [96m0.9423[0m (767/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 14 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.3636[0m | [1mUAR:[0m [1;96m0.3698[0m
🥇 [1m最佳UF1:[0m [1;93m0.6560[0m | [1m最佳UAR:[0m [1;93m0.7524[0m
============================================================


[1m[1;96m📅 Epoch 15/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 15 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9189[0m (748/814)
   • 中间层1准确率: [96m0.9115[0m (742/814)
   • 中间层2准确率: [96m0.9165[0m (746/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 15 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.5967[0m | [1mUAR:[0m [1;96m0.5905[0m
🥇 [1m最佳UF1:[0m [1;93m0.6560[0m | [1m最佳UAR:[0m [1;93m0.7524[0m
============================================================


[1m[1;96m📅 Epoch 16/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 16 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9312[0m (758/814)
   • 中间层1准确率: [96m0.9472[0m (771/814)
   • 中间层2准确率: [96m0.9300[0m (757/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 16 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.4976[0m | [1mUAR:[0m [1;96m0.6746[0m
🥇 [1m最佳UF1:[0m [1;93m0.6560[0m | [1m最佳UAR:[0m [1;93m0.7524[0m
============================================================


[1m[1;96m📅 Epoch 17/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 17 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9214[0m (750/814)
   • 中间层1准确率: [96m0.9447[0m (769/814)
   • 中间层2准确率: [96m0.9226[0m (751/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 17 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.4430[0m | [1mUAR:[0m [1;96m0.5048[0m
🥇 [1m最佳UF1:[0m [1;93m0.6560[0m | [1m最佳UAR:[0m [1;93m0.7524[0m
============================================================


[1m[1;96m📅 Epoch 18/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 18 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8784[0m (715/814)
   • 中间层1准确率: [96m0.8870[0m (722/814)
   • 中间层2准确率: [96m0.9005[0m (733/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 18 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.4185[0m | [1mUAR:[0m [1;96m0.4143[0m
🥇 [1m最佳UF1:[0m [1;93m0.6560[0m | [1m最佳UAR:[0m [1;93m0.7524[0m
============================================================


[1m[1;96m📅 Epoch 19/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 19 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9484[0m (772/814)
   • 中间层1准确率: [96m0.9410[0m (766/814)
   • 中间层2准确率: [96m0.9595[0m (781/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 19 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7647[0m (26/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;92m0.6968[0m | [1mUAR:[0m [1;96m0.7095[0m
🥇 [1m最佳UF1:[0m [1;93m0.6560[0m | [1m最佳UAR:[0m [1;93m0.7524[0m
============================================================

[1;93m⚠️ ❌ 准确率提升(0.7647)但UAR下降(0.7095↓)，不保存[0m

[1m[1;96m📅 Epoch 20/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 20 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9889[0m (805/814)
   • 中间层1准确率: [96m0.9742[0m (793/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 20 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.6330[0m | [1mUAR:[0m [1;96m0.6889[0m
🥇 [1m最佳UF1:[0m [1;93m0.6560[0m | [1m最佳UAR:[0m [1;93m0.7524[0m
============================================================


[1m[1;96m📅 Epoch 21/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 21 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9840[0m (801/814)
   • 中间层2准确率: [96m0.9914[0m (807/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 21 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.5463[0m | [1mUAR:[0m [1;96m0.6333[0m
🥇 [1m最佳UF1:[0m [1;93m0.6560[0m | [1m最佳UAR:[0m [1;93m0.7524[0m
============================================================


[1m[1;96m📅 Epoch 22/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 22 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9828[0m (800/814)
   • 中间层1准确率: [96m0.9668[0m (787/814)
   • 中间层2准确率: [96m0.9742[0m (793/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 22 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.6256[0m | [1mUAR:[0m [1;96m0.6810[0m
🥇 [1m最佳UF1:[0m [1;93m0.6560[0m | [1m最佳UAR:[0m [1;93m0.7524[0m
============================================================


[1m[1;96m📅 Epoch 23/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 23 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9447[0m (769/814)
   • 中间层1准确率: [96m0.9484[0m (772/814)
   • 中间层2准确率: [96m0.9312[0m (758/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 23 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.6155[0m | [1mUAR:[0m [1;96m0.6841[0m
🥇 [1m最佳UF1:[0m [1;93m0.6560[0m | [1m最佳UAR:[0m [1;93m0.7524[0m
============================================================


[1m[1;96m📅 Epoch 24/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 24 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9767[0m (795/814)
   • 中间层1准确率: [96m0.9558[0m (778/814)
   • 中间层2准确率: [96m0.9717[0m (791/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 24 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.3321[0m | [1mUAR:[0m [1;96m0.3714[0m
🥇 [1m最佳UF1:[0m [1;93m0.6560[0m | [1m最佳UAR:[0m [1;93m0.7524[0m
============================================================


[1m[1;96m📅 Epoch 25/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 25 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9644[0m (785/814)
   • 中间层2准确率: [96m0.9853[0m (802/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 25 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.4191[0m | [1mUAR:[0m [1;96m0.4206[0m
🥇 [1m最佳UF1:[0m [1;93m0.6560[0m | [1m最佳UAR:[0m [1;93m0.7524[0m
============================================================


[1m[1;96m📅 Epoch 26/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 26 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9521[0m (775/814)
   • 中间层1准确率: [96m0.9607[0m (782/814)
   • 中间层2准确率: [96m0.9607[0m (782/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 26 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.4559[0m | [1mUAR:[0m [1;96m0.5873[0m
🥇 [1m最佳UF1:[0m [1;93m0.6560[0m | [1m最佳UAR:[0m [1;93m0.7524[0m
============================================================


[1m[1;96m📅 Epoch 27/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 27 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9779[0m (796/814)
   • 中间层1准确率: [96m0.9730[0m (792/814)
   • 中间层2准确率: [96m0.9730[0m (792/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 27 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;92m0.7011[0m | [1mUAR:[0m [1;92m0.7889[0m
🥇 [1m最佳UF1:[0m [1;93m0.6560[0m | [1m最佳UAR:[0m [1;93m0.7524[0m
============================================================

[1;92m💾 保存最佳模型，UF1和UAR都显著提升: UF1(0.7011↑) UAR(0.7889↑)[0m
[1;96m📊 UF1: 0.7011 | UAR: 0.7889 | 准确率: 0.6471[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth

[1m[1;96m📅 Epoch 28/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 28 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9693[0m (789/814)
   • 中间层1准确率: [96m0.9472[0m (771/814)
   • 中间层2准确率: [96m0.9717[0m (791/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 28 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.4517[0m | [1mUAR:[0m [1;96m0.6397[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 29/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 29 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9619[0m (783/814)
   • 中间层1准确率: [96m0.9545[0m (777/814)
   • 中间层2准确率: [96m0.9631[0m (784/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 29 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.5881[0m | [1mUAR:[0m [1;96m0.6143[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 30/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 30 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9398[0m (765/814)
   • 中间层1准确率: [96m0.9558[0m (778/814)
   • 中间层2准确率: [96m0.9373[0m (763/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 30 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.4519[0m | [1mUAR:[0m [1;96m0.4825[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 31/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 31 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9570[0m (779/814)
   • 中间层1准确率: [96m0.9644[0m (785/814)
   • 中间层2准确率: [96m0.9607[0m (782/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 31 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.4143[0m | [1mUAR:[0m [1;96m0.6032[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 32/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 32 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9631[0m (784/814)
   • 中间层1准确率: [96m0.9742[0m (793/814)
   • 中间层2准确率: [96m0.9656[0m (786/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 32 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.3517[0m | [1mUAR:[0m [1;96m0.4540[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 33/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 33 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9767[0m (795/814)
   • 中间层1准确率: [96m0.9742[0m (793/814)
   • 中间层2准确率: [96m0.9742[0m (793/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 33 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.5419[0m | [1mUAR:[0m [1;96m0.6476[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 34/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 34 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 34 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.6275[0m | [1mUAR:[0m [1;96m0.6889[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 35/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 35 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9914[0m (807/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 35 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.5384[0m | [1mUAR:[0m [1;96m0.5746[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================

[1;93m⚠️ ❌ 准确率提升(0.7059)但UF1(0.5384↓)和UAR(0.5746↓)都下降，不保存[0m

[1m[1;96m📅 Epoch 36/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 36 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 36 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.5508[0m | [1mUAR:[0m [1;96m0.6746[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 37/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 37 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 37 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.6506[0m | [1mUAR:[0m [1;96m0.7111[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 38/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 38 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 38 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.4482[0m | [1mUAR:[0m [1;96m0.4968[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 39/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 39 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9828[0m (800/814)
   • 中间层1准确率: [96m0.9754[0m (794/814)
   • 中间层2准确率: [96m0.9816[0m (799/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 39 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;92m0.7292[0m | [1mUAR:[0m [1;96m0.7762[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================

[1;93m⚠️ ❌ 准确率提升(0.7059)但UAR下降(0.7762↓)，不保存[0m

[1m[1;96m📅 Epoch 40/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 40 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9509[0m (774/814)
   • 中间层1准确率: [96m0.9496[0m (773/814)
   • 中间层2准确率: [96m0.9570[0m (779/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 40 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.5698[0m | [1mUAR:[0m [1;96m0.6889[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 41/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 41 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9607[0m (782/814)
   • 中间层1准确率: [96m0.9509[0m (774/814)
   • 中间层2准确率: [96m0.9619[0m (783/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 41 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.5714[0m | [1mUAR:[0m [1;96m0.7032[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 42/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 42 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9705[0m (790/814)
   • 中间层1准确率: [96m0.9730[0m (792/814)
   • 中间层2准确率: [96m0.9705[0m (790/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 42 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.4906[0m | [1mUAR:[0m [1;96m0.5730[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 43/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 43 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9373[0m (763/814)
   • 中间层1准确率: [96m0.9607[0m (782/814)
   • 中间层2准确率: [96m0.9349[0m (761/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 43 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.4263[0m | [1mUAR:[0m [1;96m0.6175[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 44/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 44 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9275[0m (755/814)
   • 中间层1准确率: [96m0.9582[0m (780/814)
   • 中间层2准确率: [96m0.9398[0m (765/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 44 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.5121[0m | [1mUAR:[0m [1;96m0.5921[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 45/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 45 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9619[0m (783/814)
   • 中间层1准确率: [96m0.9558[0m (778/814)
   • 中间层2准确率: [96m0.9656[0m (786/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 45 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.6573[0m | [1mUAR:[0m [1;96m0.6365[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 46/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 46 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9889[0m (805/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 46 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.6376[0m | [1mUAR:[0m [1;96m0.6889[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 47/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 47 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 47 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.6256[0m | [1mUAR:[0m [1;96m0.6810[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 48/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 48 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 48 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.6203[0m | [1mUAR:[0m [1;96m0.6683[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 49/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 49 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 49 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.6337[0m | [1mUAR:[0m [1;96m0.6810[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 50/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 50 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 50 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.6055[0m | [1mUAR:[0m [1;96m0.6524[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 51/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 51 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 51 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.5813[0m | [1mUAR:[0m [1;96m0.6381[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 52/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 52 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 52 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.6153[0m | [1mUAR:[0m [1;96m0.6603[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 53/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 53 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 53 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.5350[0m | [1mUAR:[0m [1;96m0.5952[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 54/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 54 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 54 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.5401[0m | [1mUAR:[0m [1;96m0.6032[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 55/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 55 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9902[0m (806/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 55 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.5836[0m | [1mUAR:[0m [1;96m0.5857[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 56/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 56 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9300[0m (757/814)
   • 中间层1准确率: [96m0.9201[0m (749/814)
   • 中间层2准确率: [96m0.9201[0m (749/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 56 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.5429[0m | [1mUAR:[0m [1;96m0.6349[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 57/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 57 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9644[0m (785/814)
   • 中间层1准确率: [96m0.9398[0m (765/814)
   • 中间层2准确率: [96m0.9644[0m (785/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 57 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.6042[0m | [1mUAR:[0m [1;96m0.6873[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 58/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 58 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9570[0m (779/814)
   • 中间层1准确率: [96m0.9496[0m (773/814)
   • 中间层2准确率: [96m0.9607[0m (782/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 58 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.6603[0m | [1mUAR:[0m [1;96m0.7175[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 59/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 59 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9509[0m (774/814)
   • 中间层1准确率: [96m0.9324[0m (759/814)
   • 中间层2准确率: [96m0.9472[0m (771/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 59 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.6374[0m | [1mUAR:[0m [1;96m0.6825[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 60/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 60 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9558[0m (778/814)
   • 中间层1准确率: [96m0.9619[0m (783/814)
   • 中间层2准确率: [96m0.9668[0m (787/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 60 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.6838[0m | [1mUAR:[0m [1;96m0.7016[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 61/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 61 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9889[0m (805/814)
   • 中间层1准确率: [96m0.9803[0m (798/814)
   • 中间层2准确率: [96m0.9914[0m (807/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 61 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.5283[0m | [1mUAR:[0m [1;96m0.6032[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 62/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 62 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 62 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.4458[0m | [1mUAR:[0m [1;96m0.5365[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 63/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 63 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9754[0m (794/814)
   • 中间层1准确率: [96m0.9803[0m (798/814)
   • 中间层2准确率: [96m0.9779[0m (796/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 63 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.6062[0m | [1mUAR:[0m [1;96m0.6159[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 64/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 64 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9816[0m (799/814)
   • 中间层1准确率: [96m0.9902[0m (806/814)
   • 中间层2准确率: [96m0.9816[0m (799/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 64 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.5125[0m | [1mUAR:[0m [1;96m0.6460[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 65/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 65 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 65 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.3770[0m | [1mUAR:[0m [1;96m0.5794[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 66/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 66 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 66 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.5896[0m | [1mUAR:[0m [1;96m0.6460[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 67/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 67 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 67 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.5462[0m | [1mUAR:[0m [1;96m0.6317[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 68/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 68 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 68 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.5462[0m | [1mUAR:[0m [1;96m0.6317[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 69/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 69 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 69 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;92m0.7059[0m | [1mUAR:[0m [1;96m0.7206[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================

[1;93m⚠️ ❌ 准确率提升(0.7059)但UAR下降(0.7206↓)，不保存[0m

[1m[1;96m📅 Epoch 70/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 70 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9582[0m (780/814)
   • 中间层1准确率: [96m0.9398[0m (765/814)
   • 中间层2准确率: [96m0.9558[0m (778/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 70 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.4060[0m | [1mUAR:[0m [1;96m0.4825[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 71/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 71 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9558[0m (778/814)
   • 中间层1准确率: [96m0.9644[0m (785/814)
   • 中间层2准确率: [96m0.9496[0m (773/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 71 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.6982[0m | [1mUAR:[0m [1;96m0.7635[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================

[1;93m⚠️ ❌ 准确率提升(0.6765)但UF1(0.6982↓)和UAR(0.7635↓)都下降，不保存[0m

[1m[1;96m📅 Epoch 72/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 72 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9656[0m (786/814)
   • 中间层1准确率: [96m0.9803[0m (798/814)
   • 中间层2准确率: [96m0.9631[0m (784/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 72 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.5369[0m | [1mUAR:[0m [1;96m0.7143[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 73/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 73 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9889[0m (805/814)
   • 中间层2准确率: [96m0.9828[0m (800/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 73 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.5644[0m | [1mUAR:[0m [1;96m0.6778[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 74/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 74 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9865[0m (803/814)
   • 中间层1准确率: [96m0.9865[0m (803/814)
   • 中间层2准确率: [96m0.9853[0m (802/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 74 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.6181[0m | [1mUAR:[0m [1;96m0.6968[0m
🥇 [1m最佳UF1:[0m [1;93m0.7011[0m | [1m最佳UAR:[0m [1;93m0.7889[0m
============================================================


[1m[1;96m📅 Epoch 75/770[0m
[1;96m----------------[0m
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签: [4, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
TensorBoard日志目录已存在: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m🔥 启用三模态特征融合:[0m
  [1;96m多头注意力融合[0m 🧠
  [96m- 建立跨模态注意力关系[0m 🔗
  [96m- 动态关注重要特征[0m 🎯
  [1;96m- 注意力头数: 8[0m 🎯
[93m⏰ 使用固定训练轮数[0m

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

[1m[1;92m🚀 GPU数据预加载[0m
[1;92m------------[0m
[1;96m📤 正在将所有数据预加载到GPU内存...[0m
[1;96m📊 训练数据大小: 0.27 GB[0m
[1;96m📊 测试数据大小: 0.06 GB[0m
[1;96m📊 总数据大小: 0.32 GB[0m
[1;96m⬆️ 正在上传训练数据到GPU...[0m
[1;96m⬆️ 正在上传测试数据到GPU...[0m
[1;92m✅ 数据预加载到GPU完成！[0m
[1;96m💾 GPU内存使用: GPU0: 0.3GB/79.3GB (0.4%)[0m
[1;96m⚡ GPU预加载模式：数据传输将极大加速！[0m

[1m[1;96m📊 损失输出配置[0m
[1;96m----------[0m
[1;96m🔍 详细损失信息: 禁用[0m
[1;96m📈 Epoch损失统计: 禁用[0m
[1;96m📉 验证损失统计: 禁用[0m
[1;96m🚨 过拟合监控: 禁用[0m
[1;96m📦 批次损失详情: 禁用[0m
[1;96m❌ Epoch损失统计将显示: 否[0m
[1;96m❌ 验证损失统计将显示: 否[0m
[1;96m❌ 过拟合监控将显示: 否[0m

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 1 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.5246[0m (427/814)
   • 中间层1准确率: [96m0.5061[0m (412/814)
   • 中间层2准确率: [96m0.5221[0m (425/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 1 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.0000[0m
📊 [1mUF1:[0m [1;92m0.2603[0m | [1mUAR:[0m [1;92m0.3063[0m
🥇 [1m最佳UF1:[0m [1;93m0.0000[0m | [1m最佳UAR:[0m [1;93m0.0000[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.5000 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.2603 | UAR: 0.3063 | 准确率: 0.5000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth

[1m[1;96m📅 Epoch 2/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 2 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7113[0m (579/814)
   • 中间层1准确率: [96m0.6966[0m (567/814)
   • 中间层2准确率: [96m0.7088[0m (577/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 2 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.5000[0m
📊 [1mUF1:[0m [1;92m0.3093[0m | [1mUAR:[0m [1;92m0.4444[0m
🥇 [1m最佳UF1:[0m [1;93m0.2603[0m | [1m最佳UAR:[0m [1;93m0.3063[0m
============================================================

[1;92m💾 保存最佳模型，相同准确率下UF1和UAR都提升: UF1(0.3093↑) UAR(0.4444↑)[0m
[1;96m📊 UF1: 0.3093 | UAR: 0.4444 | 准确率: 0.5000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth

[1m[1;96m📅 Epoch 3/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 3 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7506[0m (611/814)
   • 中间层1准确率: [96m0.7408[0m (603/814)
   • 中间层2准确率: [96m0.7912[0m (644/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 3 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.5000[0m
📊 [1mUF1:[0m [1;96m0.2866[0m | [1mUAR:[0m [1;96m0.4286[0m
🥇 [1m最佳UF1:[0m [1;93m0.3093[0m | [1m最佳UAR:[0m [1;93m0.4444[0m
============================================================


[1m[1;96m📅 Epoch 4/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 4 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8329[0m (678/814)
   • 中间层1准确率: [96m0.8219[0m (669/814)
   • 中间层2准确率: [96m0.8366[0m (681/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 4 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.5000[0m
📊 [1mUF1:[0m [1;92m0.6587[0m | [1mUAR:[0m [1;92m0.6968[0m
🥇 [1m最佳UF1:[0m [1;93m0.3093[0m | [1m最佳UAR:[0m [1;93m0.4444[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.7059 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.6587 | UAR: 0.6968 | 准确率: 0.7059[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth

[1m[1;96m📅 Epoch 5/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 5 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7998[0m (651/814)
   • 中间层1准确率: [96m0.8120[0m (661/814)
   • 中间层2准确率: [96m0.7998[0m (651/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 5 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7059[0m
📊 [1mUF1:[0m [1;96m0.5817[0m | [1mUAR:[0m [1;96m0.6111[0m
🥇 [1m最佳UF1:[0m [1;93m0.6587[0m | [1m最佳UAR:[0m [1;93m0.6968[0m
============================================================


[1m[1;96m📅 Epoch 6/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 6 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8415[0m (685/814)
   • 中间层1准确率: [96m0.8268[0m (673/814)
   • 中间层2准确率: [96m0.8563[0m (697/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 6 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7941[0m (27/34)
🏆 [1m最佳准确率:[0m [1;93m0.7059[0m
📊 [1mUF1:[0m [1;92m0.7441[0m | [1mUAR:[0m [1;92m0.8540[0m
🥇 [1m最佳UF1:[0m [1;93m0.6587[0m | [1m最佳UAR:[0m [1;93m0.6968[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.7941 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.7441 | UAR: 0.8540 | 准确率: 0.7941[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth

[1m[1;96m📅 Epoch 7/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 7 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8882[0m (723/814)
   • 中间层1准确率: [96m0.8686[0m (707/814)
   • 中间层2准确率: [96m0.8833[0m (719/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 7 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6139[0m | [1mUAR:[0m [1;96m0.6444[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 8/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 8 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9287[0m (756/814)
   • 中间层1准确率: [96m0.8845[0m (720/814)
   • 中间层2准确率: [96m0.9263[0m (754/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 8 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4209[0m | [1mUAR:[0m [1;96m0.5810[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 9/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 9 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8784[0m (715/814)
   • 中间层1准确率: [96m0.8661[0m (705/814)
   • 中间层2准确率: [96m0.8735[0m (711/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 9 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.2377[0m | [1mUAR:[0m [1;96m0.4000[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 10/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 10 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9042[0m (736/814)
   • 中间层1准确率: [96m0.8759[0m (713/814)
   • 中间层2准确率: [96m0.8943[0m (728/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 10 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.3730[0m | [1mUAR:[0m [1;96m0.4540[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 11/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 11 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9201[0m (749/814)
   • 中间层1准确率: [96m0.9079[0m (739/814)
   • 中间层2准确率: [96m0.9079[0m (739/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 11 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5955[0m | [1mUAR:[0m [1;96m0.6587[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 12/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 12 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9423[0m (767/814)
   • 中间层1准确率: [96m0.9251[0m (753/814)
   • 中间层2准确率: [96m0.9287[0m (756/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 12 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.3440[0m | [1mUAR:[0m [1;96m0.4857[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 13/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 13 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9300[0m (757/814)
   • 中间层1准确率: [96m0.9226[0m (751/814)
   • 中间层2准确率: [96m0.9275[0m (755/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 13 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6353[0m | [1mUAR:[0m [1;96m0.7127[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 14/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 14 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9521[0m (775/814)
   • 中间层1准确率: [96m0.9300[0m (757/814)
   • 中间层2准确率: [96m0.9496[0m (773/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 14 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4032[0m | [1mUAR:[0m [1;96m0.4603[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 15/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 15 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9435[0m (768/814)
   • 中间层1准确率: [96m0.9423[0m (767/814)
   • 中间层2准确率: [96m0.9496[0m (773/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 15 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7647[0m (26/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;92m0.7787[0m | [1mUAR:[0m [1;96m0.8111[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 16/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 16 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9115[0m (742/814)
   • 中间层1准确率: [96m0.8907[0m (725/814)
   • 中间层2准确率: [96m0.9079[0m (739/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 16 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4583[0m | [1mUAR:[0m [1;96m0.6016[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 17/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 17 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9521[0m (775/814)
   • 中间层1准确率: [96m0.9349[0m (761/814)
   • 中间层2准确率: [96m0.9410[0m (766/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 17 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2647[0m (9/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.3589[0m | [1mUAR:[0m [1;96m0.5032[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 18/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 18 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9398[0m (765/814)
   • 中间层1准确率: [96m0.9152[0m (745/814)
   • 中间层2准确率: [96m0.9484[0m (772/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 18 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4689[0m | [1mUAR:[0m [1;96m0.6317[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 19/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 19 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9435[0m (768/814)
   • 中间层1准确率: [96m0.9386[0m (764/814)
   • 中间层2准确率: [96m0.9496[0m (773/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 19 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6017[0m | [1mUAR:[0m [1;96m0.6524[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 20/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 20 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9693[0m (789/814)
   • 中间层1准确率: [96m0.9681[0m (788/814)
   • 中间层2准确率: [96m0.9742[0m (793/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 20 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2647[0m (9/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4075[0m | [1mUAR:[0m [1;96m0.5175[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 21/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 21 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9754[0m (794/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 21 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5409[0m | [1mUAR:[0m [1;96m0.6333[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 22/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 22 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9509[0m (774/814)
   • 中间层1准确率: [96m0.9582[0m (780/814)
   • 中间层2准确率: [96m0.9545[0m (777/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 22 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.3847[0m | [1mUAR:[0m [1;96m0.5063[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 23/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 23 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9509[0m (774/814)
   • 中间层1准确率: [96m0.9582[0m (780/814)
   • 中间层2准确率: [96m0.9459[0m (770/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 23 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6545[0m | [1mUAR:[0m [1;96m0.7397[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 24/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 24 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9558[0m (778/814)
   • 中间层1准确率: [96m0.9373[0m (763/814)
   • 中间层2准确率: [96m0.9447[0m (769/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 24 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3235[0m (11/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4507[0m | [1mUAR:[0m [1;96m0.5381[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 25/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 25 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9754[0m (794/814)
   • 中间层1准确率: [96m0.9545[0m (777/814)
   • 中间层2准确率: [96m0.9693[0m (789/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 25 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6812[0m | [1mUAR:[0m [1;96m0.7333[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 26/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 26 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9717[0m (791/814)
   • 中间层1准确率: [96m0.9779[0m (796/814)
   • 中间层2准确率: [96m0.9717[0m (791/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 26 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4444[0m | [1mUAR:[0m [1;96m0.5730[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 27/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 27 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9742[0m (793/814)
   • 中间层1准确率: [96m0.9607[0m (782/814)
   • 中间层2准确率: [96m0.9681[0m (788/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 27 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6248[0m | [1mUAR:[0m [1;96m0.6746[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 28/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 28 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9828[0m (800/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9779[0m (796/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 28 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6596[0m | [1mUAR:[0m [1;96m0.7032[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 29/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 29 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9889[0m (805/814)
   • 中间层1准确率: [96m0.9767[0m (795/814)
   • 中间层2准确率: [96m0.9865[0m (803/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 29 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6060[0m | [1mUAR:[0m [1;96m0.6286[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 30/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 30 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9828[0m (800/814)
   • 中间层1准确率: [96m0.9840[0m (801/814)
   • 中间层2准确率: [96m0.9816[0m (799/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 30 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6511[0m | [1mUAR:[0m [1;96m0.7111[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 31/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 31 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9705[0m (790/814)
   • 中间层1准确率: [96m0.9754[0m (794/814)
   • 中间层2准确率: [96m0.9705[0m (790/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 31 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5510[0m | [1mUAR:[0m [1;96m0.6540[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 32/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 32 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 32 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5530[0m | [1mUAR:[0m [1;96m0.6175[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 33/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 33 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9914[0m (807/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 33 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.7026[0m | [1mUAR:[0m [1;96m0.7619[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 34/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 34 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9877[0m (804/814)
   • 中间层1准确率: [96m0.9889[0m (805/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 34 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6950[0m | [1mUAR:[0m [1;96m0.7714[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 35/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 35 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9828[0m (800/814)
   • 中间层1准确率: [96m0.9791[0m (797/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 35 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6573[0m | [1mUAR:[0m [1;96m0.6587[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 36/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 36 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9754[0m (794/814)
   • 中间层1准确率: [96m0.9521[0m (775/814)
   • 中间层2准确率: [96m0.9754[0m (794/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 36 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4267[0m | [1mUAR:[0m [1;96m0.6127[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 37/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 37 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9251[0m (753/814)
   • 中间层1准确率: [96m0.9066[0m (738/814)
   • 中间层2准确率: [96m0.9312[0m (758/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 37 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5283[0m | [1mUAR:[0m [1;96m0.6032[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 38/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 38 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9398[0m (765/814)
   • 中间层1准确率: [96m0.9582[0m (780/814)
   • 中间层2准确率: [96m0.9484[0m (772/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 38 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6149[0m | [1mUAR:[0m [1;96m0.6667[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 39/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 39 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9791[0m (797/814)
   • 中间层1准确率: [96m0.9693[0m (789/814)
   • 中间层2准确率: [96m0.9840[0m (801/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 39 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5983[0m | [1mUAR:[0m [1;96m0.6460[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 40/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 40 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 40 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6058[0m | [1mUAR:[0m [1;96m0.6603[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 41/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 41 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 41 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6044[0m | [1mUAR:[0m [1;96m0.6667[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 42/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 42 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9816[0m (799/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 42 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5006[0m | [1mUAR:[0m [1;96m0.6175[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 43/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 43 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9730[0m (792/814)
   • 中间层1准确率: [96m0.9779[0m (796/814)
   • 中间层2准确率: [96m0.9803[0m (798/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 43 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6534[0m | [1mUAR:[0m [1;96m0.7349[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 44/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 44 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9791[0m (797/814)
   • 中间层1准确率: [96m0.9681[0m (788/814)
   • 中间层2准确率: [96m0.9767[0m (795/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 44 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7647[0m (26/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.7380[0m | [1mUAR:[0m [1;96m0.7349[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 45/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 45 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9742[0m (793/814)
   • 中间层1准确率: [96m0.9705[0m (790/814)
   • 中间层2准确率: [96m0.9742[0m (793/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 45 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5784[0m | [1mUAR:[0m [1;96m0.6444[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 46/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 46 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9791[0m (797/814)
   • 中间层1准确率: [96m0.9681[0m (788/814)
   • 中间层2准确率: [96m0.9767[0m (795/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 46 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.3340[0m | [1mUAR:[0m [1;96m0.4254[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 47/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 47 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9607[0m (782/814)
   • 中间层1准确率: [96m0.9410[0m (766/814)
   • 中间层2准确率: [96m0.9631[0m (784/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 47 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6674[0m | [1mUAR:[0m [1;96m0.7000[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 48/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 48 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9742[0m (793/814)
   • 中间层1准确率: [96m0.9681[0m (788/814)
   • 中间层2准确率: [96m0.9779[0m (796/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 48 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5713[0m | [1mUAR:[0m [1;96m0.7333[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 49/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 49 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9865[0m (803/814)
   • 中间层1准确率: [96m0.9656[0m (786/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 49 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.7179[0m | [1mUAR:[0m [1;96m0.7683[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 50/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 50 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9902[0m (806/814)
   • 中间层2准确率: [96m0.9914[0m (807/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 50 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4805[0m | [1mUAR:[0m [1;96m0.6254[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 51/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 51 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9803[0m (798/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9816[0m (799/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 51 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6554[0m | [1mUAR:[0m [1;96m0.7175[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 52/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 52 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 52 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6341[0m | [1mUAR:[0m [1;96m0.6968[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 53/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 53 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 53 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5480[0m | [1mUAR:[0m [1;96m0.6381[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 54/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 54 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9914[0m (807/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 54 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6475[0m | [1mUAR:[0m [1;96m0.7048[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 55/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 55 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9877[0m (804/814)
   • 中间层1准确率: [96m0.9791[0m (797/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 55 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4984[0m | [1mUAR:[0m [1;96m0.5841[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 56/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 56 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9201[0m (749/814)
   • 中间层1准确率: [96m0.9349[0m (761/814)
   • 中间层2准确率: [96m0.9410[0m (766/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 56 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.2606[0m | [1mUAR:[0m [1;96m0.3571[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 57/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 57 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9803[0m (798/814)
   • 中间层1准确率: [96m0.9767[0m (795/814)
   • 中间层2准确率: [96m0.9767[0m (795/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 57 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5066[0m | [1mUAR:[0m [1;96m0.6317[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 58/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 58 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9644[0m (785/814)
   • 中间层1准确率: [96m0.9754[0m (794/814)
   • 中间层2准确率: [96m0.9644[0m (785/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 58 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.3582[0m | [1mUAR:[0m [1;96m0.4302[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 59/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 59 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9779[0m (796/814)
   • 中间层1准确率: [96m0.9693[0m (789/814)
   • 中间层2准确率: [96m0.9767[0m (795/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 59 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5361[0m | [1mUAR:[0m [1;96m0.6095[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 60/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 60 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9803[0m (798/814)
   • 中间层1准确率: [96m0.9754[0m (794/814)
   • 中间层2准确率: [96m0.9865[0m (803/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 60 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4688[0m | [1mUAR:[0m [1;96m0.6095[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 61/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 61 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 61 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5714[0m | [1mUAR:[0m [1;96m0.6968[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 62/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 62 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 62 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5234[0m | [1mUAR:[0m [1;96m0.6032[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 63/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 63 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 63 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4887[0m | [1mUAR:[0m [1;96m0.6175[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 64/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 64 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 64 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5911[0m | [1mUAR:[0m [1;96m0.6460[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 65/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 65 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 65 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5163[0m | [1mUAR:[0m [1;96m0.6540[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 66/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 66 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9840[0m (801/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 66 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4635[0m | [1mUAR:[0m [1;96m0.5333[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 67/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 67 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9717[0m (791/814)
   • 中间层1准确率: [96m0.9791[0m (797/814)
   • 中间层2准确率: [96m0.9742[0m (793/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 67 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5643[0m | [1mUAR:[0m [1;96m0.6460[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 68/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 68 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9521[0m (775/814)
   • 中间层1准确率: [96m0.9472[0m (771/814)
   • 中间层2准确率: [96m0.9607[0m (782/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 68 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4884[0m | [1mUAR:[0m [1;96m0.6048[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 69/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 69 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9779[0m (796/814)
   • 中间层1准确率: [96m0.9681[0m (788/814)
   • 中间层2准确率: [96m0.9791[0m (797/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 69 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.3582[0m | [1mUAR:[0m [1;96m0.4381[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 70/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 70 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9607[0m (782/814)
   • 中间层1准确率: [96m0.9570[0m (779/814)
   • 中间层2准确率: [96m0.9582[0m (780/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 70 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4194[0m | [1mUAR:[0m [1;96m0.4984[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 71/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 71 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9828[0m (800/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 71 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6693[0m | [1mUAR:[0m [1;96m0.6937[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 72/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 72 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9865[0m (803/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 72 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4412[0m | [1mUAR:[0m [1;96m0.5810[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 73/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 73 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9889[0m (805/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 73 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3235[0m (11/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4090[0m | [1mUAR:[0m [1;96m0.5524[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 74/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 74 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9889[0m (805/814)
   • 中间层1准确率: [96m0.9914[0m (807/814)
   • 中间层2准确率: [96m0.9865[0m (803/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 74 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5695[0m | [1mUAR:[0m [1;96m0.6381[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 75/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 75 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9828[0m (800/814)
   • 中间层1准确率: [96m0.9853[0m (802/814)
   • 中间层2准确率: [96m0.9791[0m (797/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 75 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6140[0m | [1mUAR:[0m [1;96m0.6603[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 76/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 76 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9779[0m (796/814)
   • 中间层1准确率: [96m0.9607[0m (782/814)
   • 中间层2准确率: [96m0.9779[0m (796/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 76 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2647[0m (9/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.2192[0m | [1mUAR:[0m [1;96m0.3143[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 77/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 77 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9840[0m (801/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9865[0m (803/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 77 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5600[0m | [1mUAR:[0m [1;96m0.6365[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 78/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 78 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9902[0m (806/814)
   • 中间层2准确率: [96m0.9914[0m (807/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 78 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5606[0m | [1mUAR:[0m [1;96m0.7111[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 79/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 79 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9840[0m (801/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 79 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4136[0m | [1mUAR:[0m [1;96m0.6016[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 80/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 80 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9889[0m (805/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 80 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4122[0m | [1mUAR:[0m [1;96m0.5746[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 81/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 81 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9840[0m (801/814)
   • 中间层1准确率: [96m0.9853[0m (802/814)
   • 中间层2准确率: [96m0.9865[0m (803/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 81 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2941[0m (10/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4383[0m | [1mUAR:[0m [1;96m0.5365[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 82/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 82 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9791[0m (797/814)
   • 中间层1准确率: [96m0.9828[0m (800/814)
   • 中间层2准确率: [96m0.9779[0m (796/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 82 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.3637[0m | [1mUAR:[0m [1;96m0.5794[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 83/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 83 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9779[0m (796/814)
   • 中间层2准确率: [96m0.9889[0m (805/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 83 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5705[0m | [1mUAR:[0m [1;96m0.6381[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 84/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 84 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 84 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4875[0m | [1mUAR:[0m [1;96m0.5667[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 85/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 85 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 85 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5533[0m | [1mUAR:[0m [1;96m0.6667[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 86/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 86 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 86 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4951[0m | [1mUAR:[0m [1;96m0.6317[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 87/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 87 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 87 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5603[0m | [1mUAR:[0m [1;96m0.6889[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 88/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 88 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 88 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4802[0m | [1mUAR:[0m [1;96m0.6095[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 89/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 89 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 89 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.3644[0m | [1mUAR:[0m [1;96m0.5730[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 90/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 90 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9717[0m (791/814)
   • 中间层1准确率: [96m0.9730[0m (792/814)
   • 中间层2准确率: [96m0.9656[0m (786/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 90 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.3241[0m | [1mUAR:[0m [1;96m0.3952[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 91/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 91 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9742[0m (793/814)
   • 中间层1准确率: [96m0.9754[0m (794/814)
   • 中间层2准确率: [96m0.9742[0m (793/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 91 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6810[0m | [1mUAR:[0m [1;96m0.7222[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 92/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 92 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9840[0m (801/814)
   • 中间层1准确率: [96m0.9693[0m (789/814)
   • 中间层2准确率: [96m0.9865[0m (803/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 92 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6401[0m | [1mUAR:[0m [1;96m0.6571[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 93/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 93 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 93 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4356[0m | [1mUAR:[0m [1;96m0.6254[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 94/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 94 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 94 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5397[0m | [1mUAR:[0m [1;96m0.6079[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 95/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 95 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9865[0m (803/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 95 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6490[0m | [1mUAR:[0m [1;96m0.7365[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 96/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 96 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9889[0m (805/814)
   • 中间层1准确率: [96m0.9840[0m (801/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 96 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5140[0m | [1mUAR:[0m [1;96m0.5937[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 97/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 97 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 97 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4678[0m | [1mUAR:[0m [1;96m0.5508[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 98/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 98 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 98 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5529[0m | [1mUAR:[0m [1;96m0.6365[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 99/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 99 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 99 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5527[0m | [1mUAR:[0m [1;96m0.6381[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 100/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 100 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9902[0m (806/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 100 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5117[0m | [1mUAR:[0m [1;96m0.5952[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 101/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 101 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9754[0m (794/814)
   • 中间层1准确率: [96m0.9681[0m (788/814)
   • 中间层2准确率: [96m0.9754[0m (794/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 101 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5104[0m | [1mUAR:[0m [1;96m0.6317[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 102/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 102 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9705[0m (790/814)
   • 中间层1准确率: [96m0.9754[0m (794/814)
   • 中间层2准确率: [96m0.9656[0m (786/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 102 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4780[0m | [1mUAR:[0m [1;96m0.6397[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 103/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 103 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9840[0m (801/814)
   • 中间层1准确率: [96m0.9840[0m (801/814)
   • 中间层2准确率: [96m0.9767[0m (795/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 103 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5411[0m | [1mUAR:[0m [1;96m0.6175[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 104/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 104 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9914[0m (807/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 104 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4357[0m | [1mUAR:[0m [1;96m0.6095[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 105/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 105 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 105 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2647[0m (9/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.3701[0m | [1mUAR:[0m [1;96m0.4873[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 106/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 106 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 106 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.2570[0m | [1mUAR:[0m [1;96m0.3587[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 107/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 107 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 107 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6152[0m | [1mUAR:[0m [1;96m0.6889[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 108/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 108 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 108 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5759[0m | [1mUAR:[0m [1;96m0.6460[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 109/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 109 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 109 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5553[0m | [1mUAR:[0m [1;96m0.6317[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 110/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 110 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 110 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5733[0m | [1mUAR:[0m [1;96m0.6460[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 111/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 111 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 111 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4884[0m | [1mUAR:[0m [1;96m0.5730[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 112/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 112 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 112 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5451[0m | [1mUAR:[0m [1;96m0.6302[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 113/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 113 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 113 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2059[0m (7/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.2807[0m | [1mUAR:[0m [1;96m0.4730[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 114/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 114 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 114 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3235[0m (11/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.2612[0m | [1mUAR:[0m [1;96m0.3444[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 115/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 115 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9840[0m (801/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9828[0m (800/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 115 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6192[0m | [1mUAR:[0m [1;96m0.6397[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 116/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 116 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9791[0m (797/814)
   • 中间层1准确率: [96m0.9681[0m (788/814)
   • 中间层2准确率: [96m0.9779[0m (796/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 116 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4805[0m | [1mUAR:[0m [1;96m0.5714[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 117/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 117 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9717[0m (791/814)
   • 中间层1准确率: [96m0.9705[0m (790/814)
   • 中间层2准确率: [96m0.9767[0m (795/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 117 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.3712[0m | [1mUAR:[0m [1;96m0.3857[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 118/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 118 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9754[0m (794/814)
   • 中间层1准确率: [96m0.9816[0m (799/814)
   • 中间层2准确率: [96m0.9705[0m (790/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 118 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4365[0m | [1mUAR:[0m [1;96m0.5540[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 119/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 119 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9828[0m (800/814)
   • 中间层1准确率: [96m0.9853[0m (802/814)
   • 中间层2准确率: [96m0.9853[0m (802/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 119 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4785[0m | [1mUAR:[0m [1;96m0.5667[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 120/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 120 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9816[0m (799/814)
   • 中间层1准确率: [96m0.9889[0m (805/814)
   • 中间层2准确率: [96m0.9791[0m (797/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 120 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5004[0m | [1mUAR:[0m [1;96m0.6603[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 121/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 121 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9816[0m (799/814)
   • 中间层1准确率: [96m0.9902[0m (806/814)
   • 中间层2准确率: [96m0.9816[0m (799/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 121 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6689[0m | [1mUAR:[0m [1;96m0.7317[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 122/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 122 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 122 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4736[0m | [1mUAR:[0m [1;96m0.6254[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 123/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 123 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9816[0m (799/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9791[0m (797/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 123 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2941[0m (10/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.1991[0m | [1mUAR:[0m [1;96m0.3476[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 124/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 124 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9803[0m (798/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 124 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5067[0m | [1mUAR:[0m [1;96m0.6095[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 125/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 125 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9840[0m (801/814)
   • 中间层1准确率: [96m0.9767[0m (795/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 125 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5363[0m | [1mUAR:[0m [1;96m0.5778[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 126/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 126 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9877[0m (804/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9853[0m (802/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 126 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4548[0m | [1mUAR:[0m [1;96m0.5587[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 127/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 127 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9840[0m (801/814)
   • 中间层1准确率: [96m0.9828[0m (800/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 127 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5197[0m | [1mUAR:[0m [1;96m0.6254[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 128/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 128 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 128 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4738[0m | [1mUAR:[0m [1;96m0.5587[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 129/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 129 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 129 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5359[0m | [1mUAR:[0m [1;96m0.6016[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 130/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 130 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 130 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4907[0m | [1mUAR:[0m [1;96m0.6254[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 131/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 131 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 131 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6467[0m | [1mUAR:[0m [1;96m0.6952[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 132/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 132 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 132 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6310[0m | [1mUAR:[0m [1;96m0.6746[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 133/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 133 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 133 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6055[0m | [1mUAR:[0m [1;96m0.6524[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 134/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 134 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 134 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6119[0m | [1mUAR:[0m [1;96m0.6810[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 135/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 135 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 135 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3235[0m (11/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4617[0m | [1mUAR:[0m [1;96m0.5603[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 136/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 136 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9828[0m (800/814)
   • 中间层2准确率: [96m0.9840[0m (801/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 136 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2941[0m (10/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4206[0m | [1mUAR:[0m [1;96m0.5143[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 137/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 137 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9803[0m (798/814)
   • 中间层1准确率: [96m0.9816[0m (799/814)
   • 中间层2准确率: [96m0.9791[0m (797/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 137 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4291[0m | [1mUAR:[0m [1;96m0.6460[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 138/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 138 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9840[0m (801/814)
   • 中间层2准确率: [96m0.9853[0m (802/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 138 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4101[0m | [1mUAR:[0m [1;96m0.4683[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 139/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 139 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9853[0m (802/814)
   • 中间层2准确率: [96m0.9889[0m (805/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 139 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5892[0m | [1mUAR:[0m [1;96m0.6413[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 140/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 140 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 140 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.2812[0m | [1mUAR:[0m [1;96m0.3810[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 141/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 141 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 141 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4920[0m | [1mUAR:[0m [1;96m0.5794[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 142/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 142 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 142 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3235[0m (11/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4389[0m | [1mUAR:[0m [1;96m0.5365[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 143/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 143 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9914[0m (807/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 143 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2647[0m (9/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.3625[0m | [1mUAR:[0m [1;96m0.4794[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 144/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 144 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9644[0m (785/814)
   • 中间层1准确率: [96m0.9644[0m (785/814)
   • 中间层2准确率: [96m0.9558[0m (778/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 144 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5618[0m | [1mUAR:[0m [1;96m0.6238[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 145/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 145 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9803[0m (798/814)
   • 中间层1准确率: [96m0.9816[0m (799/814)
   • 中间层2准确率: [96m0.9828[0m (800/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 145 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5732[0m | [1mUAR:[0m [1;96m0.6460[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 146/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 146 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9840[0m (801/814)
   • 中间层1准确率: [96m0.9853[0m (802/814)
   • 中间层2准确率: [96m0.9840[0m (801/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 146 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5857[0m | [1mUAR:[0m [1;96m0.7270[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 147/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 147 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9877[0m (804/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9914[0m (807/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 147 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5324[0m | [1mUAR:[0m [1;96m0.6397[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 148/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 148 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9865[0m (803/814)
   • 中间层1准确率: [96m0.9865[0m (803/814)
   • 中间层2准确率: [96m0.9816[0m (799/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 148 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4764[0m | [1mUAR:[0m [1;96m0.6635[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 149/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 149 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9828[0m (800/814)
   • 中间层1准确率: [96m0.9902[0m (806/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 149 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5531[0m | [1mUAR:[0m [1;96m0.6317[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 150/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 150 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 150 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4565[0m | [1mUAR:[0m [1;96m0.5683[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 151/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 151 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 151 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5217[0m | [1mUAR:[0m [1;96m0.6238[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 152/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 152 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 152 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5538[0m | [1mUAR:[0m [1;96m0.6778[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 153/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 153 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 153 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2647[0m (9/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.3466[0m | [1mUAR:[0m [1;96m0.4714[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 154/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 154 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 154 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4702[0m | [1mUAR:[0m [1;96m0.5730[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 155/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 155 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 155 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4570[0m | [1mUAR:[0m [1;96m0.5587[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 156/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 156 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 156 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5313[0m | [1mUAR:[0m [1;96m0.6381[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 157/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 157 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 157 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4352[0m | [1mUAR:[0m [1;96m0.5460[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 158/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 158 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 158 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4937[0m | [1mUAR:[0m [1;96m0.5952[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 159/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 159 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9767[0m (795/814)
   • 中间层1准确率: [96m0.9816[0m (799/814)
   • 中间层2准确率: [96m0.9767[0m (795/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 159 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5603[0m | [1mUAR:[0m [1;96m0.6302[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 160/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 160 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9877[0m (804/814)
   • 中间层1准确率: [96m0.9865[0m (803/814)
   • 中间层2准确率: [96m0.9853[0m (802/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 160 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4292[0m | [1mUAR:[0m [1;96m0.6016[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 161/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 161 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9754[0m (794/814)
   • 中间层1准确率: [96m0.9754[0m (794/814)
   • 中间层2准确率: [96m0.9730[0m (792/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 161 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5584[0m | [1mUAR:[0m [1;96m0.6365[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 162/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 162 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9779[0m (796/814)
   • 中间层1准确率: [96m0.9853[0m (802/814)
   • 中间层2准确率: [96m0.9767[0m (795/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 162 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5358[0m | [1mUAR:[0m [1;96m0.6222[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 163/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 163 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 163 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.4102[0m | [1mUAR:[0m [1;96m0.5651[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 164/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 164 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 164 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5437[0m | [1mUAR:[0m [1;96m0.6683[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 165/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 165 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9865[0m (803/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 165 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.6885[0m | [1mUAR:[0m [1;96m0.7603[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 166/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 166 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9767[0m (795/814)
   • 中间层1准确率: [96m0.9889[0m (805/814)
   • 中间层2准确率: [96m0.9828[0m (800/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 166 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5042[0m | [1mUAR:[0m [1;96m0.5873[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 167/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 167 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 167 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
📊 [1mUF1:[0m [1;96m0.5012[0m | [1mUAR:[0m [1;96m0.6000[0m
🥇 [1m最佳UF1:[0m [1;93m0.7441[0m | [1m最佳UAR:[0m [1;93m0.8540[0m
============================================================


[1m[1;96m📅 Epoch 168/770[0m
[1;96m-----------------[0m
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

[1;92m============[0m
[1m[1;92m🚀 启动并行训练模式 🚀[0m
[1;92m============[0m

[1;96m📊 有效受试者数量: 25[0m
[1;96m📋 受试者列表: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub10', 'sub23', 'sub12', 'sub20', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub04', 'sub06', 'sub13', 'sub08', 'sub15', 'sub16', 'sub18', 'sub21', 'sub22'][0m

[1;92m============[0m
[1m[1;92m🚀 开始并行训练模式 🚀[0m
[1;92m============[0m

[1;96m🔢 并行数量: 4[0m
[1;96m📋 训练顺序: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub10', 'sub23', 'sub12', 'sub20', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub04', 'sub06', 'sub13', 'sub08', 'sub15', 'sub16', 'sub18', 'sub21', 'sub22'][0m

[1m[1;96m📦 批次 1/7[0m
[1;96m----------[0m

[1m[1;92m🚀 并行训练批次[0m
[1;92m----------[0m
[1;96m🎯 开始并行训练 4 个受试者: ['sub17', 'sub05', 'sub26', 'sub19'][0m
[1;96m📊 资源监控已启动[0m
[1;96m🎯 开始训练受试者: sub17[0m[1;96m📤 已提交训练任务: sub17[0m

[1;96m🎯 开始训练受试者: sub17[0m
[1;96m📊 受试者组别: 大样本组(>12个样本)[0m[1;96m🎯 开始训练受试者: sub05[0m[1;96m📤 已提交训练任务: sub05[0m


[1;96m📁 正在加载训练数据...[0m[1;96m🎯 开始训练受试者: sub05[0m
[1;96m🎯 开始训练受试者: sub26[0m

[1;96m📤 已提交训练任务: sub26[0m[1;96m🎯 开始训练受试者: sub26[0m[1;96m📊 受试者组别: 大样本组(>12个样本)[0m


[1;96m📊 受试者组别: 大样本组(>12个样本)[0m[1;96m📁 正在加载训练数据...[0m
[1;96m🎯 开始训练受试者: sub19[0m[1;96m📤 已提交训练任务: sub19[0m

[1;96m📁 正在加载训练数据...[0m
[1;96m🎯 开始训练受试者: sub19[0m

[1;96m📊 受试者组别: 大样本组(>12个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 814, 测试样本数: 170[0m
[1;91m💥 ❌ sub17 训练失败[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 935, 测试样本数: 49[0m
[1;91m💥 ❌ sub26 训练失败[0m
[1;96m📊 训练样本数: 909, 测试样本数: 75[0m
[1;91m💥 ❌ sub19 训练失败[0m
[1;96m📊 训练样本数: 911, 测试样本数: 73[0m
[1;91m💥 ❌ sub05 训练失败[0m
[1;96m📊 资源监控已停止[0m
[1;96m⏱️ 批次训练完成，耗时: 9.4秒[0m
[1;96m📊 成功: 0, 失败: 4[0m
[1;96m😴 批次间休息5秒...[0m

[1m[1;96m📦 批次 2/7[0m
[1;96m----------[0m

[1m[1;92m🚀 并行训练批次[0m
[1;92m----------[0m
[1;96m🎯 开始并行训练 4 个受试者: ['sub09', 'sub02', 'sub10', 'sub23'][0m
[1;96m📊 资源监控已启动[0m
[1;96m🎯 开始训练受试者: sub09[0m[1;96m📤 已提交训练任务: sub09[0m

[1;96m🎯 开始训练受试者: sub09[0m[1;96m🎯 开始训练受试者: sub02[0m
[1;96m📤 已提交训练任务: sub02[0m
[1;96m📊 受试者组别: 大样本组(>12个样本)[0m
[1;96m🎯 开始训练受试者: sub02[0m
[1;96m🎯 开始训练受试者: sub10[0m
[1;96m📁 正在加载训练数据...[0m[1;96m📤 已提交训练任务: sub10[0m

[1;96m📊 受试者组别: 大样本组(>12个样本)[0m[1;96m🎯 开始训练受试者: sub10[0m


[1;96m📁 正在加载训练数据...[0m[1;96m🎯 开始训练受试者: sub23[0m[1;96m📤 已提交训练任务: sub23[0m
[1;96m📊 受试者组别: 大样本组(>12个样本)[0m


[1;96m🎯 开始训练受试者: sub23[0m[1;96m📁 正在加载训练数据...[0m

[1;96m📊 受试者组别: 中等样本组(7-12个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 958, 测试样本数: 26[0m
[1;91m💥 ❌ sub10 训练失败[0m
[1;96m📊 训练样本数: 908, 测试样本数: 76[0m
[1;91m💥 ❌ sub09 训练失败[0m
[1;96m📊 训练样本数: 906, 测试样本数: 78[0m
[1;91m💥 ❌ sub02 训练失败[0m
[1;96m📊 训练样本数: 934, 测试样本数: 50[0m
[1;91m💥 ❌ sub23 训练失败[0m
[1;96m📊 资源监控已停止[0m
[1;96m⏱️ 批次训练完成，耗时: 9.4秒[0m
[1;96m📊 成功: 0, 失败: 4[0m
[1;96m😴 批次间休息5秒...[0m

[1m[1;96m📦 批次 3/7[0m
[1;96m----------[0m

[1m[1;92m🚀 并行训练批次[0m
[1;92m----------[0m
[1;96m🎯 开始并行训练 4 个受试者: ['sub12', 'sub20', 'sub11', 'sub01'][0m
[1;96m📊 资源监控已启动[0m
[1;96m🎯 开始训练受试者: sub12[0m[1;96m📤 已提交训练任务: sub12[0m
[1;96m🎯 开始训练受试者: sub12[0m

[1;96m📊 受试者组别: 中等样本组(7-12个样本)[0m[1;96m🎯 开始训练受试者: sub20[0m[1;96m📤 已提交训练任务: sub20[0m

[1;96m📁 正在加载训练数据...[0m
[1;96m🎯 开始训练受试者: sub20[0m

[1;96m🎯 开始训练受试者: sub11[0m[1;96m📤 已提交训练任务: sub11[0m[1;96m📊 受试者组别: 中等样本组(7-12个样本)[0m


[1;96m📁 正在加载训练数据...[0m[1;96m🎯 开始训练受试者: sub01[0m[1;96m📤 已提交训练任务: sub01[0m[1;96m🎯 开始训练受试者: sub11[0m



[1;96m🎯 开始训练受试者: sub01[0m[1;96m📊 受试者组别: 中等样本组(7-12个样本)[0m

[1;96m📊 受试者组别: 中等样本组(7-12个样本)[0m[1;96m📁 正在加载训练数据...[0m

[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 960, 测试样本数: 24[0m
[1;91m💥 ❌ sub11 训练失败[0m
[1;96m📊 训练样本数: 960, 测试样本数: 24[0m
[1;91m💥 ❌ sub20 训练失败[0m
[1;96m📊 训练样本数: 960, 测试样本数: 24[0m
[1;91m💥 ❌ sub01 训练失败[0m
[1;96m📊 训练样本数: 923, 测试样本数: 61[0m
[1;91m💥 ❌ sub12 训练失败[0m
[1;96m📊 资源监控已停止[0m
[1;96m⏱️ 批次训练完成，耗时: 9.6秒[0m
[1;96m📊 成功: 0, 失败: 4[0m
[1;96m😴 批次间休息5秒...[0m

[1m[1;96m📦 批次 4/7[0m
[1;96m----------[0m

[1m[1;92m🚀 并行训练批次[0m
[1;92m----------[0m
[1;96m🎯 开始并行训练 4 个受试者: ['sub07', 'sub24', 'sub25', 'sub03'][0m
[1;96m📊 资源监控已启动[0m
[1;96m🎯 开始训练受试者: sub07[0m[1;96m📤 已提交训练任务: sub07[0m

[1;96m🎯 开始训练受试者: sub07[0m
[1;96m🎯 开始训练受试者: sub24[0m[1;96m📤 已提交训练任务: sub24[0m[1;96m📊 受试者组别: 中等样本组(7-12个样本)[0m

[1;96m🎯 开始训练受试者: sub24[0m
[1;96m🎯 开始训练受试者: sub25[0m[1;96m📤 已提交训练任务: sub25[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📊 受试者组别: 小样本组(3-6个样本)[0m
[1;96m🎯 开始训练受试者: sub03[0m

[1;96m📤 已提交训练任务: sub03[0m[1;96m🎯 开始训练受试者: sub25[0m

[1;96m📁 正在加载训练数据...[0m
[1;96m🎯 开始训练受试者: sub03[0m

[1;96m📊 受试者组别: 小样本组(3-6个样本)[0m[1;96m📊 受试者组别: 小样本组(3-6个样本)[0m

[1;96m📁 正在加载训练数据...[0m[1;96m📁 正在加载训练数据...[0m

[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 955, 测试样本数: 29[0m
[1;91m💥 ❌ sub25 训练失败[0m
[1;96m📊 训练样本数: 960, 测试样本数: 24[0m
[1;91m💥 ❌ sub03 训练失败[0m
[1;96m📊 训练样本数: 962, 测试样本数: 22[0m
[1;91m💥 ❌ sub24 训练失败[0m
[1;96m📊 训练样本数: 961, 测试样本数: 23[0m
[1;91m💥 ❌ sub07 训练失败[0m
[1;96m📊 资源监控已停止[0m
[1;96m⏱️ 批次训练完成，耗时: 10.5秒[0m
[1;96m📊 成功: 0, 失败: 4[0m
[1;96m😴 批次间休息5秒...[0m

[1m[1;96m📦 批次 5/7[0m
[1;96m----------[0m

[1m[1;92m🚀 并行训练批次[0m
[1;92m----------[0m
[1;96m🎯 开始并行训练 4 个受试者: ['sub04', 'sub06', 'sub13', 'sub08'][0m
[1;96m📊 资源监控已启动[0m
[1;96m🎯 开始训练受试者: sub04[0m[1;96m📤 已提交训练任务: sub04[0m

[1;96m🎯 开始训练受试者: sub04[0m[1;96m🎯 开始训练受试者: sub06[0m
[1;96m📤 已提交训练任务: sub06[0m
[1;96m📊 受试者组别: 小样本组(3-6个样本)[0m
[1;96m🎯 开始训练受试者: sub06[0m
[1;96m📤 已提交训练任务: sub13[0m[1;96m🎯 开始训练受试者: sub13[0m
[1;96m📁 正在加载训练数据...[0m


[1;96m🎯 开始训练受试者: sub08[0m[1;96m📤 已提交训练任务: sub08[0m[1;96m🎯 开始训练受试者: sub13[0m
[1;96m📊 受试者组别: 小样本组(3-6个样本)[0m

[1;96m🎯 开始训练受试者: sub08[0m
[1;96m📊 受试者组别: 小样本组(3-6个样本)[0m[1;96m📁 正在加载训练数据...[0m


[1;96m📁 正在加载训练数据...[0m[1;96m📊 受试者组别: 极小样本组(<=3个样本)[0m

[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 974, 测试样本数: 10[0m
[1;91m💥 ❌ sub04 训练失败[0m
[1;96m📊 训练样本数: 960, 测试样本数: 24[0m
[1;91m💥 ❌ sub13 训练失败[0m
[1;96m📊 训练样本数: 972, 测试样本数: 12[0m
[1;91m💥 ❌ sub08 训练失败[0m
[1;96m📊 训练样本数: 957, 测试样本数: 27[0m
[1;91m💥 ❌ sub06 训练失败[0m
[1;96m📊 资源监控已停止[0m
[1;96m⏱️ 批次训练完成，耗时: 9.7秒[0m
[1;96m📊 成功: 0, 失败: 4[0m
[1;96m😴 批次间休息5秒...[0m

[1m[1;96m📦 批次 6/7[0m
[1;96m----------[0m

[1m[1;92m🚀 并行训练批次[0m
[1;92m----------[0m
[1;96m🎯 开始并行训练 4 个受试者: ['sub15', 'sub16', 'sub18', 'sub21'][0m
[1;96m📊 资源监控已启动[0m
[1;96m🎯 开始训练受试者: sub15[0m[1;96m📤 已提交训练任务: sub15[0m

[1;96m🎯 开始训练受试者: sub15[0m[1;96m🎯 开始训练受试者: sub16[0m[1;96m📤 已提交训练任务: sub16[0m


[1;96m📊 受试者组别: 极小样本组(<=3个样本)[0m[1;96m📤 已提交训练任务: sub18[0m[1;96m🎯 开始训练受试者: sub16[0m
[1;96m🎯 开始训练受试者: sub18[0m


[1;96m🎯 开始训练受试者: sub21[0m[1;96m📊 受试者组别: 极小样本组(<=3个样本)[0m[1;96m📁 正在加载训练数据...[0m[1;96m📤 已提交训练任务: sub21[0m[1;96m🎯 开始训练受试者: sub18[0m



[1;96m📁 正在加载训练数据...[0m[1;96m📊 受试者组别: 极小样本组(<=3个样本)[0m


[1;96m🎯 开始训练受试者: sub21[0m[1;96m📁 正在加载训练数据...[0m

[1;96m📊 受试者组别: 极小样本组(<=3个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 967, 测试样本数: 17[0m
[1;91m💥 ❌ sub15 训练失败[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 974, 测试样本数: 10[0m
[1;91m💥 ❌ sub21 训练失败[0m
[1;96m📊 训练样本数: 970, 测试样本数: 14[0m
[1;91m💥 ❌ sub16 训练失败[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 978, 测试样本数: 6[0m
[1;91m💥 ❌ sub18 训练失败[0m
[1;96m📊 资源监控已停止[0m
[1;96m⏱️ 批次训练完成，耗时: 9.7秒[0m
[1;96m📊 成功: 0, 失败: 4[0m
[1;96m😴 批次间休息5秒...[0m

[1m[1;96m📦 批次 7/7[0m
[1;96m----------[0m

[1m[1;92m🚀 并行训练批次[0m
[1;92m----------[0m
[1;96m🎯 开始并行训练 1 个受试者: ['sub22'][0m
[1;96m📊 资源监控已启动[0m
[1;96m🎯 开始训练受试者: sub22[0m[1;96m📤 已提交训练任务: sub22[0m

[1;96m🎯 开始训练受试者: sub22[0m
[1;96m📊 受试者组别: 极小样本组(<=3个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 968, 测试样本数: 16[0m
[1;91m💥 ❌ sub22 训练失败[0m
[1;96m📊 资源监控已停止[0m
[1;96m⏱️ 批次训练完成，耗时: 7.9秒[0m
[1;96m📊 成功: 0, 失败: 1[0m

[1m[1;93m🔄 重试失败的受试者[0m
[1;93m------------[0m
[1;96m🔄 重试训练受试者: sub17[0m
[1;96m🎯 开始训练受试者: sub17[0m
[1;96m🎯 开始训练受试者: sub17[0m
[1;96m📊 受试者组别: 大样本组(>12个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 814, 测试样本数: 170[0m
[1;91m💥 ❌ sub17 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub26[0m
[1;96m🎯 开始训练受试者: sub26[0m
[1;96m🎯 开始训练受试者: sub26[0m
[1;96m📊 受试者组别: 大样本组(>12个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 935, 测试样本数: 49[0m
[1;91m💥 ❌ sub26 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub19[0m
[1;96m🎯 开始训练受试者: sub19[0m
[1;96m🎯 开始训练受试者: sub19[0m
[1;96m📊 受试者组别: 大样本组(>12个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 909, 测试样本数: 75[0m
[1;91m💥 ❌ sub19 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub05[0m
[1;96m🎯 开始训练受试者: sub05[0m
[1;96m🎯 开始训练受试者: sub05[0m
[1;96m📊 受试者组别: 大样本组(>12个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 911, 测试样本数: 73[0m
[1;91m💥 ❌ sub05 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub10[0m
[1;96m🎯 开始训练受试者: sub10[0m
[1;96m🎯 开始训练受试者: sub10[0m
[1;96m📊 受试者组别: 大样本组(>12个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 958, 测试样本数: 26[0m
[1;91m💥 ❌ sub10 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub09[0m
[1;96m🎯 开始训练受试者: sub09[0m
[1;96m🎯 开始训练受试者: sub09[0m
[1;96m📊 受试者组别: 大样本组(>12个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 908, 测试样本数: 76[0m
[1;91m💥 ❌ sub09 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub02[0m
[1;96m🎯 开始训练受试者: sub02[0m
[1;96m🎯 开始训练受试者: sub02[0m
[1;96m📊 受试者组别: 大样本组(>12个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 906, 测试样本数: 78[0m
[1;91m💥 ❌ sub02 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub23[0m
[1;96m🎯 开始训练受试者: sub23[0m
[1;96m🎯 开始训练受试者: sub23[0m
[1;96m📊 受试者组别: 中等样本组(7-12个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 934, 测试样本数: 50[0m
[1;91m💥 ❌ sub23 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub11[0m
[1;96m🎯 开始训练受试者: sub11[0m
[1;96m🎯 开始训练受试者: sub11[0m
[1;96m📊 受试者组别: 中等样本组(7-12个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 960, 测试样本数: 24[0m
[1;91m💥 ❌ sub11 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub20[0m
[1;96m🎯 开始训练受试者: sub20[0m
[1;96m🎯 开始训练受试者: sub20[0m
[1;96m📊 受试者组别: 中等样本组(7-12个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 960, 测试样本数: 24[0m
[1;91m💥 ❌ sub20 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub01[0m
[1;96m🎯 开始训练受试者: sub01[0m
[1;96m🎯 开始训练受试者: sub01[0m
[1;96m📊 受试者组别: 中等样本组(7-12个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 960, 测试样本数: 24[0m
[1;91m💥 ❌ sub01 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub12[0m
[1;96m🎯 开始训练受试者: sub12[0m
[1;96m🎯 开始训练受试者: sub12[0m
[1;96m📊 受试者组别: 中等样本组(7-12个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 923, 测试样本数: 61[0m
[1;91m💥 ❌ sub12 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub25[0m
[1;96m🎯 开始训练受试者: sub25[0m
[1;96m🎯 开始训练受试者: sub25[0m
[1;96m📊 受试者组别: 小样本组(3-6个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 955, 测试样本数: 29[0m
[1;91m💥 ❌ sub25 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub03[0m
[1;96m🎯 开始训练受试者: sub03[0m
[1;96m🎯 开始训练受试者: sub03[0m
[1;96m📊 受试者组别: 小样本组(3-6个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 960, 测试样本数: 24[0m
[1;91m💥 ❌ sub03 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub24[0m
[1;96m🎯 开始训练受试者: sub24[0m
[1;96m🎯 开始训练受试者: sub24[0m
[1;96m📊 受试者组别: 小样本组(3-6个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 962, 测试样本数: 22[0m
[1;91m💥 ❌ sub24 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub07[0m
[1;96m🎯 开始训练受试者: sub07[0m
[1;96m🎯 开始训练受试者: sub07[0m
[1;96m📊 受试者组别: 中等样本组(7-12个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 961, 测试样本数: 23[0m
[1;91m💥 ❌ sub07 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub04[0m
[1;96m🎯 开始训练受试者: sub04[0m
[1;96m🎯 开始训练受试者: sub04[0m
[1;96m📊 受试者组别: 小样本组(3-6个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 974, 测试样本数: 10[0m
[1;91m💥 ❌ sub04 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub13[0m
[1;96m🎯 开始训练受试者: sub13[0m
[1;96m🎯 开始训练受试者: sub13[0m
[1;96m📊 受试者组别: 小样本组(3-6个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 960, 测试样本数: 24[0m
[1;91m💥 ❌ sub13 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub08[0m
[1;96m🎯 开始训练受试者: sub08[0m
[1;96m🎯 开始训练受试者: sub08[0m
[1;96m📊 受试者组别: 极小样本组(<=3个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 972, 测试样本数: 12[0m
[1;91m💥 ❌ sub08 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub06[0m
[1;96m🎯 开始训练受试者: sub06[0m
[1;96m🎯 开始训练受试者: sub06[0m
[1;96m📊 受试者组别: 小样本组(3-6个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 957, 测试样本数: 27[0m
[1;91m💥 ❌ sub06 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub15[0m
[1;96m🎯 开始训练受试者: sub15[0m
[1;96m🎯 开始训练受试者: sub15[0m
[1;96m📊 受试者组别: 极小样本组(<=3个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 967, 测试样本数: 17[0m
[1;91m💥 ❌ sub15 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub21[0m
[1;96m🎯 开始训练受试者: sub21[0m
[1;96m🎯 开始训练受试者: sub21[0m
[1;96m📊 受试者组别: 极小样本组(<=3个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 974, 测试样本数: 10[0m
[1;91m💥 ❌ sub21 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub16[0m
[1;96m🎯 开始训练受试者: sub16[0m
[1;96m🎯 开始训练受试者: sub16[0m
[1;96m📊 受试者组别: 极小样本组(<=3个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 970, 测试样本数: 14[0m
[1;91m💥 ❌ sub16 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub18[0m
[1;96m🎯 开始训练受试者: sub18[0m
[1;96m🎯 开始训练受试者: sub18[0m
[1;96m📊 受试者组别: 极小样本组(<=3个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 978, 测试样本数: 6[0m
[1;91m💥 ❌ sub18 重试仍然失败[0m
[1;96m🔄 重试训练受试者: sub22[0m
[1;96m🎯 开始训练受试者: sub22[0m
[1;96m🎯 开始训练受试者: sub22[0m
[1;96m📊 受试者组别: 极小样本组(<=3个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 968, 测试样本数: 16[0m
[1;91m💥 ❌ sub22 重试仍然失败[0m

[1;92m==========[0m
[1m[1;92m🎉 并行训练完成 🎉[0m
[1;92m==========[0m

[1;96m⏱️ 总耗时: 135.8秒[0m
[1;96m✅ 成功训练: 0个受试者[0m
[1;96m❌ 失败: 25个受试者[0m
[1;93m⚠️ 失败的受试者: ['sub17', 'sub26', 'sub19', 'sub05', 'sub10', 'sub09', 'sub02', 'sub23', 'sub11', 'sub20', 'sub01', 'sub12', 'sub25', 'sub03', 'sub24', 'sub07', 'sub04', 'sub13', 'sub08', 'sub06', 'sub15', 'sub21', 'sub16', 'sub18', 'sub22'][0m

[1;92m==========[0m
[1m[1;92m🎉 并行训练完成 🎉[0m
[1;92m==========[0m


================================================================================
【实验结果报告】
================================================================================

【基本信息】
实验名称: SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_attn
时间: 2025-07-29 16:11:35
总训练时间: 00:02:15
数据集: CASME2_LOSO_full

【系统环境】
操作系统: 💻 Linux 6.8.0-1031-nvidia (x86_64)
处理器: ⚡ 32核心/32线程 @ 2195MHz
内存: 🧠 62.7GB RAM (已用: 24.5GB, 39.1%)
GPU: 🚀 NVIDIA A800 80GB PCIe (79.3GB)

【模型配置】
模型架构: SKD_TSTSAN
是否使用预训练: 是
预训练模型: ck注意力融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_attn_h8.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
批次大小: 32
最大迭代次数: 20000
损失函数: FocalLoss_weighted
随机种子: 2577

【GPU性能配置】
CUDNN Benchmark: 禁用
CUDNN Deterministic: 禁用
混合精度训练: 禁用
TF32加速: 启用
BatchNorm修复: 禁用
卷积类型: 小波变换卷积

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练模式】
训练模式: 🚀 并行训练
并行数量: 4个受试者
GPU分配策略: auto
内存限制: 80%
超时时间: 7200秒
失败重试: 启用
资源监控: 启用

【训练结果】
总体性能:
- UF1分数: N/A
- UAR分数: N/A

【各表情类别准确率】
--------------------------------------------------

【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
    平均        nan        nan        nan    


【各受试者详细预测结果】
==================================================

================================================================================

【错误】邮件处理过程出错: '>=' not supported between instances of 'NoneType' and 'float'
详细错误信息: Traceback (most recent call last):
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSANCASME2.py", line 766, in send_training_results
    performance_recommendations = generate_performance_recommendations(results_dict, config, total_time)
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSANCASME2.py", line 507, in generate_performance_recommendations
    if overall_uf1 >= 0.8 and overall_uar >= 0.8:
TypeError: '>=' not supported between instances of 'NoneType' and 'float'


训练完成!

正在关闭日志系统...
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

[1;92m============[0m
[1m[1;92m🚀 启动并行训练模式 🚀[0m
[1;92m============[0m

[1;96m📊 有效受试者数量: 25[0m
[1;96m📋 受试者列表: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub10', 'sub23', 'sub12', 'sub20', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub04', 'sub06', 'sub13', 'sub08', 'sub15', 'sub16', 'sub18', 'sub21', 'sub22'][0m

[1;92m============[0m
[1m[1;92m🚀 开始并行训练模式 🚀[0m
[1;92m============[0m

[1;96m🔢 并行数量: 3[0m
[1;96m📋 训练顺序: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub10', 'sub23', 'sub12', 'sub20', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub04', 'sub06', 'sub13', 'sub08', 'sub15', 'sub16', 'sub18', 'sub21', 'sub22'][0m

[1m[1;96m📦 批次 1/9[0m
[1;96m----------[0m

[1m[1;92m🚀 并行训练批次[0m
[1;92m----------[0m
[1;96m🎯 开始并行训练 3 个受试者: ['sub17', 'sub05', 'sub26'][0m
[1;96m📊 资源监控已启动[0m
[1;96m🎯 开始训练受试者: sub17[0m
[1;96m🎯 开始训练受试者: sub17[0m
[1;96m📊 受试者组别: 大样本组(>12个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📤 已提交训练任务: sub17[0m
[1;96m🎯 开始训练受试者: sub05[0m[1;96m📤 已提交训练任务: sub05[0m

[1;96m🎯 开始训练受试者: sub05[0m
[1;96m📊 受试者组别: 大样本组(>12个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m🎯 开始训练受试者: sub26[0m[1;96m📤 已提交训练任务: sub26[0m

[1;96m🎯 开始训练受试者: sub26[0m
[1;96m📊 受试者组别: 大样本组(>12个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 814, 测试样本数: 170[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;96m📁 正在加载测试数据...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[1;96m📊 训练样本数: 935, 测试样本数: 49[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
[1;96m📊 训练样本数: 911, 测试样本数: 73[0m
🌊 小波配置: 类型=bior2.2, 层数=2
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8=> 使用折叠除法: 8

🔥 启用三模态特征融合: attention
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 三模态融合模块初始化完成
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[1;96m🚀 开始训练,总共770个epoch...[0m
[1;96m🚀 开始训练,总共667个epoch...[0m[1;96m🚀 开始训练,总共690个epoch...[0m

[1;91m❌ 受试者 sub17 训练异常: name 'get_num_correct' is not defined[0m[1;91m❌ 受试者 sub05 训练异常: name 'get_num_correct' is not defined[0m

[1;91m❌ 受试者 sub26 训练异常: name 'get_num_correct' is not defined[0m
[1;91m💥 ❌ sub26 训练失败[0m
[1;91m💥 ❌ sub17 训练失败[0m
[1;91m💥 ❌ sub05 训练失败[0m
[1;96m📊 资源监控已停止[0m
[1;96m⏱️ 批次训练完成，耗时: 53.5秒[0m
[1;96m📊 成功: 0, 失败: 3[0m
[1;96m😴 批次间休息5秒...[0m

[1m[1;96m📦 批次 2/9[0m
[1;96m----------[0m

[1m[1;92m🚀 并行训练批次[0m
[1;92m----------[0m
[1;96m🎯 开始并行训练 3 个受试者: ['sub19', 'sub09', 'sub02'][0m
[1;96m📊 资源监控已启动[0m
[1;96m🎯 开始训练受试者: sub19[0m[1;96m📤 已提交训练任务: sub19[0m

[1;96m🎯 开始训练受试者: sub19[0m
[1;96m📊 受试者组别: 大样本组(>12个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m🎯 开始训练受试者: sub09[0m[1;96m📤 已提交训练任务: sub09[0m

[1;96m🎯 开始训练受试者: sub09[0m[1;96m📤 已提交训练任务: sub02[0m[1;96m🎯 开始训练受试者: sub02[0m


[1;96m📊 受试者组别: 大样本组(>12个样本)[0m[1;96m🎯 开始训练受试者: sub02[0m

[1;96m📁 正在加载训练数据...[0m[1;96m📊 受试者组别: 大样本组(>12个样本)[0m

[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 909, 测试样本数: 75[0m
🌊 小波配置: 类型=bior2.2, 层数=2
[1;96m📊 训练样本数: 908, 测试样本数: 76[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;96m📊 训练样本数: 906, 测试样本数: 78[0m
🌊 小波配置: 类型=bior2.2, 层数=2
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
🔧 激活函数配置: 标准ReLU✅ 三模态融合模块初始化完成

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[1;96m🚀 开始训练,总共690个epoch...[0m
[1;96m🚀 开始训练,总共690个epoch...[0m
[1;96m🚀 开始训练,总共690个epoch...[0m
[1;91m❌ 受试者 sub19 训练异常: name 'get_num_correct' is not defined[0m
[1;91m❌ 受试者 sub02 训练异常: name 'get_num_correct' is not defined[0m
[1;91m❌ 受试者 sub09 训练异常: name 'get_num_correct' is not defined[0m
[1;91m💥 ❌ sub19 训练失败[0m
[1;91m💥 ❌ sub09 训练失败[0m
[1;91m💥 ❌ sub02 训练失败[0m
[1;96m📊 资源监控已停止[0m
[1;96m⏱️ 批次训练完成，耗时: 51.4秒[0m
[1;96m📊 成功: 0, 失败: 3[0m
[1;96m😴 批次间休息5秒...[0m

[1m[1;96m📦 批次 3/9[0m
[1;96m----------[0m

[1m[1;92m🚀 并行训练批次[0m
[1;92m----------[0m
[1;96m🎯 开始并行训练 3 个受试者: ['sub10', 'sub23', 'sub12'][0m
[1;96m📊 资源监控已启动[0m
[1;96m🎯 开始训练受试者: sub10[0m[1;96m📤 已提交训练任务: sub10[0m

[1;96m🎯 开始训练受试者: sub10[0m[1;96m🎯 开始训练受试者: sub23[0m
[1;96m📤 已提交训练任务: sub23[0m

[1;96m🎯 开始训练受试者: sub23[0m[1;96m🎯 开始训练受试者: sub12[0m[1;96m📊 受试者组别: 大样本组(>12个样本)[0m[1;96m📤 已提交训练任务: sub12[0m


[1;96m🎯 开始训练受试者: sub12[0m

[1;96m📊 受试者组别: 中等样本组(7-12个样本)[0m[1;96m📁 正在加载训练数据...[0m
[1;96m📊 受试者组别: 中等样本组(7-12个样本)[0m[1;96m📁 正在加载训练数据...[0m


[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 958, 测试样本数: 26[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[1;96m📊 训练样本数: 934, 测试样本数: 50[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;96m📊 训练样本数: 923, 测试样本数: 61[0m
🌊 小波配置: 类型=bior2.2, 层数=2=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[1;96m🚀 开始训练,总共667个epoch...[0m
[1;96m🚀 开始训练,总共667个epoch...[0m
[1;96m🚀 开始训练,总共690个epoch...[0m
[1;91m❌ 受试者 sub10 训练异常: name 'get_num_correct' is not defined[0m
[1;91m❌ 受试者 sub12 训练异常: name 'get_num_correct' is not defined[0m[1;91m❌ 受试者 sub23 训练异常: name 'get_num_correct' is not defined[0m

[1;91m💥 ❌ sub10 训练失败[0m
[1;91m💥 ❌ sub12 训练失败[0m
[1;91m💥 ❌ sub23 训练失败[0m
[1;96m📊 资源监控已停止[0m
[1;96m⏱️ 批次训练完成，耗时: 51.7秒[0m
[1;96m📊 成功: 0, 失败: 3[0m
[1;96m😴 批次间休息5秒...[0m

[1m[1;96m📦 批次 4/9[0m
[1;96m----------[0m

[1m[1;92m🚀 并行训练批次[0m
[1;92m----------[0m
[1;96m🎯 开始并行训练 3 个受试者: ['sub20', 'sub11', 'sub01'][0m
[1;96m📊 资源监控已启动[0m
[1;96m🎯 开始训练受试者: sub20[0m[1;96m📤 已提交训练任务: sub20[0m

[1;96m🎯 开始训练受试者: sub20[0m[1;96m🎯 开始训练受试者: sub11[0m[1;96m📤 已提交训练任务: sub11[0m


[1;96m🎯 开始训练受试者: sub11[0m[1;96m📊 受试者组别: 中等样本组(7-12个样本)[0m[1;96m🎯 开始训练受试者: sub01[0m[1;96m📤 已提交训练任务: sub01[0m

[1;96m📁 正在加载训练数据...[0m
[1;96m🎯 开始训练受试者: sub01[0m


[1;96m📊 受试者组别: 中等样本组(7-12个样本)[0m
[1;96m📊 受试者组别: 中等样本组(7-12个样本)[0m[1;96m📁 正在加载训练数据...[0m

[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 960, 测试样本数: 24[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
[1;96m📊 训练样本数: 960, 测试样本数: 24[0m
=> 使用折叠除法: 8
🌊 小波配置: 类型=bior2.2, 层数=2
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;96m📁 正在加载测试数据...[0m
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 三模态融合模块初始化完成
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[1;96m📊 训练样本数: 960, 测试样本数: 24[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[1;96m🚀 开始训练,总共667个epoch...[0m
[1;96m🚀 开始训练,总共667个epoch...[0m
[1;96m🚀 开始训练,总共667个epoch...[0m
[1;91m❌ 受试者 sub11 训练异常: name 'get_num_correct' is not defined[0m
[1;91m❌ 受试者 sub01 训练异常: name 'get_num_correct' is not defined[0m
[1;91m❌ 受试者 sub20 训练异常: name 'get_num_correct' is not defined[0m
[1;91m💥 ❌ sub11 训练失败[0m
[1;91m💥 ❌ sub20 训练失败[0m
[1;91m💥 ❌ sub01 训练失败[0m
[1;96m📊 资源监控已停止[0m
[1;96m⏱️ 批次训练完成，耗时: 51.6秒[0m
[1;96m📊 成功: 0, 失败: 3[0m
[1;96m😴 批次间休息5秒...[0m

[1m[1;96m📦 批次 5/9[0m
[1;96m----------[0m

[1m[1;92m🚀 并行训练批次[0m
[1;92m----------[0m
[1;96m🎯 开始并行训练 3 个受试者: ['sub07', 'sub24', 'sub25'][0m
[1;96m📊 资源监控已启动[0m
[1;96m🎯 开始训练受试者: sub07[0m[1;96m📤 已提交训练任务: sub07[0m

[1;96m🎯 开始训练受试者: sub07[0m[1;96m🎯 开始训练受试者: sub24[0m

[1;96m📤 已提交训练任务: sub24[0m[1;96m🎯 开始训练受试者: sub24[0m[1;96m📊 受试者组别: 中等样本组(7-12个样本)[0m


[1;96m📤 已提交训练任务: sub25[0m
[1;96m🎯 开始训练受试者: sub25[0m[1;96m📊 受试者组别: 小样本组(3-6个样本)[0m[1;96m📁 正在加载训练数据...[0m


[1;96m🎯 开始训练受试者: sub25[0m[1;96m📁 正在加载训练数据...[0m

[1;96m📊 受试者组别: 小样本组(3-6个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 962, 测试样本数: 22[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;96m📊 训练样本数: 961, 测试样本数: 23[0m
🌊 小波配置: 类型=bior2.2, 层数=2
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
[1;96m📁 正在加载测试数据...[0m
✅ 三模态融合模块初始化完成
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[1;96m📊 训练样本数: 955, 测试样本数: 29[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

[1;92m============[0m
[1m[1;92m🚀 启动并行训练模式 🚀[0m
[1;92m============[0m

[1;96m📊 有效受试者数量: 25[0m
[1;96m📋 受试者列表: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub10', 'sub23', 'sub12', 'sub20', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub04', 'sub06', 'sub13', 'sub08', 'sub15', 'sub16', 'sub18', 'sub21', 'sub22'][0m

[1;92m============[0m
[1m[1;92m🚀 开始并行训练模式 🚀[0m
[1;92m============[0m

[1;96m🔢 并行数量: 4[0m
[1;96m📋 训练顺序: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub10', 'sub23', 'sub12', 'sub20', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub04', 'sub06', 'sub13', 'sub08', 'sub15', 'sub16', 'sub18', 'sub21', 'sub22'][0m

[1m[1;96m📦 批次 1/7[0m
[1;96m----------[0m

[1m[1;92m🚀 并行训练批次[0m
[1;92m----------[0m
[1;96m🎯 开始并行训练 4 个受试者: ['sub17', 'sub05', 'sub26', 'sub19'][0m
[1;96m📊 资源监控已启动[0m
[1;96m🎯 开始训练受试者: sub17[0m[1;96m📤 已提交训练任务: sub17[0m

[1;96m🎯 开始训练受试者: sub17[0m
[1;96m🎯 开始训练受试者: sub05[0m[1;96m📊 受试者组别: 大样本组(>12个样本)[0m[1;96m📤 已提交训练任务: sub05[0m

[1;96m🎯 开始训练受试者: sub05[0m[1;96m📁 正在加载训练数据...[0m


[1;96m📊 受试者组别: 大样本组(>12个样本)[0m[1;96m🎯 开始训练受试者: sub26[0m[1;96m📤 已提交训练任务: sub26[0m


[1;96m📁 正在加载训练数据...[0m[1;96m🎯 开始训练受试者: sub26[0m[1;96m📤 已提交训练任务: sub19[0m
[1;96m🎯 开始训练受试者: sub19[0m

[1;96m📊 受试者组别: 大样本组(>12个样本)[0m

[1;96m🎯 开始训练受试者: sub19[0m[1;96m📁 正在加载训练数据...[0m

[1;96m📊 受试者组别: 大样本组(>12个样本)[0m
[1;96m📁 正在加载训练数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📁 正在加载测试数据...[0m
[1;96m📊 训练样本数: 935, 测试样本数: 49[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;96m📊 训练样本数: 909, 测试样本数: 75[0m
🌊 小波配置: 类型=bior2.2, 层数=2
=> 使用折叠除法: 8
[1;96m📊 训练样本数: 911, 测试样本数: 73[0m
🌊 小波配置: 类型=bior2.2, 层数=2
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
[1;96m📊 训练样本数: 814, 测试样本数: 170[0m
🌊 小波配置: 类型=bior2.2, 层数=2=> 使用折叠除法: 8=> 使用折叠除法: 8


=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8=> 使用折叠除法: 8

🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8=> 使用折叠除法: 8

🔥 启用三模态特征融合: attention=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8🔥 启用三模态特征融合: attention

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 三模态融合模块初始化完成
✅ 三模态融合模块初始化完成
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

[1;92m============[0m
[1m[1;92m🚀 启动并行训练模式 🚀[0m
[1;92m============[0m

[1;96m📊 有效受试者数量: 25[0m
[1;96m📋 受试者列表: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub10', 'sub23', 'sub12', 'sub20', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub04', 'sub06', 'sub13', 'sub08', 'sub15', 'sub16', 'sub18', 'sub21', 'sub22'][0m

[1;92m============[0m
[1m[1;92m🚀 开始并行训练模式 🚀[0m
[1;92m============[0m

[1;96m🔢 并行数量: 4[0m
[1;96m📋 训练顺序: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub10', 'sub23', 'sub12', 'sub20', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub04', 'sub06', 'sub13', 'sub08', 'sub15', 'sub16', 'sub18', 'sub21', 'sub22'][0m

[1m[1;96m📦 批次 1/7[0m
[1;96m----------[0m

[1;92m====================[0m
[1m[1;92m🎯 🚀 并行训练批次 - 4个受试者 🎯[0m
[1;92m====================[0m

[1;96m📋 训练列表:[0m sub17, sub05, sub26, sub19
================================================================================
[1;96m📊 资源监控已启动[0m
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

[1;92m============[0m
[1m[1;92m🚀 启动并行训练模式 🚀[0m
[1;92m============[0m

[1;96m📊 有效受试者数量: 25[0m
[1;96m📋 受试者列表: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub10', 'sub23', 'sub12', 'sub20', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub04', 'sub06', 'sub13', 'sub08', 'sub15', 'sub16', 'sub18', 'sub21', 'sub22'][0m

[1;92m============[0m
[1m[1;92m🚀 开始并行训练模式 🚀[0m
[1;92m============[0m

[1;96m🔢 并行数量: 4[0m
[1;96m📋 训练顺序: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub10', 'sub23', 'sub12', 'sub20', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub04', 'sub06', 'sub13', 'sub08', 'sub15', 'sub16', 'sub18', 'sub21', 'sub22'][0m

[1m[1;96m📦 批次 1/7[0m
[1;96m----------[0m

[1;92m====================[0m
[1m[1;92m🎯 🚀 并行训练批次 - 4个受试者 🎯[0m
[1;92m====================[0m

[1;96m📋 训练列表:[0m sub17, sub05, sub26, sub19
================================================================================
[1;96m📊 资源监控已启动[0m
🎯 [DEBUG] 开始训练受试者: sub17
🎯 [DEBUG] 开始训练受试者: sub05🎯 [DEBUG] 开始训练受试者: sub26🎯 [DEBUG] 开始训练受试者: sub19🔍 [DEBUG] 开始处理受试者: sub17
[1;92m🎯 所有训练任务已提交，开始并行执行...[0m


================================================================================✅ [DEBUG] 受试者 sub17 基本检查通过
🔍 [DEBUG] 开始处理受试者: sub19


🔍 [DEBUG] 开始处理受试者: sub05🔍 [DEBUG] 开始处理受试者: sub26📊 [DEBUG] 受试者 sub17 组别: 大样本组(>12个样本)🔍 调试信息: 已提交 4 个任务


✅ [DEBUG] 受试者 sub19 基本检查通过✅ [DEBUG] 受试者 sub05 基本检查通过🔍 超时设置: 7200 秒



✅ [DEBUG] 受试者 sub26 基本检查通过🔍 开始等待任务完成...📊 [DEBUG] 受试者 sub19 组别: 大样本组(>12个样本)
📁 [DEBUG] 加载训练数据: /home/<USER>/data/ajq/SKD-TSTSAN-data/CASME2_LOSO_full/sub17/train



📊 [DEBUG] 受试者 sub26 组别: 大样本组(>12个样本)📊 [DEBUG] 受试者 sub05 组别: 大样本组(>12个样本)================================================================================
📁 [DEBUG] 加载训练数据: /home/<USER>/data/ajq/SKD-TSTSAN-data/CASME2_LOSO_full/sub19/train
📁 [DEBUG] 加载训练数据: /home/<USER>/data/ajq/SKD-TSTSAN-data/CASME2_LOSO_full/sub26/train


📊 [DEBUG] 找到 5 个表情类别: ['4', '1', '0', '2', '3']📁 [DEBUG] 加载训练数据: /home/<USER>/data/ajq/SKD-TSTSAN-data/CASME2_LOSO_full/sub05/train📊 [DEBUG] 找到 5 个表情类别: ['4', '1', '0', '2', '3']📊 [DEBUG] 找到 5 个表情类别: ['4', '1', '0', '2', '3']



📊 [DEBUG] 找到 5 个表情类别: ['4', '1', '0', '2', '3']
🌊 小波配置: 类型=bior2.2, 层数=2
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
🔧 激活函数配置: 标准ReLU
🔥 启用三模态特征融合: attention
🌊 小波配置: 类型=bior2.2, 层数=2
🌊 小波配置: 类型=bior2.2, 层数=2🔥 启用三模态特征融合: attention

✅ 三模态融合模块初始化完成
🔧 激活函数配置: 标准ReLU
✅ 三模态融合模块初始化完成
🔧 激活函数配置: 标准ReLU
🔥 启用三模态特征融合: attention
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
✅ 三模态融合模块初始化完成

正在关闭日志系统...
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

[1;92m============[0m
[1m[1;92m🚀 启动并行训练模式 🚀[0m
[1;92m============[0m

[1;96m📊 有效受试者数量: 25[0m
[1;96m📋 受试者列表: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub10', 'sub23', 'sub12', 'sub20', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub04', 'sub06', 'sub13', 'sub08', 'sub15', 'sub16', 'sub18', 'sub21', 'sub22'][0m

[1;92m============[0m
[1m[1;92m🚀 开始并行训练模式 🚀[0m
[1;92m============[0m

[1;96m🔢 并行数量: 2[0m
[1;96m📋 训练顺序: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub10', 'sub23', 'sub12', 'sub20', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub04', 'sub06', 'sub13', 'sub08', 'sub15', 'sub16', 'sub18', 'sub21', 'sub22'][0m

[1m[1;96m📦 批次 1/13[0m
[1;96m-----------[0m

[1;92m====================[0m
[1m[1;92m🎯 🚀 并行训练批次 - 2个受试者 🎯[0m
[1;92m====================[0m

[1;96m📋 训练列表:[0m sub17, sub05
================================================================================
[1;96m📊 资源监控已启动[0m
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

[1;92m============[0m
[1m[1;92m🚀 启动并行训练模式 🚀[0m
[1;92m============[0m

[1;96m📊 有效受试者数量: 25[0m
[1;96m📋 受试者列表: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub10', 'sub23', 'sub12', 'sub20', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub04', 'sub06', 'sub13', 'sub08', 'sub15', 'sub16', 'sub18', 'sub21', 'sub22'][0m

[1;92m============[0m
[1m[1;92m🚀 开始并行训练模式 🚀[0m
[1;92m============[0m

[1;96m🔢 并行数量: 2[0m
[1;96m📋 训练顺序: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub10', 'sub23', 'sub12', 'sub20', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub04', 'sub06', 'sub13', 'sub08', 'sub15', 'sub16', 'sub18', 'sub21', 'sub22'][0m

[1m[1;96m📦 批次 1/13[0m
[1;96m-----------[0m

[1;92m====================[0m
[1m[1;92m🎯 🚀 并行训练批次 - 2个受试者 🎯[0m
[1;92m====================[0m

[1;96m📋 训练列表:[0m sub17, sub05
================================================================================
[1;96m📊 资源监控已启动[0m
🎯 [DEBUG] 开始训练受试者: sub17
🎯 [DEBUG] 开始训练受试者: sub05
[1;92m🎯 所有训练任务已提交，开始并行执行...[0m

================================================================================
🔍 调试信息: 已提交 2 个任务
🔍 超时设置: 7200 秒
🔍 开始等待任务完成...
================================================================================
🔧 [DEBUG] sub17: 开始初始化模型...
🔧 [DEBUG] sub17: 卷积类型 = 3
🔧 [DEBUG] sub17: 导入模型构建函数完成
🔧 [DEBUG] sub17: 模型参数配置完成
  - 小波类型: bior2.2, 层数: 2
  - ECA类型: 1, GRSA: False
  - 三模态融合: True, 类型: attention
🔧 [DEBUG] sub17: 开始构建模型...
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
🔥 启用三模态特征融合: attention
🔧 [DEBUG] sub05: 开始初始化模型...
🔧 [DEBUG] sub05: 卷积类型 = 3
🔧 [DEBUG] sub05: 导入模型构建函数完成
🔧 [DEBUG] sub05: 模型参数配置完成
  - 小波类型: bior2.2, 层数: 2
  - ECA类型: 1, GRSA: False
  - 三模态融合: True, 类型: attention
🔧 [DEBUG] sub05: 开始构建模型...
🌊 小波配置: 类型=bior2.2, 层数=2
✅ 三模态融合模块初始化完成
🔧 [DEBUG] sub17: 模型构建完成，开始移动到设备...
🔧 激活函数配置: 标准ReLU
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
🔧 [DEBUG] sub05: 模型构建完成，开始移动到设备...
✅ [DEBUG] sub17: 模型初始化完成
🔧 [DEBUG] sub17: 开始处理预训练模型...
🔧 [DEBUG] sub17: 使用预训练模型
🔧 [DEBUG] sub17: 加载预训练权重: /home/<USER>/data/ajq/SKD-TSTSAN-new/Pretrained_model/ck注意力融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_attn_h8.pth
✅ [DEBUG] sub05: 模型初始化完成
🔧 [DEBUG] sub05: 开始处理预训练模型...
🔧 [DEBUG] sub05: 使用预训练模型
🔧 [DEBUG] sub05: 加载预训练权重: /home/<USER>/data/ajq/SKD-TSTSAN-new/Pretrained_model/ck注意力融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_attn_h8.pth
✅ [DEBUG] sub17: 预训练模型加载完成
🔧 [DEBUG] sub17: 创建优化器...
✅ [DEBUG] sub05: 预训练模型加载完成
🔧 [DEBUG] sub05: 创建优化器...
✅ [DEBUG] sub17: 优化器创建完成✅ [DEBUG] sub05: 优化器创建完成

🔧 [DEBUG] sub05: 创建数据加载器...🔧 [DEBUG] sub17: 创建数据加载器...

  - 训练数据: 911 样本  - 训练数据: 814 样本
  - 测试数据: 73 样本

  - 测试数据: 170 样本
✅ [DEBUG] sub05: 训练数据加载器创建完成✅ [DEBUG] sub17: 训练数据加载器创建完成

✅ [DEBUG] sub05: 测试数据加载器创建完成✅ [DEBUG] sub17: 测试数据加载器创建完成

🚀 [DEBUG] sub17: 开始训练循环...🚀 [DEBUG] sub05: 开始训练循环...

  - 最大迭代: 20000
  - 最大迭代: 20000  - 总轮数: 690

  - 每轮批次数: 29  - 总轮数: 770

  - 每轮批次数: 26🔄 [DEBUG] sub05: 开始第 1/690 轮训练...

📚 [DEBUG] sub05: 第 1 轮 - 开始训练阶段🔄 [DEBUG] sub17: 开始第 1/770 轮训练...
📚 [DEBUG] sub17: 第 1 轮 - 开始训练阶段

🔄 [DEBUG] sub05: 第 1 轮 - 开始处理批次数据...
🔄 [DEBUG] sub17: 第 1 轮 - 开始处理批次数据...
🔄 [DEBUG] sub17: 第 1 轮 - 处理批次 10/26
🔄 [DEBUG] sub05: 第 1 轮 - 处理批次 10/29
🔄 [DEBUG] sub17: 第 1 轮 - 处理批次 20/26
🔄 [DEBUG] sub05: 第 1 轮 - 处理批次 20/29
🧪 [DEBUG] sub17: 第 1 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 1 轮 - 开始测试批次处理...
📊 [DEBUG] sub17: 第 1 轮 - 测试准确率: 0.2882
🎯 [DEBUG] sub17: 第 1 轮 - 新的最佳准确率: 0.2882
💾 [DEBUG] sub17: 第 1 轮 - 最佳模型已保存
🔄 [DEBUG] sub17: 开始第 2/770 轮训练...
📚 [DEBUG] sub17: 第 2 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 2 轮 - 开始处理批次数据...
🧪 [DEBUG] sub05: 第 1 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 1 轮 - 开始测试批次处理...
📊 [DEBUG] sub05: 第 1 轮 - 测试准确率: 0.4932
🎯 [DEBUG] sub05: 第 1 轮 - 新的最佳准确率: 0.4932
💾 [DEBUG] sub05: 第 1 轮 - 最佳模型已保存
🔄 [DEBUG] sub05: 开始第 2/690 轮训练...
📚 [DEBUG] sub05: 第 2 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 2 轮 - 开始处理批次数据...
🔄 [DEBUG] sub17: 第 2 轮 - 处理批次 10/26
🔄 [DEBUG] sub05: 第 2 轮 - 处理批次 10/29
🔄 [DEBUG] sub17: 第 2 轮 - 处理批次 20/26
🔄 [DEBUG] sub05: 第 2 轮 - 处理批次 20/29
🧪 [DEBUG] sub17: 第 2 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 2 轮 - 开始测试批次处理...
📊 [DEBUG] sub17: 第 2 轮 - 测试准确率: 0.6059
🎯 [DEBUG] sub17: 第 2 轮 - 新的最佳准确率: 0.6059
💾 [DEBUG] sub17: 第 2 轮 - 最佳模型已保存
🔄 [DEBUG] sub17: 开始第 3/770 轮训练...
📚 [DEBUG] sub17: 第 3 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 3 轮 - 开始处理批次数据...
🧪 [DEBUG] sub05: 第 2 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 2 轮 - 开始测试批次处理...
📊 [DEBUG] sub05: 第 2 轮 - 测试准确率: 0.5753
🎯 [DEBUG] sub05: 第 2 轮 - 新的最佳准确率: 0.5753
💾 [DEBUG] sub05: 第 2 轮 - 最佳模型已保存
🔄 [DEBUG] sub05: 开始第 3/690 轮训练...
📚 [DEBUG] sub05: 第 3 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 3 轮 - 开始处理批次数据...
🔄 [DEBUG] sub17: 第 3 轮 - 处理批次 10/26
🔄 [DEBUG] sub05: 第 3 轮 - 处理批次 10/29
🔄 [DEBUG] sub17: 第 3 轮 - 处理批次 20/26
🔄 [DEBUG] sub05: 第 3 轮 - 处理批次 20/29
🧪 [DEBUG] sub17: 第 3 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 3 轮 - 开始测试批次处理...
📊 [DEBUG] sub17: 第 3 轮 - 测试准确率: 0.4824
🔄 [DEBUG] sub17: 开始第 4/770 轮训练...
📚 [DEBUG] sub17: 第 4 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 4 轮 - 开始处理批次数据...
🧪 [DEBUG] sub05: 第 3 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 3 轮 - 开始测试批次处理...
📊 [DEBUG] sub05: 第 3 轮 - 测试准确率: 0.5205
🔄 [DEBUG] sub05: 开始第 4/690 轮训练...
📚 [DEBUG] sub05: 第 4 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 4 轮 - 开始处理批次数据...
🔄 [DEBUG] sub17: 第 4 轮 - 处理批次 10/26
🔄 [DEBUG] sub05: 第 4 轮 - 处理批次 10/29
🔄 [DEBUG] sub17: 第 4 轮 - 处理批次 20/26
🔄 [DEBUG] sub05: 第 4 轮 - 处理批次 20/29
🧪 [DEBUG] sub17: 第 4 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 4 轮 - 开始测试批次处理...
📊 [DEBUG] sub17: 第 4 轮 - 测试准确率: 0.5471
🔄 [DEBUG] sub17: 开始第 5/770 轮训练...
📚 [DEBUG] sub17: 第 5 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 5 轮 - 开始处理批次数据...
🧪 [DEBUG] sub05: 第 4 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 4 轮 - 开始测试批次处理...
📊 [DEBUG] sub05: 第 4 轮 - 测试准确率: 0.5205
🔄 [DEBUG] sub05: 开始第 5/690 轮训练...
📚 [DEBUG] sub05: 第 5 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 5 轮 - 开始处理批次数据...
🔄 [DEBUG] sub17: 第 5 轮 - 处理批次 10/26
🔄 [DEBUG] sub05: 第 5 轮 - 处理批次 10/29
🔄 [DEBUG] sub17: 第 5 轮 - 处理批次 20/26
🧪 [DEBUG] sub17: 第 5 轮 - 开始测试阶段
🔄 [DEBUG] sub05: 第 5 轮 - 处理批次 20/29
🧪 [DEBUG] sub17: 第 5 轮 - 开始测试批次处理...
📊 [DEBUG] sub17: 第 5 轮 - 测试准确率: 0.5824
🔄 [DEBUG] sub17: 开始第 6/770 轮训练...
📚 [DEBUG] sub17: 第 6 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 6 轮 - 开始处理批次数据...
🧪 [DEBUG] sub05: 第 5 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 5 轮 - 开始测试批次处理...
📊 [DEBUG] sub05: 第 5 轮 - 测试准确率: 0.5753
🔄 [DEBUG] sub05: 开始第 6/690 轮训练...
📚 [DEBUG] sub05: 第 6 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 6 轮 - 开始处理批次数据...
🔄 [DEBUG] sub17: 第 6 轮 - 处理批次 10/26
🔄 [DEBUG] sub05: 第 6 轮 - 处理批次 10/29
🔄 [DEBUG] sub17: 第 6 轮 - 处理批次 20/26
🧪 [DEBUG] sub17: 第 6 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 6 轮 - 开始测试批次处理...
🔄 [DEBUG] sub05: 第 6 轮 - 处理批次 20/29
📊 [DEBUG] sub17: 第 6 轮 - 测试准确率: 0.7000
🎯 [DEBUG] sub17: 第 6 轮 - 新的最佳准确率: 0.7000
💾 [DEBUG] sub17: 第 6 轮 - 最佳模型已保存
🔄 [DEBUG] sub17: 开始第 7/770 轮训练...
📚 [DEBUG] sub17: 第 7 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 7 轮 - 开始处理批次数据...
🧪 [DEBUG] sub05: 第 6 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 6 轮 - 开始测试批次处理...
🔄 [DEBUG] sub17: 第 7 轮 - 处理批次 10/26
📊 [DEBUG] sub05: 第 6 轮 - 测试准确率: 0.5753
🔄 [DEBUG] sub05: 开始第 7/690 轮训练...
📚 [DEBUG] sub05: 第 7 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 7 轮 - 开始处理批次数据...
🔄 [DEBUG] sub05: 第 7 轮 - 处理批次 10/29
🔄 [DEBUG] sub17: 第 7 轮 - 处理批次 20/26
🧪 [DEBUG] sub17: 第 7 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 7 轮 - 开始测试批次处理...
📊 [DEBUG] sub17: 第 7 轮 - 测试准确率: 0.1824
🔄 [DEBUG] sub17: 开始第 8/770 轮训练...
📚 [DEBUG] sub17: 第 8 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 8 轮 - 开始处理批次数据...
🔄 [DEBUG] sub05: 第 7 轮 - 处理批次 20/29
🔄 [DEBUG] sub17: 第 8 轮 - 处理批次 10/26
🧪 [DEBUG] sub05: 第 7 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 7 轮 - 开始测试批次处理...
📊 [DEBUG] sub05: 第 7 轮 - 测试准确率: 0.5479
🔄 [DEBUG] sub05: 开始第 8/690 轮训练...
📚 [DEBUG] sub05: 第 8 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 8 轮 - 开始处理批次数据...
🔄 [DEBUG] sub17: 第 8 轮 - 处理批次 20/26
🔄 [DEBUG] sub05: 第 8 轮 - 处理批次 10/29
🧪 [DEBUG] sub17: 第 8 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 8 轮 - 开始测试批次处理...
📊 [DEBUG] sub17: 第 8 轮 - 测试准确率: 0.2824
🔄 [DEBUG] sub17: 开始第 9/770 轮训练...
📚 [DEBUG] sub17: 第 9 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 9 轮 - 开始处理批次数据...
🔄 [DEBUG] sub05: 第 8 轮 - 处理批次 20/29
🔄 [DEBUG] sub17: 第 9 轮 - 处理批次 10/26
🧪 [DEBUG] sub05: 第 8 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 8 轮 - 开始测试批次处理...
📊 [DEBUG] sub05: 第 8 轮 - 测试准确率: 0.7123
🎯 [DEBUG] sub05: 第 8 轮 - 新的最佳准确率: 0.7123
💾 [DEBUG] sub05: 第 8 轮 - 最佳模型已保存
🔄 [DEBUG] sub05: 开始第 9/690 轮训练...
📚 [DEBUG] sub05: 第 9 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 9 轮 - 开始处理批次数据...
🔄 [DEBUG] sub17: 第 9 轮 - 处理批次 20/26
🔄 [DEBUG] sub05: 第 9 轮 - 处理批次 10/29
🧪 [DEBUG] sub17: 第 9 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 9 轮 - 开始测试批次处理...
📊 [DEBUG] sub17: 第 9 轮 - 测试准确率: 0.2765
🔄 [DEBUG] sub17: 开始第 10/770 轮训练...
📚 [DEBUG] sub17: 第 10 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 10 轮 - 开始处理批次数据...
🔄 [DEBUG] sub05: 第 9 轮 - 处理批次 20/29
🔄 [DEBUG] sub17: 第 10 轮 - 处理批次 10/26
🧪 [DEBUG] sub05: 第 9 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 9 轮 - 开始测试批次处理...
📊 [DEBUG] sub05: 第 9 轮 - 测试准确率: 0.5753
🔄 [DEBUG] sub05: 开始第 10/690 轮训练...
📚 [DEBUG] sub05: 第 10 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 10 轮 - 开始处理批次数据...
🔄 [DEBUG] sub17: 第 10 轮 - 处理批次 20/26
🧪 [DEBUG] sub17: 第 10 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 10 轮 - 开始测试批次处理...
🔄 [DEBUG] sub05: 第 10 轮 - 处理批次 10/29
📊 [DEBUG] sub17: 第 10 轮 - 测试准确率: 0.2412
🔄 [DEBUG] sub17: 开始第 11/770 轮训练...
📚 [DEBUG] sub17: 第 11 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 11 轮 - 开始处理批次数据...
🔄 [DEBUG] sub05: 第 10 轮 - 处理批次 20/29
🔄 [DEBUG] sub17: 第 11 轮 - 处理批次 10/26
🧪 [DEBUG] sub05: 第 10 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 10 轮 - 开始测试批次处理...
📊 [DEBUG] sub05: 第 10 轮 - 测试准确率: 0.6164
🔄 [DEBUG] sub05: 开始第 11/690 轮训练...
📚 [DEBUG] sub05: 第 11 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 11 轮 - 开始处理批次数据...
🔄 [DEBUG] sub17: 第 11 轮 - 处理批次 20/26
🧪 [DEBUG] sub17: 第 11 轮 - 开始测试阶段
🔄 [DEBUG] sub05: 第 11 轮 - 处理批次 10/29
🧪 [DEBUG] sub17: 第 11 轮 - 开始测试批次处理...
📊 [DEBUG] sub17: 第 11 轮 - 测试准确率: 0.2882
🔄 [DEBUG] sub17: 开始第 12/770 轮训练...
📚 [DEBUG] sub17: 第 12 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 12 轮 - 开始处理批次数据...
🔄 [DEBUG] sub05: 第 11 轮 - 处理批次 20/29
🔄 [DEBUG] sub17: 第 12 轮 - 处理批次 10/26
🧪 [DEBUG] sub05: 第 11 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 11 轮 - 开始测试批次处理...
📊 [DEBUG] sub05: 第 11 轮 - 测试准确率: 0.7123
🔄 [DEBUG] sub05: 开始第 12/690 轮训练...
📚 [DEBUG] sub05: 第 12 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 12 轮 - 开始处理批次数据...
🔄 [DEBUG] sub17: 第 12 轮 - 处理批次 20/26
🧪 [DEBUG] sub17: 第 12 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 12 轮 - 开始测试批次处理...
🔄 [DEBUG] sub05: 第 12 轮 - 处理批次 10/29
📊 [DEBUG] sub17: 第 12 轮 - 测试准确率: 0.6529
🔄 [DEBUG] sub17: 开始第 13/770 轮训练...
📚 [DEBUG] sub17: 第 13 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 13 轮 - 开始处理批次数据...
🔄 [DEBUG] sub05: 第 12 轮 - 处理批次 20/29
🔄 [DEBUG] sub17: 第 13 轮 - 处理批次 10/26
🧪 [DEBUG] sub05: 第 12 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 12 轮 - 开始测试批次处理...
📊 [DEBUG] sub05: 第 12 轮 - 测试准确率: 0.4658🔄 [DEBUG] sub17: 第 13 轮 - 处理批次 20/26

🔄 [DEBUG] sub05: 开始第 13/690 轮训练...
📚 [DEBUG] sub05: 第 13 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 13 轮 - 开始处理批次数据...
🧪 [DEBUG] sub17: 第 13 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 13 轮 - 开始测试批次处理...
📊 [DEBUG] sub17: 第 13 轮 - 测试准确率: 0.6529
🔄 [DEBUG] sub17: 开始第 14/770 轮训练...
📚 [DEBUG] sub17: 第 14 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 14 轮 - 开始处理批次数据...
🔄 [DEBUG] sub05: 第 13 轮 - 处理批次 10/29
🔄 [DEBUG] sub05: 第 13 轮 - 处理批次 20/29
🔄 [DEBUG] sub17: 第 14 轮 - 处理批次 10/26
🧪 [DEBUG] sub05: 第 13 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 13 轮 - 开始测试批次处理...
🔄 [DEBUG] sub17: 第 14 轮 - 处理批次 20/26
📊 [DEBUG] sub05: 第 13 轮 - 测试准确率: 0.4110
🔄 [DEBUG] sub05: 开始第 14/690 轮训练...
📚 [DEBUG] sub05: 第 14 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 14 轮 - 开始处理批次数据...
🧪 [DEBUG] sub17: 第 14 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 14 轮 - 开始测试批次处理...
📊 [DEBUG] sub17: 第 14 轮 - 测试准确率: 0.5059
🔄 [DEBUG] sub17: 开始第 15/770 轮训练...
📚 [DEBUG] sub17: 第 15 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 15 轮 - 开始处理批次数据...
🔄 [DEBUG] sub05: 第 14 轮 - 处理批次 10/29
🔄 [DEBUG] sub17: 第 15 轮 - 处理批次 10/26
🔄 [DEBUG] sub05: 第 14 轮 - 处理批次 20/29
🧪 [DEBUG] sub05: 第 14 轮 - 开始测试阶段
🔄 [DEBUG] sub17: 第 15 轮 - 处理批次 20/26
🧪 [DEBUG] sub05: 第 14 轮 - 开始测试批次处理...
📊 [DEBUG] sub05: 第 14 轮 - 测试准确率: 0.6027
🔄 [DEBUG] sub05: 开始第 15/690 轮训练...
📚 [DEBUG] sub05: 第 15 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 15 轮 - 开始处理批次数据...
🧪 [DEBUG] sub17: 第 15 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 15 轮 - 开始测试批次处理...
📊 [DEBUG] sub17: 第 15 轮 - 测试准确率: 0.6059
🔄 [DEBUG] sub17: 开始第 16/770 轮训练...
📚 [DEBUG] sub17: 第 16 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 16 轮 - 开始处理批次数据...
🔄 [DEBUG] sub05: 第 15 轮 - 处理批次 10/29
🔄 [DEBUG] sub17: 第 16 轮 - 处理批次 10/26
🔄 [DEBUG] sub05: 第 15 轮 - 处理批次 20/29
🔄 [DEBUG] sub17: 第 16 轮 - 处理批次 20/26
🧪 [DEBUG] sub05: 第 15 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 15 轮 - 开始测试批次处理...
📊 [DEBUG] sub05: 第 15 轮 - 测试准确率: 0.5616
🔄 [DEBUG] sub05: 开始第 16/690 轮训练...
📚 [DEBUG] sub05: 第 16 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 16 轮 - 开始处理批次数据...
🧪 [DEBUG] sub17: 第 16 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 16 轮 - 开始测试批次处理...
📊 [DEBUG] sub17: 第 16 轮 - 测试准确率: 0.5353
🔄 [DEBUG] sub17: 开始第 17/770 轮训练...
📚 [DEBUG] sub17: 第 17 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 17 轮 - 开始处理批次数据...
🔄 [DEBUG] sub05: 第 16 轮 - 处理批次 10/29
🔄 [DEBUG] sub17: 第 17 轮 - 处理批次 10/26
🔄 [DEBUG] sub05: 第 16 轮 - 处理批次 20/29
🔄 [DEBUG] sub17: 第 17 轮 - 处理批次 20/26
🧪 [DEBUG] sub05: 第 16 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 16 轮 - 开始测试批次处理...
📊 [DEBUG] sub05: 第 16 轮 - 测试准确率: 0.6164
🔄 [DEBUG] sub05: 开始第 17/690 轮训练...
📚 [DEBUG] sub05: 第 17 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 17 轮 - 开始处理批次数据...
🧪 [DEBUG] sub17: 第 17 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 17 轮 - 开始测试批次处理...
📊 [DEBUG] sub17: 第 17 轮 - 测试准确率: 0.5588
🔄 [DEBUG] sub17: 开始第 18/770 轮训练...
📚 [DEBUG] sub17: 第 18 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 18 轮 - 开始处理批次数据...
🔄 [DEBUG] sub05: 第 17 轮 - 处理批次 10/29
🔄 [DEBUG] sub17: 第 18 轮 - 处理批次 10/26
🔄 [DEBUG] sub05: 第 17 轮 - 处理批次 20/29
🔄 [DEBUG] sub17: 第 18 轮 - 处理批次 20/26
🧪 [DEBUG] sub05: 第 17 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 18 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 17 轮 - 开始测试批次处理...
🧪 [DEBUG] sub17: 第 18 轮 - 开始测试批次处理...
📊 [DEBUG] sub05: 第 17 轮 - 测试准确率: 0.6301
🔄 [DEBUG] sub05: 开始第 18/690 轮训练...
📚 [DEBUG] sub05: 第 18 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 18 轮 - 开始处理批次数据...
📊 [DEBUG] sub17: 第 18 轮 - 测试准确率: 0.7529
🎯 [DEBUG] sub17: 第 18 轮 - 新的最佳准确率: 0.7529
💾 [DEBUG] sub17: 第 18 轮 - 最佳模型已保存
🔄 [DEBUG] sub17: 开始第 19/770 轮训练...
📚 [DEBUG] sub17: 第 19 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 19 轮 - 开始处理批次数据...
🔄 [DEBUG] sub05: 第 18 轮 - 处理批次 10/29
🔄 [DEBUG] sub17: 第 19 轮 - 处理批次 10/26
🔄 [DEBUG] sub05: 第 18 轮 - 处理批次 20/29
🔄 [DEBUG] sub17: 第 19 轮 - 处理批次 20/26
🧪 [DEBUG] sub17: 第 19 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 19 轮 - 开始测试批次处理...
🧪 [DEBUG] sub05: 第 18 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 18 轮 - 开始测试批次处理...
📊 [DEBUG] sub17: 第 19 轮 - 测试准确率: 0.5294
🔄 [DEBUG] sub17: 开始第 20/770 轮训练...
📚 [DEBUG] sub17: 第 20 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 20 轮 - 开始处理批次数据...
📊 [DEBUG] sub05: 第 18 轮 - 测试准确率: 0.6986
🔄 [DEBUG] sub05: 开始第 19/690 轮训练...
📚 [DEBUG] sub05: 第 19 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 19 轮 - 开始处理批次数据...
🔄 [DEBUG] sub17: 第 20 轮 - 处理批次 10/26
🔄 [DEBUG] sub05: 第 19 轮 - 处理批次 10/29
🔄 [DEBUG] sub17: 第 20 轮 - 处理批次 20/26
🔄 [DEBUG] sub05: 第 19 轮 - 处理批次 20/29
🧪 [DEBUG] sub17: 第 20 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 20 轮 - 开始测试批次处理...
📊 [DEBUG] sub17: 第 20 轮 - 测试准确率: 0.1706
🔄 [DEBUG] sub17: 开始第 21/770 轮训练...
📚 [DEBUG] sub17: 第 21 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 21 轮 - 开始处理批次数据...
🧪 [DEBUG] sub05: 第 19 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 19 轮 - 开始测试批次处理...
📊 [DEBUG] sub05: 第 19 轮 - 测试准确率: 0.5205
🔄 [DEBUG] sub05: 开始第 20/690 轮训练...
📚 [DEBUG] sub05: 第 20 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 20 轮 - 开始处理批次数据...
🔄 [DEBUG] sub17: 第 21 轮 - 处理批次 10/26
🔄 [DEBUG] sub05: 第 20 轮 - 处理批次 10/29
🔄 [DEBUG] sub17: 第 21 轮 - 处理批次 20/26
🔄 [DEBUG] sub05: 第 20 轮 - 处理批次 20/29
🧪 [DEBUG] sub17: 第 21 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 21 轮 - 开始测试批次处理...
📊 [DEBUG] sub17: 第 21 轮 - 测试准确率: 0.2235
🔄 [DEBUG] sub17: 开始第 22/770 轮训练...
📚 [DEBUG] sub17: 第 22 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 22 轮 - 开始处理批次数据...
🧪 [DEBUG] sub05: 第 20 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 20 轮 - 开始测试批次处理...
📊 [DEBUG] sub05: 第 20 轮 - 测试准确率: 0.6849
🔄 [DEBUG] sub05: 开始第 21/690 轮训练...
📚 [DEBUG] sub05: 第 21 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 21 轮 - 开始处理批次数据...
🔄 [DEBUG] sub17: 第 22 轮 - 处理批次 10/26
🔄 [DEBUG] sub05: 第 21 轮 - 处理批次 10/29
🔄 [DEBUG] sub17: 第 22 轮 - 处理批次 20/26
🔄 [DEBUG] sub05: 第 21 轮 - 处理批次 20/29
🧪 [DEBUG] sub17: 第 22 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 22 轮 - 开始测试批次处理...
📊 [DEBUG] sub17: 第 22 轮 - 测试准确率: 0.2588
🔄 [DEBUG] sub17: 开始第 23/770 轮训练...
📚 [DEBUG] sub17: 第 23 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 23 轮 - 开始处理批次数据...
🧪 [DEBUG] sub05: 第 21 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 21 轮 - 开始测试批次处理...
📊 [DEBUG] sub05: 第 21 轮 - 测试准确率: 0.5205
🔄 [DEBUG] sub05: 开始第 22/690 轮训练...
📚 [DEBUG] sub05: 第 22 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 22 轮 - 开始处理批次数据...
🔄 [DEBUG] sub17: 第 23 轮 - 处理批次 10/26
🔄 [DEBUG] sub05: 第 22 轮 - 处理批次 10/29
🔄 [DEBUG] sub17: 第 23 轮 - 处理批次 20/26
🧪 [DEBUG] sub17: 第 23 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 23 轮 - 开始测试批次处理...
🔄 [DEBUG] sub05: 第 22 轮 - 处理批次 20/29
📊 [DEBUG] sub17: 第 23 轮 - 测试准确率: 0.4471
🔄 [DEBUG] sub17: 开始第 24/770 轮训练...
📚 [DEBUG] sub17: 第 24 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 24 轮 - 开始处理批次数据...
🧪 [DEBUG] sub05: 第 22 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 22 轮 - 开始测试批次处理...
📊 [DEBUG] sub05: 第 22 轮 - 测试准确率: 0.5342
🔄 [DEBUG] sub05: 开始第 23/690 轮训练...
📚 [DEBUG] sub05: 第 23 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 23 轮 - 开始处理批次数据...
🔄 [DEBUG] sub17: 第 24 轮 - 处理批次 10/26
🔄 [DEBUG] sub05: 第 23 轮 - 处理批次 10/29
🔄 [DEBUG] sub17: 第 24 轮 - 处理批次 20/26
🧪 [DEBUG] sub17: 第 24 轮 - 开始测试阶段
🧪 [DEBUG] sub17: 第 24 轮 - 开始测试批次处理...
🔄 [DEBUG] sub05: 第 23 轮 - 处理批次 20/29
📊 [DEBUG] sub17: 第 24 轮 - 测试准确率: 0.3235
🔄 [DEBUG] sub17: 开始第 25/770 轮训练...
📚 [DEBUG] sub17: 第 25 轮 - 开始训练阶段
🔄 [DEBUG] sub17: 第 25 轮 - 开始处理批次数据...
🧪 [DEBUG] sub05: 第 23 轮 - 开始测试阶段
🧪 [DEBUG] sub05: 第 23 轮 - 开始测试批次处理...
🔄 [DEBUG] sub17: 第 25 轮 - 处理批次 10/26
📊 [DEBUG] sub05: 第 23 轮 - 测试准确率: 0.6849
🔄 [DEBUG] sub05: 开始第 24/690 轮训练...
📚 [DEBUG] sub05: 第 24 轮 - 开始训练阶段
🔄 [DEBUG] sub05: 第 24 轮 - 开始处理批次数据...
🔄 [DEBUG] sub05: 第 24 轮 - 处理批次 10/29
🔄 [DEBUG] sub17: 第 25 轮 - 处理批次 20/26

正在关闭日志系统...
