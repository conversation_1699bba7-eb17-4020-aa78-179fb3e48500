
================================================================================
【训练开始】
================================================================================

【硬件信息】使用单个GPU进行训练
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

【损失函数配置】
使用加权Focal Loss (6分类)
类别权重: ['0.1339', '0.0542', '0.0669', '0.3012', '0.3708', '0.0730']
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: S30
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

S30测试标签: [4, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5, 3]
创建受试者目录: S30
创建日志目录: ./Experiment_for_recognize/MMEWC4JJ1_class6_conv6_legm2/S30/logs
正在初始化 小波变换风车形感受野注意力卷积 (轻量化) 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用小波变换风车形感受野注意力卷积功能(轻量化):
  - 融合三种先进技术的轻量化版本
  - 大幅减少参数数量，保持核心功能
  - 推荐选择，性能与效率的最佳平衡
✅ 启用标准LEGM模块
  - 局部-全局特征增强
  - 提升特征表达能力
  - 推荐选择，性能提升明显

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
