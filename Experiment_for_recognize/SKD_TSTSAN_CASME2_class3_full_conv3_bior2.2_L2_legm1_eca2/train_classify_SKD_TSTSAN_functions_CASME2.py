# 导入所需的库和模块
import warnings
warnings.filterwarnings("ignore", category=FutureWarning)  # 忽略未来版本警告
warnings.filterwarnings("ignore", category=UserWarning)  # 忽略用户警告

# 首先导入基础依赖
import os
import sys
import shutil  # 添加shutil导入
import numpy as np
import random
import time
import math
import subprocess
import socket
import threading
import webbrowser
from collections import OrderedDict
from os import path

# 导入torch相关依赖
import torch
import torch.backends.cudnn as cudnn
from torch.utils.data import TensorDataset, DataLoader
from torch.utils.tensorboard import SummaryWriter
from torch.cuda.amp import autocast, GradScaler

# 导入其他机器学习相关依赖
import cv2
from sklearn.metrics import confusion_matrix
from sklearn.metrics import classification_report, precision_recall_fscore_support
import visdom

# 导入自定义模块
from all_model import *

# 颜色和样式定义
class Colors:
    """ANSI颜色代码"""
    # 基础颜色
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'

    # 亮色
    BRIGHT_RED = '\033[1;91m'
    BRIGHT_GREEN = '\033[1;92m'
    BRIGHT_YELLOW = '\033[1;93m'
    BRIGHT_BLUE = '\033[1;94m'
    BRIGHT_MAGENTA = '\033[1;95m'
    BRIGHT_CYAN = '\033[1;96m'

    # 背景色
    BG_RED = '\033[101m'
    BG_GREEN = '\033[102m'
    BG_YELLOW = '\033[103m'
    BG_BLUE = '\033[104m'
    BG_MAGENTA = '\033[105m'
    BG_CYAN = '\033[106m'

    # 样式
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    ITALIC = '\033[3m'

    # 重置
    RESET = '\033[0m'

    # 渐变效果
    RAINBOW = ['\033[91m', '\033[93m', '\033[92m', '\033[96m', '\033[94m', '\033[95m']

def colorize(text, color):
    """给文本添加颜色"""
    return f"{color}{text}{Colors.RESET}"

def rainbow_text(text):
    """彩虹文本效果"""
    result = ""
    for i, char in enumerate(text):
        color = Colors.RAINBOW[i % len(Colors.RAINBOW)]
        result += f"{color}{char}"
    return result + Colors.RESET

def print_banner(text, color=Colors.BRIGHT_CYAN, emoji="🎯"):
    """打印漂亮的横幅"""
    border = "=" * (len(text) + 4)
    print(f"\n{colorize(border, color)}")
    print(f"{colorize(f'{emoji} {text} {emoji}', Colors.BOLD + color)}")
    print(f"{colorize(border, color)}\n")

def print_section(title, color=Colors.BRIGHT_BLUE, emoji="📋"):
    """打印章节标题"""
    print(f"\n{colorize(f'{emoji} {title}', Colors.BOLD + color)}")
    print(f"{colorize('-' * (len(title) + 4), color)}")

def print_success(text, emoji="✅"):
    """打印成功信息"""
    print(f"{colorize(f'{emoji} {text}', Colors.BRIGHT_GREEN)}")

def print_warning(text, emoji="⚠️"):
    """打印警告信息"""
    print(f"{colorize(f'{emoji} {text}', Colors.BRIGHT_YELLOW)}")

def print_error(text, emoji="❌"):
    """打印错误信息"""
    print(f"{colorize(f'{emoji} {text}', Colors.BRIGHT_RED)}")

def print_info(text, emoji="ℹ️"):
    """打印信息"""
    print(f"{colorize(f'{emoji} {text}', Colors.BRIGHT_CYAN)}")

def print_highlight(text, emoji="⭐"):
    """打印高亮信息"""
    print(f"{colorize(f'{emoji} {text}', Colors.BOLD + Colors.BRIGHT_MAGENTA)}")

# 导入BatchNorm修复模块（可选）
try:
    from batchnorm_fix import apply_batchnorm_fix, count_normalization_layers
    BATCHNORM_FIX_AVAILABLE = True
    # 只在启动时显示一次导入成功信息
except ImportError as e:
    # BatchNorm修复模块不可用，但不影响正常训练
    BATCHNORM_FIX_AVAILABLE = False

    def apply_batchnorm_fix(*args, **kwargs):
        """占位函数，当BatchNorm修复模块不可用时使用"""
        raise ImportError("BatchNorm修复模块不可用")

    def count_normalization_layers(*args, **kwargs):
        """占位函数，当BatchNorm修复模块不可用时使用"""
        return {}

# 设置GPU优先级和设备
os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"  # 确保GPU ID与nvidia-smi一致

def get_gpu_memory_info():
    """获取GPU内存使用信息"""
    if not torch.cuda.is_available():
        return "GPU不可用"

    gpu_info = []
    for i in range(torch.cuda.device_count()):
        try:
            allocated = torch.cuda.memory_allocated(i) / 1024**3  # GB
            reserved = torch.cuda.memory_reserved(i) / 1024**3   # GB
            total = torch.cuda.get_device_properties(i).total_memory / 1024**3  # GB
            gpu_info.append(f"GPU{i}: {allocated:.1f}GB/{total:.1f}GB ({allocated/total*100:.1f}%)")
        except:
            gpu_info.append(f"GPU{i}: 信息获取失败")

    return " | ".join(gpu_info)

def setup_gpu():
    """
    设置GPU环境,包括设备检测、内存管理等
    返回:
        is_cuda_available: 是否有可用的GPU
        gpu_count: 可用的GPU数量
        gpu_info: GPU信息字符串
    """
    try:
        is_cuda_available = torch.cuda.is_available()
        if not is_cuda_available:
            return False, 0, "未检测到可用的GPU设备"
            
        # 获取GPU信息
        gpu_count = torch.cuda.device_count()
        gpu_info = []
        
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_mem = torch.cuda.get_device_properties(i).total_memory / (1024**3)  # 转换为GB
            gpu_info.append(f"GPU {i}: {gpu_name} ({gpu_mem:.1f}GB)")
            
            # 设置GPU内存管理
            try:
                torch.cuda.set_per_process_memory_fraction(0.95, i)  # 限制使用95%的显存
                torch.cuda.empty_cache()  # 清空GPU缓存
            except Exception as e:
                print(f"警告: GPU {i} 内存管理设置失败: {str(e)}")
                
        # 如果有多个GPU,设置主GPU
        if gpu_count > 0:
            torch.cuda.set_device(0)
            
        return True, gpu_count, "\n".join(gpu_info)
        
    except Exception as e:
        print(f"GPU初始化过程中出现错误: {str(e)}")
        return False, 0, f"GPU初始化失败: {str(e)}"

# 初始化GPU设置
is_cuda_available, gpu_count, gpu_info = setup_gpu()

if is_cuda_available:
    print("\n【GPU 信息】")
    print(f"检测到 {gpu_count} 个可用的GPU:")
    print(gpu_info)
    print("\nGPU内存管理已启用:")
    print("- 每个GPU最大使用95%显存")
    print("- 已清空GPU缓存")
else:
    print("\n【警告】未检测到可用的GPU,将使用CPU模式运行")

# CASME2数据集中各类别样本数量
CASME2_5class_numbers = [32, 25, 63, 27, 99]  # 5分类样本数量
CASME2_3class_numbers = [32, 90, 25]  # 3分类样本数量 (正性、负性、惊讶)

# 情绪标签映射
EMOTION_LABELS_5CLASS = {'快乐': 0, '惊讶': 1, '厌恶': 2, '压抑': 3, '其他': 4}
EMOTION_LABELS_3CLASS = {'正性': 0, '负性': 1, '惊讶': 2}

# 5分类到3分类的映射关系
CLASS_5_TO_3_MAPPING = {
    0: 0,  # 快乐 -> 正性
    1: 2,  # 惊讶 -> 惊讶
    2: 1,  # 厌恶 -> 负性
    3: 1,  # 压抑 -> 负性
}

def convert_5class_to_3class(label_5class):
    """
    将5分类标签转换为3分类标签，如果是其他类别则返回None
    """
    return CLASS_5_TO_3_MAPPING.get(label_5class, None)  # 如果是其他类别(4)则返回None

def get_subject_group_info(subject_name, class_num):
    """
    获取受试者所属的组别信息
    参数:
        subject_name: 受试者名称
        class_num: 分类数量(3或5)
    返回:
        组别信息字符串
    """
    if class_num == 3:
        if subject_name in INVALID_SUBJECTS_3CLASS:
            return "无效受试者(只包含其他类别样本)"
        elif subject_name in LARGE_SUBJECTS_3CLASS:
            return "大样本组(>12个样本)"
        elif subject_name in MEDIUM_SUBJECTS_3CLASS:
            return "中等样本组(7-12个样本)"
        elif subject_name in SMALL_SUBJECTS_3CLASS:
            return "小样本组(3-6个样本)"
        elif subject_name in VERY_SMALL_SUBJECTS_3CLASS:
            return "极小样本组(<=2个样本)"
        else:
            return "未知组别"
    else:  # 5分类
        if subject_name in LARGE_SUBJECTS_5CLASS:
            return "大样本组(>12个样本)"
        elif subject_name in MEDIUM_SUBJECTS_5CLASS:
            return "中等样本组(7-12个样本)"
        elif subject_name in SMALL_SUBJECTS_5CLASS:
            return "小样本组(3-6个样本)"
        elif subject_name in VERY_SMALL_SUBJECTS_5CLASS:
            return "极小样本组(<=3个样本)"
        else:
            return "未知组别"

# 5分类的分组（保持不变）
# 大样本组(>12个样本)
LARGE_SUBJECTS_5CLASS = ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub10']
# 中等样本组(7-12个样本)
MEDIUM_SUBJECTS_5CLASS = ['sub23', 'sub12', 'sub20', 'sub11', 'sub01', 'sub07']
# 小样本组(3-6个样本)
SMALL_SUBJECTS_5CLASS = ['sub24', 'sub25', 'sub03', 'sub04', 'sub06', 'sub13']
# 极小样本组(<=3个样本)
VERY_SMALL_SUBJECTS_5CLASS = ['sub08', 'sub15', 'sub16', 'sub18', 'sub21', 'sub22']

# 3分类的分组（基于有效样本数）
# 大样本组(>12个样本)
LARGE_SUBJECTS_3CLASS = ['sub17']
# 中等样本组(7-12个样本)
MEDIUM_SUBJECTS_3CLASS = ['sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub23', 'sub12']
# 小样本组(3-6个样本)
SMALL_SUBJECTS_3CLASS = ['sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub06', 'sub15']
# 极小样本组(<=2个样本)
VERY_SMALL_SUBJECTS_3CLASS = ['sub20', 'sub04', 'sub13', 'sub08', 'sub16', 'sub21', 'sub22']

# 3分类中的无效受试者（只包含其他类别样本）
INVALID_SUBJECTS_3CLASS = ['sub10', 'sub18']

# 3分类的训练顺序（排除无效受试者）
TRAINING_ORDER_3CLASS = LARGE_SUBJECTS_3CLASS + MEDIUM_SUBJECTS_3CLASS + SMALL_SUBJECTS_3CLASS + VERY_SMALL_SUBJECTS_3CLASS

# 为了向后兼容，保留原始变量名
CASME2_numbers = CASME2_5class_numbers
LARGE_SUBJECTS = LARGE_SUBJECTS_5CLASS
MEDIUM_SUBJECTS = MEDIUM_SUBJECTS_5CLASS
SMALL_SUBJECTS = SMALL_SUBJECTS_5CLASS
VERY_SMALL_SUBJECTS = VERY_SMALL_SUBJECTS_5CLASS

# 按样本量从大到小的训练顺序
TRAINING_ORDER = LARGE_SUBJECTS + MEDIUM_SUBJECTS + SMALL_SUBJECTS + VERY_SMALL_SUBJECTS

def reset_weights(m):  # 重置网络权重,避免权重泄露
    for layer in m.children():
        if hasattr(layer, 'reset_parameters'):
            layer.reset_parameters()


def confusionMatrix(gt, pred, show=False):
    """
    计算二分类混淆矩阵相关指标
    参数:
        gt: 真实标签（二分类：0或1）
        pred: 预测标签（二分类：0或1）
        show: 是否显示结果
    返回:
        f1_score: F1分数
        average_recall: 平均召回率（召回率）
    """
    try:
        cm = confusion_matrix(gt, pred)

        # 处理不同形状的混淆矩阵
        if cm.shape == (2, 2):
            TN, FP, FN, TP = cm.ravel()
        elif cm.shape == (1, 1):
            # 只有一个类别的情况
            if gt.count(1) == 0:  # 只有负样本
                TN = cm[0, 0]
                FP, FN, TP = 0, 0, 0
            else:  # 只有正样本
                TP = cm[0, 0]
                FP, FN, TN = 0, 0, 0
        else:
            print(f"警告: 混淆矩阵形状异常: {cm.shape}")
            return 0.0, 0.0

        # 计算精确率、召回率和F1分数
        precision = TP / (TP + FP) if (TP + FP) > 0 else 0.0
        recall = TP / (TP + FN) if (TP + FN) > 0 else 0.0

        if (precision + recall) > 0:
            f1_score = 2 * (precision * recall) / (precision + recall)
        else:
            f1_score = 0.0

        if show:
            print(f"TP: {TP}, FP: {FP}, FN: {FN}, TN: {TN}")
            print(f"精确率: {precision:.4f}, 召回率: {recall:.4f}, F1分数: {f1_score:.4f}")

        return f1_score, recall

    except Exception as e:
        print(f"混淆矩阵计算出错: {str(e)}")
        return 0.0, 0.0


def normalize_gray(images):
    """
    归一化灰度图像到[0,1]范围
    """
    images = cv2.normalize(images, None, alpha=0, beta=1, norm_type=cv2.NORM_MINMAX, dtype=cv2.CV_32F)
    return images


def recognition_evaluation(dataset, final_gt, final_pred, show=False, class_num=5):
    """
    评估表情识别结果
    参数:
        dataset: 数据集名称
        final_gt: 真实标签列表
        final_pred: 预测标签列表
        show: 是否显示详细结果
        class_num: 分类数量(3或5)
    返回:
        UF1: 未加权F1分数
        UAR: 未加权平均召回率
    """
    try:
        # 检查输入数据有效性
        if not final_gt or not final_pred:
            print("错误: 真实标签或预测标签为空")
            return 0.0, 0.0

        if len(final_gt) != len(final_pred):
            print(f"错误: 标签长度不匹配 (真实: {len(final_gt)}, 预测: {len(final_pred)})")
            return 0.0, 0.0

        # 根据分类数选择标签字典
        if class_num == 3:
            label_dict = {'positive': 0, 'negative': 1, 'surprise': 2}
            emotion_names = ['正性(positive)', '负性(negative)', '惊讶(surprise)']
        else:
            label_dict = {'happiness': 0, 'surprise': 1, 'disgust': 2, 'repression': 3, 'others': 4}
            emotion_names = ['快乐(happiness)', '惊讶(surprise)', '厌恶(disgust)',
                            '压抑(repression)', '其他(others)']
        
        f1_list = []  # 存储每个类别的F1分数
        ar_list = []  # 存储每个类别的召回率
        
        # 对每个表情类别分别计算
        for emotion, emotion_index in label_dict.items():
            try:
                # 转换为二分类问题
                gt_recog = [1 if x == emotion_index else 0 for x in final_gt]
                pred_recog = [1 if x == emotion_index else 0 for x in final_pred]
                
                # 计算该类别的样本数
                num_samples = gt_recog.count(1)
                if num_samples == 0:
                    if show:
                        print(f"{emotion_names[emotion_index]}: 没有样本")
                    continue
                
                # 计算混淆矩阵
                try:
                    cm = confusion_matrix(gt_recog, pred_recog)
                    
                    # 确保混淆矩阵是2x2的
                    if cm.shape != (2, 2):
                        if cm.shape == (1, 1):
                            if gt_recog.count(1) == 0:  # 如果没有正样本
                                TN = cm[0, 0]
                                FP, FN, TP = 0, 0, 0
                            else:  # 如果只有正样本
                                TP = cm[0, 0]
                                FP, FN, TN = 0, 0, 0
                        else:
                            print(f"警告: {emotion}类别的混淆矩阵形状异常: {cm.shape}")
                            continue
                    else:
                        TN, FP, FN, TP = cm.ravel()
                        
                except ValueError as e:
                    print(f"{emotion_names[emotion_index]}类别计算出错: {str(e)}")
                    print(f"标签分布 - 真实: {gt_recog.count(1)}/{len(gt_recog)}, "
                          f"预测: {pred_recog.count(1)}/{len(pred_recog)}")
                    continue
                
                # 计算精确率、召回率和F1分数
                precision = TP / (TP + FP) if (TP + FP) > 0 else 0.0
                recall = TP / (TP + FN) if (TP + FN) > 0 else 0.0

                # F1分数计算（调和平均数）
                if (precision + recall) > 0:
                    f1_recog = 2 * (precision * recall) / (precision + recall)
                else:
                    f1_recog = 0.0

                # 召回率（与上面计算的recall相同，但为了清晰性保留）
                ar_recog = recall
                
                # 存储指标
                f1_list.append(f1_recog)
                ar_list.append(ar_recog)
                
                if show:
                    print(f"\n{emotion_names[emotion_index]}评估结果:")
                    print(f"样本数: {num_samples}")
                    print(f"TP: {TP}, FP: {FP}, FN: {FN}, TN: {TN}")
                    print(f"精确率: {precision:.4f}, 召回率: {recall:.4f}, F1分数: {f1_recog:.4f}")
                    
            except Exception as e:
                print(f"{emotion_names[emotion_index]}类别计算出错: {str(e)}")
                continue
                
        # 计算未加权平均值
        if len(f1_list) > 0 and len(ar_list) > 0:
            UF1 = float(np.mean(f1_list))
            UAR = float(np.mean(ar_list))
            
            if show:
                print("\n总体评估结果:")
                print(f"UF1: {UF1:.4f}")
                print(f"UAR: {UAR:.4f}")
                
            return UF1, UAR
        else:
            print("没有有效的评估结果")
            return 0.0, 0.0
            
    except Exception as e:
        print(f"评估过程出错: {str(e)}")
        print("GT标签分布:", np.unique(final_gt, return_counts=True))
        print("Pred标签分布:", np.unique(final_pred, return_counts=True))
        return 0.0, 0.0


def extract_prefix(file_name):
    """
    从文件名中提取前缀
    """
    prefixes = ["_1_u", "_2_u", "_1_v", "_2_v", "_apex"]
    for prefix in prefixes:
        if prefix in file_name:
            return file_name.split(prefix)[0]
    return None


def get_folder_all_cases(folder_path):
    """
    获取文件夹中所有样本的唯一前缀
    """
    unique_prefixes = set()
    for file_name in os.listdir(folder_path):
        if file_name.lower().endswith(".jpg"):
            prefix = extract_prefix(file_name)
            if prefix is not None:
                unique_prefixes.add(prefix)
    unique_prefixes = list(unique_prefixes)
    unique_prefixes.sort()

    return unique_prefixes


class FocalLoss(nn.Module):
    '''多类别Focal Loss实现'''

    def __init__(self, gamma=2, weight=None):
        super(FocalLoss, self).__init__()
        self.gamma = gamma
        self.weight = weight

    def forward(self, input, target):
        """
        参数:
            input: [N, C] 预测输出
            target: [N, ] 真实标签
        """
        logpt = F.log_softmax(input, dim=1)
        pt = torch.exp(logpt)
        logpt = (1 - pt) ** self.gamma * logpt
        loss = F.nll_loss(logpt, target, self.weight)
        return loss


def get_loss_function(loss_name, weight=None):
    """
    获取损失函数
    """
    if loss_name == "CELoss":
        return nn.CrossEntropyLoss()
    elif loss_name == "FocalLoss":
        return FocalLoss()
    elif loss_name == "FocalLoss_weighted":
        return FocalLoss(weight=weight)


class Logger(object):
    """
    日志记录器,同时输出到终端和文件
    """
    def __init__(self, filename):
        self.terminal = sys.stdout
        self.filename = filename
        self.closed = False

        try:
            # 确保日志文件目录存在
            log_dir = os.path.dirname(filename)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
                print(f'创建日志目录: {log_dir}')

            # 打开日志文件
            self.log = open(filename, "a", encoding='utf-8')
            print(f'日志文件已创建: {filename}')

        except Exception as e:
            print(f'日志文件创建失败: {str(e)}')
            print(f'日志路径: {filename}')
            self.log = None

    def write(self, message):
        try:
            if not self.closed:
                # 写入终端
                self.terminal.write(message)
                self.terminal.flush()

                # 写入日志文件
                if self.log is not None:
                    self.log.write(message)
                    self.log.flush()
        except Exception as e:
            # 如果写入失败，至少保证终端输出
            try:
                self.terminal.write(message)
                self.terminal.flush()
            except:
                pass

    def flush(self):
        try:
            if not self.closed:
                self.terminal.flush()
                if self.log is not None:
                    self.log.flush()
        except:
            pass

    def close(self):
        if not self.closed:
            try:
                if self.log is not None:
                    self.log.close()
                    print(f'日志文件已关闭: {self.filename}')
                self.closed = True
            except Exception as e:
                print(f'关闭日志文件时出错: {str(e)}')


def get_subject_parameters(subject_id, class_num=5):
    """
    根据分类数和受试者ID获取相关参数
    参数:
        subject_id: 受试者ID
        class_num: 分类数(3或5)
    返回:
        temp_adjust: 温度调整系数
        sample_factor: 样本因子
        beta_factor: beta调整系数
    """
    if class_num == 3:
        if subject_id in LARGE_SUBJECTS_3CLASS:
            return 0.8, 0.8, 0.8  # 大样本组
        elif subject_id in MEDIUM_SUBJECTS_3CLASS:
            return 1.0, 1.0, 1.0  # 中等样本组
        elif subject_id in SMALL_SUBJECTS_3CLASS:
            return 1.2, 1.2, 1.2  # 小样本组
        else:
            return 1.5, 1.5, 1.5  # 极小样本组
    else:  # 5分类
        if subject_id in LARGE_SUBJECTS_5CLASS:
            return 0.8, 0.8, 0.8  # 大样本组
        elif subject_id in MEDIUM_SUBJECTS_5CLASS:
            return 1.0, 1.0, 1.0  # 中等样本组
        elif subject_id in SMALL_SUBJECTS_5CLASS:
            return 1.2, 1.2, 1.2  # 小样本组
        else:
            return 1.5, 1.5, 1.5  # 极小样本组

def get_subject_temperature(subject_id, class_num=5):
    """
    基于subject特征确定温度参数
    """
    # 基础温度
    base_temp = 3.0

    # 获取温度调整系数
    temp_adjust, _, _ = get_subject_parameters(subject_id, class_num)

    return base_temp * temp_adjust

def get_subject_alpha(subject_id, current_iter, max_iter, class_num=5):
    """
    基于subject特征和训练进度动态调整alpha值
    """
    # 基础alpha值
    base_alpha = 0.1

    # 训练进度因子(随训练进行逐渐降低alpha)
    progress_factor = 1 - (current_iter / max_iter)

    # 获取样本因子
    _, sample_factor, _ = get_subject_parameters(subject_id, class_num)

    return base_alpha * progress_factor * sample_factor

def get_subject_beta(subject_id, class_num=5):
    """
    基于subject特征动态调整beta值(特征损失权重)
    """
    # 基础beta值
    base_beta = 1e-6

    # 获取beta因子
    _, _, beta_factor = get_subject_parameters(subject_id, class_num)

    return base_beta * beta_factor

def new_kd_loss_function(output, target_output, current_iter, max_iter, subject_id, class_num=5):
    """
    自适应知识蒸馏损失
    """
    # 获取subject自适应的温度参数
    T = get_subject_temperature(subject_id, class_num)

    # 计算软目标的KL散度loss
    output = output / T
    output_log_softmax = torch.log_softmax(output, dim=1)
    loss_kd = nn.KLDivLoss(reduction="batchmean")(output_log_softmax,
                                                 torch.softmax(target_output/T, dim=1))

    # 根据温度参数调整loss
    loss_kd = loss_kd * (T * T)

    return loss_kd

def feature_loss_function(fea, target_fea, current_iter, max_iter, subject_id, class_num=5):
    """
    自适应特征损失
    """
    # 获取subject自适应的beta值
    beta = get_subject_beta(subject_id, class_num)

    # 计算特征损失
    loss = (fea - target_fea) ** 2 * ((fea > 0) | (target_fea > 0)).float()

    # 应用beta权重
    return beta * torch.abs(loss).sum()


def augment_image(image, angle=None, do_mirror=False):
    """
    对图像进行数据扩充
    参数:
        image: 输入图像
        angle: 旋转角度,如果为None则不旋转
        do_mirror: 是否进行镜像
    返回:
        扩充后的图像
    """
    h, w = image.shape[:2]
    if angle is not None:
        # 计算旋转矩阵
        M = cv2.getRotationMatrix2D((w/2, h/2), angle, 1.0)
        # 执行旋转
        image = cv2.warpAffine(image, M, (w, h), borderMode=cv2.BORDER_REPLICATE)
    
    if do_mirror:
        # 执行镜像
        image = cv2.flip(image, 1)
    
    return image


def augment_flow(flow_dict, angle=None, do_mirror=False):
    """
    对光流数据进行扩充，包括旋转和镜像
    参数:
        flow_dict: 包含'u'和'v'分量的光流字典
        angle: 旋转角度(度)，如果为None则不旋转
        do_mirror: 是否进行镜像
    返回:
        扩充后的光流字典
    """
    u = flow_dict['u'].copy()
    v = flow_dict['v'].copy()
    
    # 处理旋转 - 光流向量需要跟随图像旋转
    if angle is not None:
        # 将角度转换为弧度
        theta = np.deg2rad(angle)
        # 光流向量旋转变换矩阵
        cos_theta = np.cos(theta)
        sin_theta = np.sin(theta)
        
        # 对每个像素的光流向量进行旋转
        u_rot = u * cos_theta - v * sin_theta
        v_rot = u * sin_theta + v * cos_theta
        
        u = u_rot
        v = v_rot
    
    # 处理镜像 - 水平镜像时u分量需要取反
    if do_mirror:
        u = -cv2.flip(u, 1)  # 水平分量取反
        v = cv2.flip(v, 1)   # 垂直分量保持方向
    
    return {'u': u, 'v': v}


def get_augmentation_strategy(multiplier):
    """
    生成数据增强策略，最大支持8倍扩增
    参数:
        multiplier: 需要的扩增倍数(1-8)
    返回:
        增强策略列表，每个元素是(use_rotation, angle_sign, use_mirror)的元组
    """
    # 基础策略组合（按优先级排序）：
    base_strategies = [
        (False, 0, False),     # 1. 原始数据
        (False, 0, True),      # 2. 只镜像
        (True, 1, False),      # 3. 小角度正向旋转
        (True, -1, False),     # 4. 小角度负向旋转
        (True, 1, True),       # 5. 小角度正向旋转+镜像
        (True, -1, True),      # 6. 小角度负向旋转+镜像
        (True, 2, False),      # 7. 大角度正向旋转
        (True, -2, False),     # 8. 大角度负向旋转
        (True, 2, True),       # 9. 大角度正向旋转+镜像
        (True, -2, True),      # 10. 大角度负向旋转+镜像
    ]
    
    # 确保multiplier不超过最大值
    multiplier = min(multiplier, len(base_strategies) + 1)
    
    # 返回策略列表（不包括原始数据的策略）
    return base_strategies[:multiplier-1]


def augment_sample(images, flow_dicts, min_angle=3, max_angle=8, use_rotation=False, angle_sign=1, do_mirror=False):
    """
    对样本进行数据扩充
    参数:
        images: 图像列表
        flow_dicts: 光流数据字典列表
        min_angle: 最小旋转角度
        max_angle: 最大旋转角度
        use_rotation: 是否使用旋转
        angle_sign: 角度系数(1:小角度正向, -1:小角度负向, 2:大角度正向, -2:大角度负向)
        do_mirror: 是否进行镜像
    返回:
        扩充后的图像和光流数据
    """
    # 根据angle_sign确定实际旋转角度
    angle = None
    if use_rotation:
        if abs(angle_sign) == 1:  # 小角度旋转
            base_angle = random.uniform(min_angle, (min_angle + max_angle)/2)
        else:  # 大角度旋转
            base_angle = random.uniform((min_angle + max_angle)/2, max_angle)
        angle = base_angle * (1 if angle_sign > 0 else -1)
    
    # 扩充图像
    aug_images = [augment_image(img, angle, do_mirror) for img in images]
    
    # 扩充光流数据，传入相同的角度确保一致性
    aug_flows = [augment_flow(flow, angle, do_mirror) for flow in flow_dicts]
    
    return aug_images, aug_flows


def process_sample(large_S, large_S_onset, flow_1, flow_2):
    """
    处理单个样本的图像和光流数据
    """
    end_input = []
    
    # 处理主图像
    small_S = cv2.resize(large_S, (48, 48))
    small_S_onset = cv2.resize(large_S_onset, (48, 48))
    end_input.extend([small_S, small_S_onset])
    
    # 处理网格图像
    grid_sizes = [4]
    for grid_size in grid_sizes:
        height, width = large_S.shape
        block_height, block_width = height // grid_size, width // grid_size
        
        for i in range(grid_size):
            for j in range(grid_size):
                block = large_S[i * block_height: (i + 1) * block_height,
                        j * block_width: (j + 1) * block_width]
                scaled_block = cv2.resize(block, (48, 48))
                end_input.append(scaled_block)
        
        for i in range(grid_size):
            for j in range(grid_size):
                block = large_S_onset[i * block_height: (i + 1) * block_height,
                        j * block_width: (j + 1) * block_width]
                scaled_block = cv2.resize(block, (48, 48))
                end_input.append(scaled_block)
    
    # 处理光流数据
    flow_1_u = cv2.resize(flow_1['u'], (48, 48))
    flow_1_u = normalize_gray(flow_1_u)
    flow_1_v = cv2.resize(flow_1['v'], (48, 48))
    flow_1_v = normalize_gray(flow_1_v)
    end_input.extend([flow_1_u, flow_1_v])
    
    flow_2_u = cv2.resize(flow_2['u'], (48, 48))
    flow_2_u = normalize_gray(flow_2_u)
    flow_2_v = cv2.resize(flow_2['v'], (48, 48))
    flow_2_v = normalize_gray(flow_2_v)
    end_input.extend([flow_2_u, flow_2_v])
    
    return np.stack(end_input, axis=-1)


def voting_predict(predictions, confidences, method='weighted'):
    """
    对多个预测结果进行投票
    参数:
        predictions: 预测标签列表
        confidences: 预测置信度列表
        method: 投票方式 ('majority' 或 'weighted')
    返回:
        最终预测标签
    """
    if method == 'majority':
        # 简单多数投票
        return max(set(predictions), key=predictions.count)
    else:
        # 加权投票
        vote_dict = {}
        for pred, conf in zip(predictions, confidences):
            if pred not in vote_dict:
                vote_dict[pred] = 0
            vote_dict[pred] += conf
        return max(vote_dict.items(), key=lambda x: x[1])[0]


class VisdomServer:
    def __init__(self, port=8097, timeout=60, open_browser=True):
        self.port = port
        self.process = None
        self.timeout = timeout
        self.is_available = False
        self.open_browser = open_browser
        
    def is_port_in_use(self):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                return s.connect_ex(('localhost', self.port)) == 0
        except:
            return False
            
    def wait_for_server(self, timeout=None):
        if timeout is None:
            timeout = self.timeout
            
        print(f"等待Visdom服务器启动(最多{timeout}秒)...")
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self.is_port_in_use():
                return True
            time.sleep(1)
        return False
        
    def start(self):
        try:
            if self.is_port_in_use():
                print(f"Visdom服务器已在端口 {self.port} 运行")
                self.is_available = True
                # 尝试打开浏览器
                if self.open_browser:
                    try:
                        threading.Timer(2.0, lambda: webbrowser.open(f'http://localhost:{self.port}')).start()
                    except:
                        print("无法自动打开浏览器,请手动访问 http://localhost:{self.port}")
                return True
                
            # 使用subprocess启动visdom服务器
            print(f"正在启动Visdom服务器(端口:{self.port})...")
            self.process = subprocess.Popen(
                ['python', '-m', 'visdom.server', '-port', str(self.port)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            # 等待服务器启动
            if self.wait_for_server():
                print(f"Visdom服务器已成功启动在端口 {self.port}")
                self.is_available = True
                # 在新线程中打开浏览器
                if self.open_browser:
                    try:
                        threading.Timer(2.0, lambda: webbrowser.open(f'http://localhost:{self.port}')).start()
                    except:
                        print("无法自动打开浏览器,请手动访问 http://localhost:{self.port}")
                return True
            else:
                print("Visdom服务器启动超时,将继续训练但不使用可视化功能")
                if self.process:
                    self.process.terminate()
                    self.process = None
                return False
        except Exception as e:
            print(f"启动Visdom服务器时出错: {str(e)}")
            print("将继续训练但不使用可视化功能")
            return False
            
    def stop(self):
        if self.process:
            try:
                self.process.terminate()
            except:
                pass
            self.process = None


def main_SKD_TSTSAN_with_Aug_with_SKD(config):
    """
    主训练函数
    返回:
        results_dict: 包含训练结果的字典
    """
    print("\n" + "="*80)
    print("【训练开始】")
    print("="*80)
    
    learning_rate = config.learning_rate
    base_batch_size = config.batch_size

    # 初始化结果字典
    results_dict = {
        'status': 'success',
        'subject_results': {},
        'emotion_acc': {},
        'UF1': None,
        'UAR': None
    }

    # 设置随机种子以确保可重复性
    seed = config.seed
    random.seed(seed)
    np.random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)

    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

    cudnn.benchmark = False
    cudnn.deterministic = True

    # 检查是否可用GPU并配置多GPU训练
    is_cuda = torch.cuda.is_available()
    gpu_count = 0

    if is_cuda:
        # 获取可用的GPU数量
        gpu_count = torch.cuda.device_count()
        print(f'\n【硬件信息】检测到{gpu_count}个GPU')

        # 显示所有GPU信息
        for gpu_id in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(gpu_id)
            gpu_memory = torch.cuda.get_device_properties(gpu_id).total_memory / 1024**3
            print(f'  GPU {gpu_id}: {gpu_name} ({gpu_memory:.1f}GB)')

        # 配置多GPU训练
        if gpu_count > 1:
            print(f'\n【多GPU配置】将使用所有{gpu_count}个GPU进行并行训练')
            # 根据GPU数量调整批次大小
            batch_size = base_batch_size * gpu_count
            print(f'  - 基础批次大小: {base_batch_size}')
            print(f'  - 多GPU调整后批次大小: {batch_size} ({base_batch_size} × {gpu_count})')

            # 设置所有GPU的内存使用限制
            for gpu_id in range(gpu_count):
                try:
                    # 设置最大内存使用限制为90%（多GPU时更保守）
                    torch.cuda.set_per_process_memory_fraction(0.90, gpu_id)
                    print(f'  - GPU {gpu_id}: 内存限制设置为90%')
                except Exception as e:
                    print(f'  - GPU {gpu_id}: 内存设置失败 - {str(e)}')

            # 清空所有GPU缓存
            torch.cuda.empty_cache()
            # 设置主GPU
            torch.cuda.set_device(0)
            print(f'  - 主GPU设置为: GPU 0')

        else:
            print('\n【单GPU配置】使用单个GPU进行训练')
            batch_size = base_batch_size
            # 单GPU时可以使用更多内存
            torch.cuda.set_per_process_memory_fraction(0.95, 0)
            torch.cuda.empty_cache()
            print(f'  - 批次大小: {batch_size}')
            print(f'  - GPU内存限制: 95%')

        device = torch.device('cuda')
        print(f'  - 主设备: {device}')

    else:
        device = torch.device('cpu')
        batch_size = base_batch_size
        print('\n【硬件信息】使用CPU进行训练')
        print(f'  - 批次大小: {batch_size}')

    # 性能优化设置
    if is_cuda:
        # 基础CUDA优化 - 使用配置参数
        torch.backends.cudnn.benchmark = config.cudnn_benchmark
        torch.backends.cudnn.deterministic = config.cudnn_deterministic
        # 设置GPU内存分配策略
        torch.cuda.set_per_process_memory_fraction(0.95)  # 使用95%的GPU内存
        torch.cuda.empty_cache()  # 清空GPU缓存

        # TF32加速设置（适用于Ampere架构GPU）
        if config.use_tf32:
            try:
                torch.backends.cuda.matmul.allow_tf32 = True
                torch.backends.cudnn.allow_tf32 = True
                print("✓ TF32加速已启用")
            except AttributeError:
                print("⚠ 当前PyTorch版本不支持TF32设置")
        else:
            torch.backends.cuda.matmul.allow_tf32 = False
            torch.backends.cudnn.allow_tf32 = False
            print("✗ TF32加速已禁用")

    # 混合精度训练设置
    use_mixed_precision = config.use_mixed_precision and is_cuda
    if use_mixed_precision:
        scaler = GradScaler()
        print("✓ 混合精度训练已启用")
    else:
        scaler = None
        print("✗ 混合精度训练已禁用")

    # BatchNorm修复设置
    use_batchnorm_fix = getattr(config, 'use_batchnorm_fix', False)
    if use_batchnorm_fix:
        if BATCHNORM_FIX_AVAILABLE:
            print("✓ BatchNorm修复已启用 (适用于小批次训练)")
        else:
            print("⚠️ BatchNorm修复已启用但模块不可用")
    else:
        print("✗ BatchNorm修复已禁用 (使用原始BatchNorm层)")

    # 根据配置选择损失函数
    if config.loss_function == "FocalLoss_weighted":
        # 根据分类数选择对应的样本数量
        if config.class_num == 3:
            numbers = CASME2_3class_numbers
        else:
            numbers = CASME2_5class_numbers

        sum_reciprocal = sum(1 / num for num in numbers)
        weights = [(1 / num) / sum_reciprocal for num in numbers]
        print_section("损失函数配置", Colors.BRIGHT_MAGENTA, "📊")
        print_success(f"使用加权Focal Loss ({config.class_num}分类)", "⚖️")
        print_info(f"类别权重: {[f'{w:.4f}' for w in weights]}", "🎯")

        loss_fn = get_loss_function(config.loss_function, torch.tensor(weights).to(device))
    else:
        loss_fn = get_loss_function(config.loss_function)
        print_section("损失函数配置", Colors.BRIGHT_MAGENTA, "📊")
        print_success(f"使用{config.loss_function} ({config.class_num}分类)", "⚖️")

    # 复制相关代码文件到实验目录
    current_file = os.path.abspath(__file__)
    shutil.copy(current_file, './Experiment_for_recognize/' + config.exp_name)
    shutil.copy("./all_model.py", './Experiment_for_recognize/' + config.exp_name)

    print('开始训练...')

    total_gt = []
    total_pred = []
    best_total_pred = []
    all_accuracy_dict = {}

    t = time.time()

    main_path = config.main_path
    
    # 在主循环开始前初始化统计
    aug_stats = {i: 0 for i in range(config.class_num)}
    
    # 初始化Visdom服务器
    visdom_available = False
    viz = None
    visdom_server = None
    
    if getattr(config, 'use_visdom', True):
        try:
            visdom_server = VisdomServer(
                port=getattr(config, 'visdom_port', 8097),
                timeout=getattr(config, 'visdom_timeout', 60),
                open_browser=getattr(config, 'open_browser', True)
            )
            visdom_available = visdom_server.start()
            
            # 创建Visdom环境(如果可用)
            if visdom_available:
                try:
                    viz = visdom.Visdom(env=config.exp_name)
                    print(f"Visdom环境 '{config.exp_name}' 已创建")
                except Exception as e:
                    print(f"创建Visdom环境时出错: {str(e)}")
                    print("将继续训练但不使用可视化功能")
                    visdom_available = False
        except Exception as e:
            print(f"Visdom初始化出错: {str(e)}")
            print("将继续训练但不使用可视化功能")
    else:
        print("根据配置,不使用Visdom可视化")

    try:
        # 根据分类数量选择训练顺序
        if config.class_num == 3:
            training_order = TRAINING_ORDER_3CLASS
        else:
            training_order = TRAINING_ORDER

        # 按照预定义的顺序处理主体
        for n_subName in training_order:
            if not os.path.exists(os.path.join(main_path, n_subName)):
                print(f'警告: 找不到主体 {n_subName} 的数据目录,跳过')
                continue

            # 3分类模式下检查无效受试者
            if config.class_num == 3 and n_subName in INVALID_SUBJECTS_3CLASS:
                print(f"\n{'='*40}")
                print(f"【跳过无效受试者】: {n_subName}")
                print(f"{'='*40}")
                print(f"原因: 该受试者在3分类模式下只包含'其他'类别样本")
                print("跳过处理，继续下一个受试者...")
                continue

            print(f"\n{'='*40}")
            print(f"【当前处理受试者】: {n_subName}")
            print(f"{'='*40}")

            # 确定当前主体所属的组
            group_info = get_subject_group_info(n_subName, config.class_num)
            print(f"组别: {group_info}")

            X_train = []
            y_train = []

            X_test = []
            y_test = []

            # 加载训练数据
            print('正在加载训练数据...')
            expression = os.listdir(main_path + '/' + n_subName + '/train')
            for n_expression in expression:
                case_list = get_folder_all_cases(main_path + '/' + n_subName + '/train/' + n_expression)
                original_label = int(n_expression)

                # 如果是3分类模式，需要转换标签并过滤"其他"类别
                if config.class_num == 3:
                    converted_label = convert_5class_to_3class(original_label)
                    if converted_label is None:  # 过滤掉"其他"类别
                        print(f'跳过"其他"类别数据: {n_expression}')
                        continue
                    label = converted_label
                else:
                    label = original_label

                # 获取当前标签的扩充倍数
                if config.use_data_augmentation:
                    multipliers = [int(x) for x in config.aug_multipliers.split(',')]
                    if label >= len(multipliers):
                        print(f'警告: 标签 {label} 超出增强倍数范围，使用默认倍数1')
                        current_multiplier = 1
                    else:
                        current_multiplier = multipliers[label]
                    min_angle, max_angle = map(float, config.aug_rotation_range.split(','))
                else:
                    current_multiplier = 1
                    
                for case in case_list:
                    # 原始数据处理
                    original_images = []
                    original_flows = []
                    
                    # 读取原始图像
                    large_S = normalize_gray(cv2.imread(main_path + '/' + n_subName + '/train/' + n_expression + '/' + case + "_apex.jpg", 0))
                    large_S_onset = normalize_gray(cv2.imread(main_path + '/' + n_subName + '/train/' + n_expression + '/' + case + "_onset.jpg", 0))
                    original_images.extend([large_S, large_S_onset])
                    
                    # 读取光流数据
                    flow_1 = np.load(main_path + '/' + n_subName + '/train/' + n_expression + '/' + case + "_1.npy", allow_pickle=True).item()
                    flow_2 = np.load(main_path + '/' + n_subName + '/train/' + n_expression + '/' + case + "_2.npy", allow_pickle=True).item()
                    original_flows.extend([flow_1, flow_2])
                    
                    # 处理原始样本
                    end_input = process_sample(large_S, large_S_onset, flow_1, flow_2)
                    X_train.append(end_input)
                    y_train.append(label)
                    
                    # 数据扩充
                    if config.use_data_augmentation and current_multiplier > 1:
                        # 获取当前标签的增强策略
                        aug_strategies = get_augmentation_strategy(current_multiplier)
                        min_angle, max_angle = map(float, config.aug_rotation_range.split(','))
                        
                        # 更新统计信息
                        aug_stats[label] += len(aug_strategies)  # 记录实际增强的样本数
                        
                        for use_rotation, angle_sign, use_mirror in aug_strategies:
                            # 根据策略进行数据增强
                            do_mirror = use_mirror and config.aug_use_mirror
                            
                            # 扩充原始图像和光流数据
                            aug_images, aug_flows = augment_sample(
                                original_images, 
                                original_flows,
                                min_angle=min_angle,
                                max_angle=max_angle,
                                use_rotation=use_rotation,
                                angle_sign=angle_sign,
                                do_mirror=do_mirror
                            )
                            
                            # 处理扩充后的数据
                            end_input = process_sample(aug_images[0], aug_images[1], aug_flows[0], aug_flows[1])
                            X_train.append(end_input)
                            y_train.append(label)

            # 加载测试数据
            print('正在加载测试数据...')
            expression = os.listdir(main_path + '/' + n_subName + '/test')
            original_test_labels = []  # 用于存储原始测试标签
            original_test_indices = []  # 用于追踪原始样本的索引
            current_orig_idx = 0  # 当前原始样本的索引
            
            for n_expression in expression:
                case_list = get_folder_all_cases(main_path + '/' + n_subName + '/test/' + n_expression)
                original_label = int(n_expression)

                # 如果是3分类模式，需要转换标签并过滤"其他"类别
                if config.class_num == 3:
                    converted_label = convert_5class_to_3class(original_label)
                    if converted_label is None:  # 过滤掉"其他"类别
                        print(f'跳过"其他"类别测试数据: {n_expression}')
                        continue
                    label = converted_label
                else:
                    label = original_label

                # 获取当前标签的扩充倍数
                if config.use_test_augmentation:
                    multipliers = [int(x) for x in config.test_aug_multipliers.split(',')]
                    if label >= len(multipliers):
                        print(f'警告: 测试标签 {label} 超出增强倍数范围，使用默认倍数1')
                        current_multiplier = 1
                    else:
                        current_multiplier = multipliers[label]
                    min_angle, max_angle = map(float, config.aug_rotation_range.split(','))
                else:
                    current_multiplier = 1

                for case in case_list:
                    original_test_labels.append(label)  # 记录原始标签
                    original_test_indices.append(current_orig_idx)  # 记录原始样本索引
                    
                    # 原始数据处理
                    original_images = []
                    original_flows = []
                    
                    # 读取原始图像
                    large_S = normalize_gray(cv2.imread(main_path + '/' + n_subName + '/test/' + n_expression + '/' + case + "_apex.jpg", 0))
                    large_S_onset = normalize_gray(cv2.imread(main_path + '/' + n_subName + '/test/' + n_expression + '/' + case + "_onset.jpg", 0))
                    original_images.extend([large_S, large_S_onset])
                    
                    # 读取光流数据
                    flow_1 = np.load(main_path + '/' + n_subName + '/test/' + n_expression + '/' + case + "_1.npy", allow_pickle=True).item()
                    flow_2 = np.load(main_path + '/' + n_subName + '/test/' + n_expression + '/' + case + "_2.npy", allow_pickle=True).item()
                    original_flows.extend([flow_1, flow_2])
                    
                    # 处理原始样本
                    end_input = process_sample(large_S, large_S_onset, flow_1, flow_2)
                    X_test.append(end_input)
                    y_test.append(label)
                    
                    # 数据扩充
                    if config.use_test_augmentation and current_multiplier > 1:
                        # 获取当前标签的增强策略
                        aug_strategies = get_augmentation_strategy(current_multiplier)
                        
                        for use_rotation, angle_sign, use_mirror in aug_strategies:
                            # 根据策略进行数据增强
                            do_mirror = use_mirror and config.aug_use_mirror
                            
                            # 扩充原始图像和光流数据
                            aug_images, aug_flows = augment_sample(
                                original_images, 
                                original_flows,
                                min_angle=min_angle,
                                max_angle=max_angle,
                                use_rotation=use_rotation,
                                angle_sign=angle_sign,
                                do_mirror=do_mirror
                            )
                            
                            # 处理扩充后的数据
                            end_input = process_sample(aug_images[0], aug_images[1], aug_flows[0], aug_flows[1])
                            X_test.append(end_input)
                            y_test.append(label)
                            original_test_indices.append(current_orig_idx)  # 关联到原始样本
                    
                    current_orig_idx += 1  # 更新原始样本索引

            # 打印原始测试标签
            print(f"\n{n_subName}测试标签: {original_test_labels}")

            # 测试数据镜像训练功能
            if getattr(config, 'use_test_mirror_training', False):
                # 解析需要镜像训练的受试者列表
                mirror_subjects = [s.strip() for s in getattr(config, 'test_mirror_subjects', 'sub02,sub05').split(',')]

                if n_subName in mirror_subjects:
                    print(f"\n【测试数据镜像训练】为 {n_subName} 添加测试数据的镜像版本到训练集")

                    # 记录原始训练数据数量
                    original_train_count = len(X_train)

                    # 遍历测试数据，创建镜像版本并添加到训练集
                    for test_idx in range(len(X_test)):
                        # 获取测试样本的原始数据
                        test_sample = X_test[test_idx]
                        test_label = y_test[test_idx]

                        # 从测试样本中提取各个组件
                        # 输入格式: [apex, onset, 16个网格块(apex), 16个网格块(onset), 2个光流u, 2个光流v]
                        apex_img = test_sample[:, :, 0]  # apex图像
                        onset_img = test_sample[:, :, 1]  # onset图像

                        # 网格块数据
                        grid_apex = test_sample[:, :, 2:18]  # 16个apex网格块
                        grid_onset = test_sample[:, :, 18:34]  # 16个onset网格块

                        # 光流数据
                        flow_u1 = test_sample[:, :, 34]  # 第一个光流u分量
                        flow_v1 = test_sample[:, :, 35]  # 第一个光流v分量
                        flow_u2 = test_sample[:, :, 36]  # 第二个光流u分量
                        flow_v2 = test_sample[:, :, 37]  # 第二个光流v分量

                        # 创建镜像版本
                        # 图像镜像（水平翻转）
                        mirror_apex = cv2.flip(apex_img, 1)
                        mirror_onset = cv2.flip(onset_img, 1)

                        # 网格块镜像
                        mirror_grid_apex = np.array([cv2.flip(grid_apex[:, :, i], 1) for i in range(16)]).transpose(1, 2, 0)
                        mirror_grid_onset = np.array([cv2.flip(grid_onset[:, :, i], 1) for i in range(16)]).transpose(1, 2, 0)

                        # 光流镜像（u分量取反，v分量保持）
                        mirror_flow_u1 = -cv2.flip(flow_u1, 1)  # u分量水平翻转并取反
                        mirror_flow_v1 = cv2.flip(flow_v1, 1)   # v分量只水平翻转
                        mirror_flow_u2 = -cv2.flip(flow_u2, 1)  # u分量水平翻转并取反
                        mirror_flow_v2 = cv2.flip(flow_v2, 1)   # v分量只水平翻转

                        # 重新组装镜像样本
                        mirror_sample = np.stack([
                            mirror_apex, mirror_onset,
                            *[mirror_grid_apex[:, :, i] for i in range(16)],
                            *[mirror_grid_onset[:, :, i] for i in range(16)],
                            mirror_flow_u1, mirror_flow_v1,
                            mirror_flow_u2, mirror_flow_v2
                        ], axis=-1)

                        # 添加到训练集
                        X_train.append(mirror_sample)
                        y_train.append(test_label)

                    added_count = len(X_train) - original_train_count
                    print(f"  ✓ 成功添加 {added_count} 个镜像训练样本")
                    print(f"  ✓ 训练集大小: {original_train_count} → {len(X_train)}")
                    print(f"  ✓ 光流数据已正确处理 (u分量取反，v分量保持)")

            # 3分类模式特殊处理：检查是否有有效样本
            if config.class_num == 3:
                if len(X_test) == 0 or len(y_test) == 0:
                    print(f"\n警告: {n_subName} 在3分类模式下没有有效的测试样本(全部是'其他'类别)")
                    print("此受试者将被跳过，不参与3分类评估")
                    continue
                if len(X_train) == 0 or len(y_train) == 0:
                    print(f"\n警告: {n_subName} 在3分类模式下没有有效的训练样本(全部是'其他'类别)")
                    print("此受试者将被跳过，不参与3分类评估")
                    continue

            # 设置模型权重和日志路径
            weight_path = './Experiment_for_recognize/' + config.exp_name + '/' + n_subName + '/' + n_subName + '.pth'
            log_path = './Experiment_for_recognize/' + config.exp_name + '/' + n_subName + '/' + "logs"

            # 确保受试者目录存在
            subject_dir = './Experiment_for_recognize/' + config.exp_name + '/' + n_subName
            if not os.path.exists(subject_dir):
                os.makedirs(subject_dir)
                print(f'创建受试者目录: {n_subName}')
            
            # 确保日志目录存在
            try:
                if not os.path.exists(log_path):
                    os.makedirs(log_path, exist_ok=True)
                    print(f'创建TensorBoard日志目录: {log_path}')
                else:
                    print(f'TensorBoard日志目录已存在: {log_path}')
            except Exception as e:
                print(f'创建TensorBoard日志目录失败: {str(e)}')
                print(f'目录路径: {log_path}')
                raise

            # 创建TensorBoard写入器
            try:
                writer = SummaryWriter(log_path)
                print(f'TensorBoard写入器创建成功: {log_path}')
            except Exception as e:
                print(f'TensorBoard写入器创建失败: {str(e)}')
                print('将继续训练但不记录TensorBoard日志')
                writer = None

            # 初始化模型
            from conv_selector import ConvolutionSelector
            selector = ConvolutionSelector()
            conv_type = getattr(config, 'conv_type', 1)
            conv_name = selector.get_conv_name(conv_type)

            print_section(f"模型初始化", Colors.BRIGHT_BLUE, "🤖")
            print_info(f"正在初始化 {conv_name} 模型...", "⚙️")
            from all_model import get_model_by_conv_type

            # 获取LEGM配置
            legm_type = getattr(config, 'legm_type', 2)  # 默认使用标准LEGM
            use_legm = legm_type == 2
            use_enhanced_legm = False  # 不再使用增强版LEGM

            # 获取ECA配置
            eca_type = getattr(config, 'eca_type', 1)  # 默认使用标准ECA
            use_eca_enhanced = eca_type == 2

            # 获取小波参数
            wt_type = getattr(config, 'wt_type', 'db4')  # 默认使用db4小波
            wt_levels = getattr(config, 'wt_levels', 1)  # 默认使用1层小波变换

            model = get_model_by_conv_type(
                config.model,
                config.class_num,
                config.Aug_alpha,
                config.conv_type,
                use_legm=use_legm,
                use_eca_enhanced=use_eca_enhanced,
                wt_type=wt_type,
                wt_levels=wt_levels
            ).to(device)

            # 显示选择的卷积类型信息
            conv_descriptions = {
                1: f"{colorize('使用标准卷积层', Colors.CYAN)} 🔧",
                2: f"{colorize('✅ 启用风车形卷积功能:', Colors.BRIGHT_GREEN)}\n  {colorize('- 采用非对称填充策略', Colors.CYAN)} 🎯\n  {colorize('- 更好地对齐目标空间分布特性', Colors.CYAN)} 📍\n  {colorize('- 增强微表情细节特征提取能力', Colors.CYAN)} 🔍",
                3: f"{colorize('✅ 启用WTConv小波变换卷积功能:', Colors.BRIGHT_GREEN)}\n  {colorize(f'- 小波类型: {wt_type} (层数: {wt_levels})', Colors.BRIGHT_CYAN)} 🌊\n  {colorize('- 通过小波变换实现多频响应特征提取', Colors.CYAN)} 🌊\n  {colorize('- 同时捕获微表情的全局结构和局部细节', Colors.CYAN)} 🎭\n  {colorize('- 实现非常大的感受野而不会过度参数化', Colors.CYAN)} 📡\n  {colorize('- 特别适合微表情的频域特征分析', Colors.CYAN)} 📊",
                4: f"{colorize('✅ 启用小波变换感受野注意力卷积功能:', Colors.BRIGHT_GREEN)}\n  {colorize('- 结合小波变换和感受野注意力机制', Colors.CYAN)} 🧠\n  {colorize('- 实现频域-空域联合优化', Colors.CYAN)} ⚡\n  {colorize('- 自适应特征权重分配', Colors.CYAN)} 🎯",
                5: f"{colorize('✅ 启用小波变换风车形感受野注意力卷积功能:', Colors.BRIGHT_GREEN)}\n  {colorize('- 融合三种先进技术：小波变换+风车形卷积+感受野注意力', Colors.CYAN)} 🚀\n  {colorize('- 最强特征提取能力(参数较多)', Colors.YELLOW)} 💪",
                6: f"{colorize('✅ 启用小波变换风车形感受野注意力卷积功能(轻量化):', Colors.BRIGHT_GREEN)}\n  {colorize('- 融合三种先进技术的轻量化版本', Colors.CYAN)} ⚡\n  {colorize('- 大幅减少参数数量，保持核心功能', Colors.CYAN)} 🎯\n  {colorize('- 推荐选择，性能与效率的最佳平衡', Colors.BRIGHT_MAGENTA)} ⭐"
            }

            description = conv_descriptions.get(conv_type, f"{colorize('未知卷积类型', Colors.RED)} ❓")
            print(description)

            # 显示选择的LEGM模块信息
            legm_descriptions = {
                1: f"{colorize('🚫 未启用LEGM模块', Colors.YELLOW)}\n  {colorize('- 使用标准ECA注意力模块', Colors.CYAN)} 🔧\n  {colorize('- 保持原始模型结构', Colors.CYAN)} 📦\n  {colorize('- 最快推理速度，最低内存占用', Colors.CYAN)} ⚡",
                2: f"{colorize('✅ 启用标准LEGM模块:', Colors.BRIGHT_GREEN)}\n  {colorize('- 在conv1后增强初期特征提取', Colors.CYAN)} 🎯\n  {colorize('- 专注于低层纹理和边缘特征捕获', Colors.CYAN)} 🔍\n  {colorize('- 参数增加约6.5%，性能提升显著', Colors.CYAN)} 📈\n  {colorize('- 推荐选择，性能与效率平衡', Colors.BRIGHT_MAGENTA)} ⭐"
            }

            legm_description = legm_descriptions.get(legm_type, f"{colorize('未知LEGM类型', Colors.RED)} ❓")
            print(legm_description)

            # 应用BatchNorm修复（在DataParallel包装之前）
            if getattr(config, 'use_batchnorm_fix', False):
                if BATCHNORM_FIX_AVAILABLE:
                    print_section("BatchNorm修复", Colors.BRIGHT_YELLOW, "🔧")
                    print_info("正在应用BatchNorm修复...", "⚙️")
                    try:
                        # 统计原始归一化层
                        original_counts = count_normalization_layers(model)
                        print_info("原始归一化层统计:", "📊")
                        for layer_type, count in original_counts.items():
                            if count > 0:
                                print(f"  {colorize('- ' + layer_type + ':', Colors.CYAN)} {colorize(str(count), Colors.BRIGHT_YELLOW)}")

                        # 应用自适应BatchNorm方案
                        model = apply_batchnorm_fix(model, method="adaptive")

                        # 统计修复后的归一化层
                        new_counts = count_normalization_layers(model)
                        print_info("修复后归一化层统计:", "📈")
                        for layer_type, count in new_counts.items():
                            if count > 0:
                                print(f"  {colorize('- ' + layer_type + ':', Colors.CYAN)} {colorize(str(count), Colors.BRIGHT_GREEN)}")

                        print_success("BatchNorm修复成功完成", "✅")
                        print(f"  {colorize('- 所有BatchNorm层已被替换为自适应版本', Colors.CYAN)} 🔄")
                        print(f"  {colorize('- 批次大小>1时使用BatchNorm，批次大小=1时使用GroupNorm', Colors.CYAN)} 🎯")
                        print(f"  {colorize('- 现在可以安全处理任何批次大小的情况', Colors.CYAN)} 🛡️")

                    except Exception as e:
                        print(f"⚠️ BatchNorm修复失败: {str(e)}")
                        print("  - 将继续使用原始BatchNorm层，可能在小批次时出现问题")
                else:
                    print("\n⚠️ BatchNorm修复模块不可用，使用原始BatchNorm层")
                    print("  - 请检查batchnorm_fix.py文件是否存在")
                    print("  - 或者设置 --use_batchnorm_fix=False 来禁用此功能")
            else:
                print("\n【BatchNorm修复】已禁用，使用原始BatchNorm层")
                print("  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复")

            # 配置多GPU并行训练
            if is_cuda and gpu_count > 1:
                print(f'\n【DataParallel配置】')
                print(f'  - 将模型包装为DataParallel')
                print(f'  - 并行GPU数量: {gpu_count}')
                print(f'  - GPU设备列表: {list(range(gpu_count))}')

                # 使用DataParallel包装模型，指定所有可用GPU
                model = nn.DataParallel(model, device_ids=list(range(gpu_count)))
                print(f'  - DataParallel包装完成')
                print(f'  - 模型将在{gpu_count}个GPU上并行训练')

                # 显示每个GPU的预期负载
                effective_batch_per_gpu = batch_size // gpu_count
                print(f'  - 每个GPU的有效批次大小: {effective_batch_per_gpu}')

                # 多GPU训练优化提示
                if effective_batch_per_gpu < 8:
                    print(f'  ⚠️ 警告: 每个GPU的批次大小较小({effective_batch_per_gpu})，可能影响训练效率')
                    print(f'  💡 建议: 考虑增加基础批次大小到 {8 * gpu_count} 以获得更好的性能')
                elif effective_batch_per_gpu > 64:
                    print(f'  ⚠️ 警告: 每个GPU的批次大小较大({effective_batch_per_gpu})，可能导致显存不足')
                    print(f'  💡 建议: 考虑减少基础批次大小或启用混合精度训练')
                else:
                    print(f'  ✓ 每个GPU的批次大小合理({effective_batch_per_gpu})')

            elif is_cuda:
                print('\n【单GPU配置】模型将在单个GPU上训练')
                print(f'  - 使用设备: {device}')
                print(f'  - 批次大小: {batch_size}')

                # 单GPU优化提示
                if batch_size < 16:
                    print(f'  💡 建议: 批次大小较小，考虑增加到16-32以获得更好的训练稳定性')
                elif batch_size > 128:
                    print(f'  ⚠️ 警告: 批次大小较大，注意显存使用情况')

            # 处理预训练模型
            if (config.train):
                if (config.pre_trained):
                    print('加载预训练模型...')
                    model.apply(reset_weights)
                    pre_trained_model = torch.load(config.pre_trained_model_path)
                    filtered_dict = OrderedDict((k, v) for k, v in pre_trained_model.items() if (not "fc" in k))
                    model.load_state_dict(filtered_dict, strict=False)
                elif (config.Aug_COCO_pre_trained):
                    print('加载COCO预训练模型...')
                    model.apply(reset_weights)
                    Aug_weight_path = r"motion_magnification_learning_based_master/magnet.pth"
                    Aug_state_dict = gen_state_dict(Aug_weight_path)
                    model.Aug_Encoder_L.load_state_dict(Aug_state_dict, strict=False)
                    model.Aug_Encoder_S.load_state_dict(Aug_state_dict, strict=False)
                    model.Aug_Encoder_T.load_state_dict(Aug_state_dict, strict=False)
                    model.Aug_Manipulator_L.load_state_dict(Aug_state_dict, strict=False)
                    model.Aug_Manipulator_S.load_state_dict(Aug_state_dict, strict=False)
                    model.Aug_Manipulator_T.load_state_dict(Aug_state_dict, strict=False)
                else:
                    print('使用随机初始化权重...')
                    model.apply(reset_weights)
            else:
                print('加载已训练模型...')
                model.load_state_dict(torch.load(weight_path))

            # 设置优化器
            optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate, betas=(0.9, 0.99), weight_decay=0.0005)

            # 准备数据加载器
            X_train = torch.Tensor(X_train).permute(0, 3, 1, 2)
            y_train = torch.Tensor(y_train).to(dtype=torch.long)
            dataset_train = TensorDataset(X_train, y_train)

            def worker_init_fn(worker_id):
                random.seed(seed + worker_id)
                np.random.seed(seed + worker_id)

            train_dl = DataLoader(dataset_train, batch_size=batch_size, shuffle=True, num_workers=8, pin_memory=True,
                                  worker_init_fn=worker_init_fn)

            X_test = torch.Tensor(X_test).permute(0, 3, 1, 2)
            y_test = torch.Tensor(y_test).to(dtype=torch.long)
            dataset_test = TensorDataset(X_test, y_test)
            test_dl = DataLoader(dataset_test, batch_size=batch_size, shuffle=False, num_workers=8)

            best_accuracy_for_each_subject = 0
            best_each_subject_pred = []

            max_iter = config.max_iter
            iter_num = 0
            epochs = max_iter // len(train_dl) + 1

            print_banner(f"🚀 开始训练征程 🚀", Colors.BRIGHT_GREEN, "🎯")
            print_highlight(f"总共 {epochs} 个epoch的精彩旅程即将开始！", "🌟")

            for epoch in range(1, epochs + 1):
                print_section(f"Epoch {epoch}/{epochs}", Colors.BRIGHT_CYAN, "📅")
                if (config.train):
                    model.train()
                    train_ce_loss = 0.0
                    middle_loss1 = 0.0
                    middle_loss2 = 0.0
                    KL_loss1 = 0.0
                    KL_loss2 = 0.0
                    L2_loss1 = 0.0
                    L2_loss2 = 0.0
                    loss_sum = 0.0

                    num_train_correct = 0
                    num_train_examples = 0

                    middle1_num_train_correct = 0
                    middle2_num_train_correct = 0

                    for batch_idx, batch in enumerate(train_dl):
                        optimizer.zero_grad()
                        x = batch[0].to(device)
                        y = batch[1].to(device)

                        # 混合精度前向传播
                        if use_mixed_precision:
                            with autocast():
                                yhat, AC1_out, AC2_out, final_feature, AC1_feature, AC2_feature = model(x)

                                # 计算各种损失
                                loss = loss_fn(yhat, y)
                                AC1_loss = loss_fn(AC1_out, y)
                                AC2_loss = loss_fn(AC2_out, y)

                                # 使用自适应参数的知识蒸馏损失
                                loss1by4 = new_kd_loss_function(AC1_out, yhat.detach(), iter_num, max_iter, n_subName, config.class_num)
                                loss2by4 = new_kd_loss_function(AC2_out, yhat.detach(), iter_num, max_iter, n_subName, config.class_num)

                                # 使用自适应参数的特征损失
                                feature_loss_1 = feature_loss_function(AC1_feature, final_feature.detach(), iter_num, max_iter, n_subName, config.class_num)
                                feature_loss_2 = feature_loss_function(AC2_feature, final_feature.detach(), iter_num, max_iter, n_subName, config.class_num)

                                # 获取当前subject的alpha值
                                alpha = get_subject_alpha(n_subName, iter_num, max_iter, config.class_num)

                                # 组合所有损失
                                total_losses = loss + (1 - alpha) * (AC1_loss + AC2_loss) + \
                                               alpha * (loss1by4 + loss2by4) + \
                                               (feature_loss_1 + feature_loss_2)

                            # 混合精度反向传播
                            scaler.scale(total_losses).backward()
                            scaler.step(optimizer)
                            scaler.update()
                        else:
                            # 标准精度训练
                            yhat, AC1_out, AC2_out, final_feature, AC1_feature, AC2_feature = model(x)

                            # 计算各种损失
                            loss = loss_fn(yhat, y)
                            AC1_loss = loss_fn(AC1_out, y)
                            AC2_loss = loss_fn(AC2_out, y)

                            # 使用自适应参数的知识蒸馏损失
                            loss1by4 = new_kd_loss_function(AC1_out, yhat.detach(), iter_num, max_iter, n_subName, config.class_num)
                            loss2by4 = new_kd_loss_function(AC2_out, yhat.detach(), iter_num, max_iter, n_subName, config.class_num)

                            # 使用自适应参数的特征损失
                            feature_loss_1 = feature_loss_function(AC1_feature, final_feature.detach(), iter_num, max_iter, n_subName, config.class_num)
                            feature_loss_2 = feature_loss_function(AC2_feature, final_feature.detach(), iter_num, max_iter, n_subName, config.class_num)

                            # 获取当前subject的alpha值
                            alpha = get_subject_alpha(n_subName, iter_num, max_iter, config.class_num)

                            # 组合所有损失
                            total_losses = loss + (1 - alpha) * (AC1_loss + AC2_loss) + \
                                           alpha * (loss1by4 + loss2by4) + \
                                           (feature_loss_1 + feature_loss_2)

                            # 标准反向传播
                            total_losses.backward()
                            optimizer.step()

                        # 累计损失和准确率
                        train_ce_loss += loss.data.item() * x.size(0)
                        middle_loss1 += AC1_loss.data.item() * x.size(0)
                        middle_loss2 += AC2_loss.data.item() * x.size(0)
                        KL_loss1 += loss1by4.data.item() * x.size(0)
                        KL_loss2 += loss2by4.data.item() * x.size(0)
                        L2_loss1 += feature_loss_1.data.item() * x.size(0)
                        L2_loss2 += feature_loss_2.data.item() * x.size(0)
                        loss_sum += total_losses * x.size(0)

                        num_train_correct += (torch.max(yhat, 1)[1] == y).sum().item()
                        num_train_examples += x.shape[0]

                        middle1_num_train_correct += (torch.max(AC1_out, 1)[1] == y).sum().item()
                        middle2_num_train_correct += (torch.max(AC2_out, 1)[1] == y).sum().item()

                        if batch_idx % 10 == 0:
                            current_temp = get_subject_temperature(n_subName, config.class_num)
                            current_alpha = get_subject_alpha(n_subName, iter_num, max_iter, config.class_num)
                            current_beta = get_subject_beta(n_subName, config.class_num)

                            #print(f"\n当前蒸馏参数 ({config.class_num}分类):")
                            #print(f"- 温度: {current_temp:.4f}")
                            #print(f"- Alpha: {current_alpha:.4f}")
                            #print(f"- Beta: {current_beta:.8f}")

                            # 记录到tensorboard
                            if writer is not None:
                                writer.add_scalar("Distillation/Temperature", current_temp, iter_num)
                                writer.add_scalar("Distillation/Alpha", current_alpha, iter_num)
                                writer.add_scalar("Distillation/Beta", current_beta, iter_num)

                        iter_num += 1
                        if iter_num >= max_iter:
                            break

                    # 计算训练指标
                    train_acc = num_train_correct / num_train_examples
                    middle1_acc = middle1_num_train_correct / num_train_examples
                    middle2_acc = middle2_num_train_correct / num_train_examples

                    train_ce_loss = train_ce_loss / len(train_dl.dataset)
                    middle_loss1 = middle_loss1 / len(train_dl.dataset)
                    middle_loss2 = middle_loss2 / len(train_dl.dataset)
                    KL_loss1 = KL_loss1 / len(train_dl.dataset)
                    KL_loss2 = KL_loss2 / len(train_dl.dataset)
                    L2_loss1 = L2_loss1 / len(train_dl.dataset)
                    L2_loss2 = L2_loss2 / len(train_dl.dataset)
                    loss_sum = loss_sum / len(train_dl.dataset)

                    print_success(f"训练集准确率: {train_acc:.4f}", "🎯")
                    #print(f'中间层1准确率: {middle1_acc:.4f}')
                    #print(f'中间层2准确率: {middle2_acc:.4f}')

                    # 显示GPU内存使用情况（多GPU训练时）
                    if is_cuda and gpu_count > 1:
                        gpu_memory = get_gpu_memory_info()
                        print(f'GPU内存使用: {gpu_memory}')

                    # 记录训练指标
                    if writer is not None:
                        writer.add_scalar("Train_Acc", train_acc, epoch)
                        writer.add_scalar("Middle1_Train_Acc", middle1_acc, epoch)
                        writer.add_scalar("Middle2_Train_Acc", middle2_acc, epoch)
                        writer.add_scalar("train_ce_loss", train_ce_loss, epoch)
                        writer.add_scalar("middle_loss1", middle_loss1, epoch)
                        writer.add_scalar("middle_loss2", middle_loss2, epoch)
                        writer.add_scalar("KL_loss1", KL_loss1, epoch)
                        writer.add_scalar("KL_loss2", KL_loss2, epoch)
                        writer.add_scalar("L2_loss1", L2_loss1, epoch)
                        writer.add_scalar("L2_loss2", L2_loss2, epoch)
                        writer.add_scalar("loss_sum", loss_sum, epoch)

                        writer.add_scalar("Aug Factor", model.amp_factor, epoch)

                # 评估模式
                print('\n开始验证...')
                model.eval()
                num_val_correct = 0
                num_val_examples = 0
                
                # 用于存储每个原始样本的所有预测结果
                predictions_by_original = {i: [] for i in range(len(original_test_labels))}  # 预初始化所有索引
                confidences_by_original = {i: [] for i in range(len(original_test_labels))}
                
                with torch.no_grad():
                    batch_start_idx = 0  # 跟踪batch中样本的起始索引
                    for batch in test_dl:
                        x = batch[0].to(device)
                        y = batch[1].to(device)
                        yhat, _, _, _, _, _ = model(x)
                        
                        # 获取预测结果和置信度
                        predictions = torch.max(yhat, 1)[1].cpu().numpy()
                        confidences = torch.max(torch.softmax(yhat, dim=1), 1)[0].cpu().numpy()
                        
                        # 将预测结果和置信度按原始样本分组
                        for i in range(len(y)):
                            orig_idx = original_test_indices[batch_start_idx + i]
                            predictions_by_original[orig_idx].append(predictions[i])
                            confidences_by_original[orig_idx].append(confidences[i])
                        
                        batch_start_idx += len(y)
                
                # 对每个原始样本进行投票
                final_predictions = []
                for i in range(len(original_test_labels)):
                    pred_list = predictions_by_original[i]
                    conf_list = confidences_by_original[i]
                    
                    if not pred_list:  # 安全检查
                        print(f"警告: 样本 {i} 没有预测结果")
                        final_predictions.append(0)  # 使用默认预测
                        continue
                    
                    if config.voting_method == 'weighted':
                        # 加权投票
                        vote_dict = {}
                        for pred, conf in zip(pred_list, conf_list):
                            if pred not in vote_dict:
                                vote_dict[pred] = 0
                            vote_dict[pred] += conf
                        final_pred = max(vote_dict.items(), key=lambda x: x[1])[0]
                    else:
                        # 多数投票
                        final_pred = max(set(pred_list), key=pred_list.count)
                    
                    final_predictions.append(final_pred)
                    if final_pred == original_test_labels[i]:
                        num_val_correct += 1
                    num_val_examples += 1
                
                val_acc = num_val_correct / num_val_examples
                # 使用颜色显示验证结果
                val_color = Colors.BRIGHT_GREEN if val_acc > best_accuracy_for_each_subject else Colors.BRIGHT_CYAN
                best_color = Colors.BRIGHT_YELLOW
                print(f"📊 {colorize('验证集准确率:', Colors.BOLD)} {colorize(f'{val_acc:.4f}', val_color)} | {colorize('最佳准确率:', Colors.BOLD)} {colorize(f'{best_accuracy_for_each_subject:.4f}', best_color)}")
                
                if writer is not None:
                    writer.add_scalar("Val_Acc", val_acc, epoch)

                if best_accuracy_for_each_subject <= val_acc:
                    best_accuracy_for_each_subject = val_acc
                    best_each_subject_pred = final_predictions
                    if (config.train) and (config.save_model):
                        try:
                            # 确保模型保存目录存在
                            model_dir = os.path.dirname(weight_path)
                            if not os.path.exists(model_dir):
                                os.makedirs(model_dir, exist_ok=True)
                                print(f'创建模型保存目录: {model_dir}')

                            print_success(f"保存最佳模型，准确率: {val_acc:.4f}", "💾")
                            print_info(f"模型保存路径: {weight_path}", "📁")

                            # 保存模型状态字典
                            if hasattr(model, 'module'):  # 如果使用了DataParallel
                                torch.save(model.module.state_dict(), weight_path)
                            else:
                                torch.save(model.state_dict(), weight_path)

                            print(f'模型保存成功: {weight_path}')

                        except Exception as e:
                            print(f'模型保存失败: {str(e)}')
                            print(f'保存路径: {weight_path}')
                            import traceback
                            print(f'详细错误信息: {traceback.format_exc()}')

                if val_acc == 1:
                    print_highlight("🎉 达到完美准确率，提前结束训练！ 🎉", "🏆")
                    break

                if not (config.train):
                    break

                # 在训练循环中添加Visdom可视化
                if (config.train) and visdom_available and viz is not None:
                    try:
                        # 训练过程可视化
                        viz.line([train_acc], [epoch], win='train_acc', update='append' if epoch > 1 else None,
                                opts=dict(title='训练准确率', xlabel='Epoch', ylabel='Accuracy'))
                        
                        viz.line([val_acc], [epoch], win='val_acc', update='append' if epoch > 1 else None,
                                opts=dict(title='验证准确率', xlabel='Epoch', ylabel='Accuracy'))
                        
                        viz.line([train_ce_loss], [epoch], win='train_loss', update='append' if epoch > 1 else None,
                                opts=dict(title='训练损失', xlabel='Epoch', ylabel='Loss'))
                    except Exception as e:
                        print(f"Visdom可视化出错: {str(e)}")
                        print("将继续训练但不再使用可视化功能")
                        visdom_available = False

            print('当前主体训练完成')
            print('最佳预测结果:', best_each_subject_pred)
            print('真实标签:', original_test_labels)
            print('当前评估结果:')
            
            # 先进行当前主体的评估
            dataset_name = "CASME2"  # 直接使用固定值
            current_UF1, current_UAR = recognition_evaluation(dataset_name, original_test_labels, best_each_subject_pred, show=True, class_num=config.class_num)
            best_current_UF1, best_current_UAR = recognition_evaluation(dataset_name, original_test_labels, best_each_subject_pred, show=True, class_num=config.class_num)

            # 更新累积结果
            total_pred.extend(best_each_subject_pred)
            total_gt.extend(original_test_labels)
            best_total_pred.extend(best_each_subject_pred)

            # 计算总体评估结果
            UF1, UAR = recognition_evaluation(dataset_name, total_gt, total_pred, show=False, class_num=config.class_num)
            best_UF1, best_UAR = recognition_evaluation(dataset_name, total_gt, best_total_pred, show=False, class_num=config.class_num)
            
            # 创建并更新accuracydict
            accuracydict = {
                'pred': best_each_subject_pred,
                'truth': original_test_labels,
                'UF1': best_current_UF1 if best_current_UF1 != '' else 0.0,
                'UAR': best_current_UAR if best_current_UAR != '' else 0.0
            }
            
            # 更新all_accuracy_dict和results_dict
            all_accuracy_dict[n_subName] = accuracydict
            results_dict['subject_results'][n_subName] = accuracydict
            
            # 更新总体结果
            results_dict['UF1'] = float(best_UF1) if best_UF1 != '' else 0.0
            results_dict['UAR'] = float(best_UAR) if best_UAR != '' else 0.0
            
            # 打印评估结果
            print('UF1:', round(float(UF1), 4) if UF1 != '' else 'N/A', 
                  '| UAR:', round(float(UAR), 4) if UAR != '' else 'N/A')
            print('最佳 UF1:', round(float(best_UF1), 4) if best_UF1 != '' else 'N/A', 
                  '| 最佳 UAR:', round(float(best_UAR), 4) if best_UAR != '' else 'N/A')

            # 在每个subject处理完后打印统计
            print("\n【数据增强统计】")
            for label, count in aug_stats.items():
                print(f"标签 {label}: 增强了 {count} 个样本")

            # 多GPU训练统计
            if is_cuda and gpu_count > 1:
                print(f"\n【多GPU训练统计】")
                print(f"使用GPU数量: {gpu_count}")
                print(f"总批次大小: {batch_size}")
                print(f"每GPU批次大小: {batch_size // gpu_count}")
                final_gpu_memory = get_gpu_memory_info()
                print(f"最终GPU内存使用: {final_gpu_memory}")
                
            # 计算每个情绪类别的准确率
            if config.class_num == 3:
                emotion_labels = {'正性': 0, '负性': 1, '惊讶': 2}
            else:
                emotion_labels = {'快乐': 0, '惊讶': 1, '厌恶': 2, '压抑': 3, '其他': 4}

            for emotion, label in emotion_labels.items():
                try:
                    # 确保有预测结果
                    if not best_total_pred:
                        results_dict['emotion_acc'][emotion] = 0.0
                        continue

                    # 转换为numpy数组
                    gt_array = np.array(total_gt)
                    pred_array = np.array(best_total_pred)
                    
                    # 获取当前情绪的样本索引
                    mask = (gt_array == label)
                    
                    # 如果没有该情绪的样本,准确率记为0
                    if not np.any(mask):
                        results_dict['emotion_acc'][emotion] = 0.0
                        continue
                    
                    # 计算准确率
                    correct = np.sum((pred_array[mask] == gt_array[mask]))
                    total = np.sum(mask)
                    acc = float(correct) / total if total > 0 else 0.0
                    
                    results_dict['emotion_acc'][emotion] = acc
                    
                except Exception as e:
                    print(f"{emotion}情绪准确率计算出错: {str(e)}")
                    results_dict['emotion_acc'][emotion] = 0.0

    finally:
        # 确保在程序结束时关闭TensorBoard writer
        try:
            if 'writer' in locals() and writer is not None:
                print("正在关闭TensorBoard写入器...")
                writer.close()
                print("TensorBoard写入器已关闭")
        except Exception as e:
            print(f"关闭TensorBoard写入器时出错: {str(e)}")

        # 确保在程序结束时关闭Visdom服务器
        try:
            if visdom_server:
                print("正在关闭Visdom服务器...")
                visdom_server.stop()
                print("Visdom服务器已关闭")
        except Exception as e:
            print(f"关闭Visdom服务器时出错: {str(e)}")

        # 注意：不在这里关闭Logger，因为它应该在主脚本中管理
        # Logger的关闭应该在主脚本的finally块中处理

    return results_dict
