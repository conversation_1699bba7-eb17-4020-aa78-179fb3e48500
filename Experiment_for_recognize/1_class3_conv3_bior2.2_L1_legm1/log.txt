日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (3分类)[0m
[1;96m🎯 类别权重: ['0.3794', '0.1349', '0.4857'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub17测试标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=1
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 1)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 953 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6485[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.2581[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.2581[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 2/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.8894[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.1935[0m | [1m最佳准确率:[0m [1;93m0.2581[0m

[1m[1;96m📅 Epoch 3/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9636[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.4516[0m | [1m最佳准确率:[0m [1;93m0.2581[0m
[1;92m💾 保存最佳模型，准确率: 0.4516[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 4/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9939[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.6774[0m | [1m最佳准确率:[0m [1;93m0.4516[0m
[1;92m💾 保存最佳模型，准确率: 0.6774[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 5/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6129[0m | [1m最佳准确率:[0m [1;93m0.6774[0m

[1m[1;96m📅 Epoch 6/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.7419[0m | [1m最佳准确率:[0m [1;93m0.6774[0m
[1;92m💾 保存最佳模型，准确率: 0.7419[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 7/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.7419[0m
[1;92m💾 保存最佳模型，准确率: 0.7419[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 8/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.7742[0m | [1m最佳准确率:[0m [1;93m0.7419[0m
[1;92m💾 保存最佳模型，准确率: 0.7742[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 9/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.7742[0m
[1;92m💾 保存最佳模型，准确率: 0.7742[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 10/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9924[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.7742[0m

[1m[1;96m📅 Epoch 11/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9894[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6774[0m | [1m最佳准确率:[0m [1;93m0.7742[0m

[1m[1;96m📅 Epoch 12/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.7742[0m

[1m[1;96m📅 Epoch 13/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.8387[0m | [1m最佳准确率:[0m [1;93m0.7742[0m
[1;92m💾 保存最佳模型，准确率: 0.8387[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 14/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 15/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 16/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.8387[0m
[1;92m💾 保存最佳模型，准确率: 0.8387[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 17/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.8387[0m
[1;92m💾 保存最佳模型，准确率: 0.8387[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 18/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.9032[0m | [1m最佳准确率:[0m [1;93m0.8387[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/1_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 19/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 20/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 21/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 22/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9833[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 23/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9864[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5806[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 24/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 25/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 26/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 27/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 28/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 29/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 30/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 31/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 32/953[0m
[1;96m----------------[0m
正在关闭TensorBoard写入器...
TensorBoard写入器已关闭

正在关闭日志系统...
