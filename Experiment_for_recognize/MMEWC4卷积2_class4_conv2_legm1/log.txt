
================================================================================
【训练开始】
================================================================================

【硬件信息】使用单个GPU进行训练
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

【损失函数配置】
使用加权Focal Loss (4分类)
类别权重: ['0.4082', '0.1651', '0.2041', '0.2226']
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: S30
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 4
跳过fear/sadness类别测试数据: 3

S30测试标签: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3]
创建受试者目录: S30
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积2_class4_conv2_legm1/S30/logs
正在初始化 风车形卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用风车形卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用风车形卷积
  - 在中间卷积层(conv2_L, conv2_S)中使用风车形卷积
  - 增强微表情细节特征提取能力
  - 扩大感受野范围，更好匹配微表情空间分布
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.4781

开始验证...
验证集准确率: 0.1143 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.1143
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
真实标签: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 4
TP: 4, FP: 31, FN: 0, TN: 0
F1分数: 0.2051, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 14
TP: 0, FP: 0, FN: 14, TN: 21
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 12
TP: 0, FP: 0, FN: 12, TN: 23
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 30
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0513
UAR: 0.2500

快乐(happiness)评估结果:
样本数: 4
TP: 4, FP: 31, FN: 0, TN: 0
F1分数: 0.2051, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 14
TP: 0, FP: 0, FN: 14, TN: 21
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 12
TP: 0, FP: 0, FN: 12, TN: 23
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 30
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0513
UAR: 0.2500
UF1: 0.0513 | UAR: 0.25
最佳 UF1: 0.0513 | 最佳 UAR: 0.25

【数据增强统计】
标签 0: 增强了 224 个样本
标签 1: 增强了 150 个样本
标签 2: 增强了 180 个样本
标签 3: 增强了 183 个样本

========================================
【当前处理受试者】: S06
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 3

S06测试标签: [0, 0, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
创建受试者目录: S06
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积2_class4_conv2_legm1/S06/logs
正在初始化 风车形卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用风车形卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用风车形卷积
  - 在中间卷积层(conv2_L, conv2_S)中使用风车形卷积
  - 增强微表情细节特征提取能力
  - 扩大感受野范围，更好匹配微表情空间分布
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.4594

开始验证...
验证集准确率: 0.1176 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.1176
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
真实标签: [0, 0, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 2
TP: 2, FP: 15, FN: 0, TN: 0
F1分数: 0.2105, 召回率: 1.0000
惊讶(surprise): 没有样本

厌恶(disgust)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 13
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 11
TP: 0, FP: 0, FN: 11, TN: 6
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0702
UAR: 0.3333

快乐(happiness)评估结果:
样本数: 2
TP: 2, FP: 15, FN: 0, TN: 0
F1分数: 0.2105, 召回率: 1.0000
惊讶(surprise): 没有样本

厌恶(disgust)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 13
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 11
TP: 0, FP: 0, FN: 11, TN: 6
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0702
UAR: 0.3333
UF1: 0.0517 | UAR: 0.25
最佳 UF1: 0.0517 | 最佳 UAR: 0.25

【数据增强统计】
标签 0: 增强了 462 个样本
标签 1: 增强了 328 个样本
标签 2: 增强了 384 个样本
标签 3: 增强了 348 个样本

========================================
【当前处理受试者】: S13
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 3

S13测试标签: [1, 1, 1, 1, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 3]
创建受试者目录: S13
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积2_class4_conv2_legm1/S13/logs
正在初始化 风车形卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用风车形卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用风车形卷积
  - 在中间卷积层(conv2_L, conv2_S)中使用风车形卷积
  - 增强微表情细节特征提取能力
  - 扩大感受野范围，更好匹配微表情空间分布
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.4625

开始验证...
验证集准确率: 0.2000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.2000
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
真实标签: [1, 1, 1, 1, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 3
TP: 3, FP: 12, FN: 0, TN: 0
F1分数: 0.3333, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 11
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 7
TP: 0, FP: 0, FN: 7, TN: 8
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 14
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0833
UAR: 0.2500

快乐(happiness)评估结果:
样本数: 3
TP: 3, FP: 12, FN: 0, TN: 0
F1分数: 0.3333, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 11
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 7
TP: 0, FP: 0, FN: 7, TN: 8
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 14
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0833
UAR: 0.2500
UF1: 0.0592 | UAR: 0.25
最佳 UF1: 0.0592 | 最佳 UAR: 0.25

【数据增强统计】
标签 0: 增强了 693 个样本
标签 1: 增强了 498 个样本
标签 2: 增强了 579 个样本
标签 3: 增强了 543 个样本

========================================
【当前处理受试者】: S09
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 3

S09测试标签: [1, 1, 1, 1, 1, 0, 2, 2, 2, 2, 2, 2, 2, 3]
创建受试者目录: S09
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积2_class4_conv2_legm1/S09/logs
正在初始化 风车形卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用风车形卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用风车形卷积
  - 在中间卷积层(conv2_L, conv2_S)中使用风车形卷积
  - 增强微表情细节特征提取能力
  - 扩大感受野范围，更好匹配微表情空间分布
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.4688

开始验证...
验证集准确率: 0.0714 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.0714
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
真实标签: [1, 1, 1, 1, 1, 0, 2, 2, 2, 2, 2, 2, 2, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 1
TP: 1, FP: 13, FN: 0, TN: 0
F1分数: 0.1333, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 9
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 7
TP: 0, FP: 0, FN: 7, TN: 7
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 13
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0333
UAR: 0.2500

快乐(happiness)评估结果:
样本数: 1
TP: 1, FP: 13, FN: 0, TN: 0
F1分数: 0.1333, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 9
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 7
TP: 0, FP: 0, FN: 7, TN: 7
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 13
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0333
UAR: 0.2500
UF1: 0.0549 | UAR: 0.25
最佳 UF1: 0.0549 | 最佳 UAR: 0.25

【数据增强统计】
标签 0: 增强了 938 个样本
标签 1: 增强了 666 个样本
标签 2: 增强了 774 个样本
标签 3: 增强了 738 个样本

========================================
【当前处理受试者】: S10
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 4
跳过fear/sadness类别测试数据: 3

S10测试标签: [1, 1, 0, 0, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3]
创建受试者目录: S10
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积2_class4_conv2_legm1/S10/logs
正在初始化 风车形卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用风车形卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用风车形卷积
  - 在中间卷积层(conv2_L, conv2_S)中使用风车形卷积
  - 增强微表情细节特征提取能力
  - 扩大感受野范围，更好匹配微表情空间分布
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.5188

开始验证...
验证集准确率: 0.1429 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.1429
当前主体训练完成
最佳预测结果: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
真实标签: [1, 1, 0, 0, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 12
F1分数: 0.0000, 召回率: 0.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 2, FP: 12, FN: 0, TN: 0
F1分数: 0.2500, 召回率: 1.0000

厌恶(disgust)评估结果:
样本数: 6
TP: 0, FP: 0, FN: 6, TN: 8
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 10
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0625
UAR: 0.2500

快乐(happiness)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 12
F1分数: 0.0000, 召回率: 0.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 2, FP: 12, FN: 0, TN: 0
F1分数: 0.2500, 召回率: 1.0000

厌恶(disgust)评估结果:
样本数: 6
TP: 0, FP: 0, FN: 6, TN: 8
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 10
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0625
UAR: 0.2500
UF1: 0.0794 | UAR: 0.2283
最佳 UF1: 0.0794 | 最佳 UAR: 0.2283

【数据增强统计】
标签 0: 增强了 1176 个样本
标签 1: 增强了 840 个样本
标签 2: 增强了 972 个样本
标签 3: 增强了 924 个样本

========================================
【当前处理受试者】: S16
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...

S16测试标签: [1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3]
创建受试者目录: S16
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积2_class4_conv2_legm1/S16/logs
正在初始化 风车形卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用风车形卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用风车形卷积
  - 在中间卷积层(conv2_L, conv2_S)中使用风车形卷积
  - 增强微表情细节特征提取能力
  - 扩大感受野范围，更好匹配微表情空间分布
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.4562

开始验证...
验证集准确率: 0.0000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.0000
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
真实标签: [1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3]
当前评估结果:
快乐(happiness): 没有样本

惊讶(surprise)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 12
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 10
TP: 0, FP: 0, FN: 10, TN: 4
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 12
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0000
UAR: 0.0000
快乐(happiness): 没有样本

惊讶(surprise)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 12
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 10
TP: 0, FP: 0, FN: 10, TN: 4
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 12
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0000
UAR: 0.0000
UF1: 0.0711 | UAR: 0.2269
最佳 UF1: 0.0711 | 最佳 UAR: 0.2269

【数据增强统计】
标签 0: 增强了 1428 个样本
标签 1: 增强了 1014 个样本
标签 2: 增强了 1158 个样本
标签 3: 增强了 1116 个样本

========================================
【当前处理受试者】: S05
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 3

S05测试标签: [1, 1, 0, 0, 2, 2, 2, 2, 3, 3, 3, 3]
创建受试者目录: S05
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积2_class4_conv2_legm1/S05/logs
正在初始化 风车形卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用风车形卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用风车形卷积
  - 在中间卷积层(conv2_L, conv2_S)中使用风车形卷积
  - 增强微表情细节特征提取能力
  - 扩大感受野范围，更好匹配微表情空间分布
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.4234

开始验证...
验证集准确率: 0.5833 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.5833
当前主体训练完成
最佳预测结果: [1, 1, 0, 0, 0, 0, 3, 0, 3, 0, 3, 3]
真实标签: [1, 1, 0, 0, 2, 2, 2, 2, 3, 3, 3, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 2
TP: 2, FP: 4, FN: 0, TN: 6
F1分数: 0.5000, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 10
F1分数: 1.0000, 召回率: 1.0000

厌恶(disgust)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 8
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 4
TP: 3, FP: 1, FN: 1, TN: 7
F1分数: 0.7500, 召回率: 0.7500

总体评估结果:
UF1: 0.5625
UAR: 0.6875

快乐(happiness)评估结果:
样本数: 2
TP: 2, FP: 4, FN: 0, TN: 6
F1分数: 0.5000, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 10
F1分数: 1.0000, 召回率: 1.0000

厌恶(disgust)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 8
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 4
TP: 3, FP: 1, FN: 1, TN: 7
F1分数: 0.7500, 召回率: 0.7500

总体评估结果:
UF1: 0.5625
UAR: 0.6875
UF1: 0.1435 | UAR: 0.2756
最佳 UF1: 0.1435 | 最佳 UAR: 0.2756

【数据增强统计】
标签 0: 增强了 1666 个样本
标签 1: 增强了 1188 个样本
标签 2: 增强了 1362 个样本
标签 3: 增强了 1302 个样本

========================================
【当前处理受试者】: S11
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 4

S11测试标签: [1, 1, 1, 1, 1, 0, 2, 2, 2, 2, 3]
创建受试者目录: S11
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积2_class4_conv2_legm1/S11/logs
正在初始化 风车形卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用风车形卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用风车形卷积
  - 在中间卷积层(conv2_L, conv2_S)中使用风车形卷积
  - 增强微表情细节特征提取能力
  - 扩大感受野范围，更好匹配微表情空间分布
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.5141

开始验证...
验证集准确率: 0.0909 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.0909
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
真实标签: [1, 1, 1, 1, 1, 0, 2, 2, 2, 2, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 1
TP: 1, FP: 10, FN: 0, TN: 0
F1分数: 0.1667, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 6
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 7
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 10
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0417
UAR: 0.2500

快乐(happiness)评估结果:
样本数: 1
TP: 1, FP: 10, FN: 0, TN: 0
F1分数: 0.1667, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 6
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 7
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 10
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0417
UAR: 0.2500
UF1: 0.1366 | UAR: 0.2719
最佳 UF1: 0.1366 | 最佳 UAR: 0.2719

【数据增强统计】
标签 0: 增强了 1911 个样本
标签 1: 增强了 1356 个样本
标签 2: 增强了 1566 个样本
标签 3: 增强了 1497 个样本

========================================
【当前处理受试者】: S21
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 3

S21测试标签: [1, 1, 1, 1, 1, 0, 0, 0, 3, 3, 3, 3]
创建受试者目录: S21
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积2_class4_conv2_legm1/S21/logs
正在初始化 风车形卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用风车形卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用风车形卷积
  - 在中间卷积层(conv2_L, conv2_S)中使用风车形卷积
  - 增强微表情细节特征提取能力
  - 扩大感受野范围，更好匹配微表情空间分布
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.5328

开始验证...
验证集准确率: 0.3333 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.3333
当前主体训练完成
最佳预测结果: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
真实标签: [1, 1, 1, 1, 1, 0, 0, 0, 3, 3, 3, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 3
TP: 0, FP: 0, FN: 3, TN: 9
F1分数: 0.0000, 召回率: 0.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 7
F1分数: 0.0000, 召回率: 0.0000
厌恶(disgust): 没有样本

其他(others)评估结果:
样本数: 4
TP: 4, FP: 8, FN: 0, TN: 0
F1分数: 0.5000, 召回率: 1.0000

总体评估结果:
UF1: 0.1667
UAR: 0.3333

快乐(happiness)评估结果:
样本数: 3
TP: 0, FP: 0, FN: 3, TN: 9
F1分数: 0.0000, 召回率: 0.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 7
F1分数: 0.0000, 召回率: 0.0000
厌恶(disgust): 没有样本

其他(others)评估结果:
样本数: 4
TP: 4, FP: 8, FN: 0, TN: 0
F1分数: 0.5000, 召回率: 1.0000

总体评估结果:
UF1: 0.1667
UAR: 0.3333
UF1: 0.1578 | UAR: 0.2592
最佳 UF1: 0.1578 | 最佳 UAR: 0.2592

【数据增强统计】
标签 0: 增强了 2142 个样本
标签 1: 增强了 1524 个样本
标签 2: 增强了 1782 个样本
标签 3: 增强了 1683 个样本

========================================
【当前处理受试者】: S15
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 4
跳过fear/sadness类别测试数据: 3

S15测试标签: [1, 1, 1, 0, 0, 0, 2, 3, 3, 3]
创建受试者目录: S15
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积2_class4_conv2_legm1/S15/logs
正在初始化 风车形卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用风车形卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用风车形卷积
  - 在中间卷积层(conv2_L, conv2_S)中使用风车形卷积
  - 增强微表情细节特征提取能力
  - 扩大感受野范围，更好匹配微表情空间分布
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.4422

开始验证...
验证集准确率: 0.3000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.3000
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
真实标签: [1, 1, 1, 0, 0, 0, 2, 3, 3, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 3
TP: 3, FP: 7, FN: 0, TN: 0
F1分数: 0.4615, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 3
TP: 0, FP: 0, FN: 3, TN: 7
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 9
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 3
TP: 0, FP: 0, FN: 3, TN: 7
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.1154
UAR: 0.2500

快乐(happiness)评估结果:
样本数: 3
TP: 3, FP: 7, FN: 0, TN: 0
F1分数: 0.4615, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 3
TP: 0, FP: 0, FN: 3, TN: 7
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 9
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 3
TP: 0, FP: 0, FN: 3, TN: 7
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.1154
UAR: 0.2500
UF1: 0.1577 | UAR: 0.2629
最佳 UF1: 0.1577 | 最佳 UAR: 0.2629

【数据增强统计】
标签 0: 增强了 2373 个样本
标签 1: 增强了 1696 个样本
标签 2: 增强了 1995 个样本
标签 3: 增强了 1872 个样本

========================================
【当前处理受试者】: S03
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...

S03测试标签: [1, 1, 1, 1, 1, 1, 1, 3, 3, 3, 3, 3]
创建受试者目录: S03
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积2_class4_conv2_legm1/S03/logs
正在初始化 风车形卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用风车形卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用风车形卷积
  - 在中间卷积层(conv2_L, conv2_S)中使用风车形卷积
  - 增强微表情细节特征提取能力
  - 扩大感受野范围，更好匹配微表情空间分布
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.5141

开始验证...
验证集准确率: 0.0000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.0000
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
真实标签: [1, 1, 1, 1, 1, 1, 1, 3, 3, 3, 3, 3]
当前评估结果:
快乐(happiness): 没有样本

惊讶(surprise)评估结果:
样本数: 7
TP: 0, FP: 0, FN: 7, TN: 5
F1分数: 0.0000, 召回率: 0.0000
厌恶(disgust): 没有样本

其他(others)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 7
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0000
UAR: 0.0000
快乐(happiness): 没有样本

惊讶(surprise)评估结果:
样本数: 7
TP: 0, FP: 0, FN: 7, TN: 5
F1分数: 0.0000, 召回率: 0.0000
厌恶(disgust): 没有样本

其他(others)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 7
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0000
UAR: 0.0000
UF1: 0.1438 | UAR: 0.2536
最佳 UF1: 0.1438 | 最佳 UAR: 0.2536

【数据增强统计】
标签 0: 增强了 2625 个样本
标签 1: 增强了 1860 个样本
标签 2: 增强了 2211 个样本
标签 3: 增强了 2055 个样本

========================================
【当前处理受试者】: S08
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 3

S08测试标签: [1, 0, 0, 0, 0, 0, 2, 2, 3, 3]
创建受试者目录: S08
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积2_class4_conv2_legm1/S08/logs
正在初始化 风车形卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用风车形卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用风车形卷积
  - 在中间卷积层(conv2_L, conv2_S)中使用风车形卷积
  - 增强微表情细节特征提取能力
  - 扩大感受野范围，更好匹配微表情空间分布
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.4266

开始验证...
验证集准确率: 0.5000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.5000
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
真实标签: [1, 0, 0, 0, 0, 0, 2, 2, 3, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 5
TP: 5, FP: 5, FN: 0, TN: 0
F1分数: 0.6667, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 9
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 8
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 8
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.1667
UAR: 0.2500

快乐(happiness)评估结果:
样本数: 5
TP: 5, FP: 5, FN: 0, TN: 0
F1分数: 0.6667, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 9
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 8
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 8
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.1667
UAR: 0.2500
UF1: 0.1514 | UAR: 0.2626
最佳 UF1: 0.1514 | 最佳 UAR: 0.2626

【数据增强统计】
标签 0: 增强了 2842 个样本
标签 1: 增强了 2036 个样本
标签 2: 增强了 2421 个样本
标签 3: 增强了 2247 个样本

========================================
【当前处理受试者】: S01
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 4

S01测试标签: [1, 1, 0, 0, 0, 3, 3, 3, 3, 3]
创建受试者目录: S01
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积2_class4_conv2_legm1/S01/logs
正在初始化 风车形卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用风车形卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用风车形卷积
  - 在中间卷积层(conv2_L, conv2_S)中使用风车形卷积
  - 增强微表情细节特征提取能力
  - 扩大感受野范围，更好匹配微表情空间分布
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.4922

开始验证...
验证集准确率: 0.3000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.3000
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
真实标签: [1, 1, 0, 0, 0, 3, 3, 3, 3, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 3
TP: 3, FP: 7, FN: 0, TN: 0
F1分数: 0.4615, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 8
F1分数: 0.0000, 召回率: 0.0000
厌恶(disgust): 没有样本

其他(others)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 5
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.1538
UAR: 0.3333

快乐(happiness)评估结果:
样本数: 3
TP: 3, FP: 7, FN: 0, TN: 0
F1分数: 0.4615, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 8
F1分数: 0.0000, 召回率: 0.0000
厌恶(disgust): 没有样本

其他(others)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 5
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.1538
UAR: 0.3333
UF1: 0.1497 | UAR: 0.2626
最佳 UF1: 0.1497 | 最佳 UAR: 0.2626

【数据增强统计】
标签 0: 增强了 3073 个样本
标签 1: 增强了 2210 个样本
标签 2: 增强了 2637 个样本
标签 3: 增强了 2430 个样本

========================================
【当前处理受试者】: S18
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...

S18测试标签: [1, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3]
创建受试者目录: S18
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积2_class4_conv2_legm1/S18/logs
正在初始化 风车形卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用风车形卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用风车形卷积
  - 在中间卷积层(conv2_L, conv2_S)中使用风车形卷积
  - 增强微表情细节特征提取能力
  - 扩大感受野范围，更好匹配微表情空间分布
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.5047

开始验证...
验证集准确率: 0.0000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.0000
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
真实标签: [1, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3]
当前评估结果:
快乐(happiness): 没有样本

惊讶(surprise)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 6
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 7
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 9
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0000
UAR: 0.0000
快乐(happiness): 没有样本

惊讶(surprise)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 6
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 7
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 9
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0000
UAR: 0.0000
UF1: 0.1423 | UAR: 0.2594
最佳 UF1: 0.1423 | 最佳 UAR: 0.2594

【数据增强统计】
标签 0: 增强了 3325 个样本
标签 1: 增强了 2378 个样本
标签 2: 增强了 2841 个样本
标签 3: 增强了 2622 个样本

========================================
【当前处理受试者】: S02
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...

S02测试标签: [1, 1, 0, 0, 2, 3, 3, 3, 3, 3]
创建受试者目录: S02
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积2_class4_conv2_legm1/S02/logs
正在初始化 风车形卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用风车形卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用风车形卷积
  - 在中间卷积层(conv2_L, conv2_S)中使用风车形卷积
  - 增强微表情细节特征提取能力
  - 扩大感受野范围，更好匹配微表情空间分布
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.4719

开始验证...
验证集准确率: 0.2000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.2000
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 0, 0, 0, 2, 2, 2]
真实标签: [1, 1, 0, 0, 2, 3, 3, 3, 3, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 2
TP: 2, FP: 5, FN: 0, TN: 3
F1分数: 0.4444, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 8
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 1
TP: 0, FP: 3, FN: 1, TN: 6
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 5
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.1111
UAR: 0.2500

快乐(happiness)评估结果:
样本数: 2
TP: 2, FP: 5, FN: 0, TN: 3
F1分数: 0.4444, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 8
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 1
TP: 0, FP: 3, FN: 1, TN: 6
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 5
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.1111
UAR: 0.2500
UF1: 0.14 | UAR: 0.2584
最佳 UF1: 0.14 | 最佳 UAR: 0.2584

【数据增强统计】
标签 0: 增强了 3563 个样本
标签 1: 增强了 2552 个样本
标签 2: 增强了 3054 个样本
标签 3: 增强了 2805 个样本

========================================
【当前处理受试者】: S04
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 4

S04测试标签: [1, 1, 1, 0, 2, 2, 2, 3, 3]
创建受试者目录: S04
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积2_class4_conv2_legm1/S04/logs
正在初始化 风车形卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用风车形卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用风车形卷积
  - 在中间卷积层(conv2_L, conv2_S)中使用风车形卷积
  - 增强微表情细节特征提取能力
  - 扩大感受野范围，更好匹配微表情空间分布
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1
