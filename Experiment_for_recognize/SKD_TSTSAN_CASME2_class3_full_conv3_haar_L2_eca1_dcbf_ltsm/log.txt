日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (3分类)[0m
[1;96m🎯 类别权重: ['0.3794', '0.1349', '0.4857'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub17测试标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1_dcbf_ltsm/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1_dcbf_ltsm/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=haar, 层数=2
🔗 启用动态跨分支特征交互机制 (DCBF) - 增强分支间信息流动
🔄 启用可学习时间移位模块 (LTSM) - 自适应时间建模策略
🔄 启用轻量化时间移位: 128通道, 16组, 2种移位模式
🔄 启用轻量化时间移位: 128通道, 16组, 2种移位模式
🔄 启用轻量化时间移位: 64通道, 8组, 2种移位模式
🔄 启用轻量化时间移位: 64通道, 8组, 2种移位模式
🔄 启用轻量化时间移位: 128通道, 16组, 2种移位模式
🔄 启用轻量化时间移位: 128通道, 16组, 2种移位模式
🔄 启用轻量化时间移位: 128通道, 16组, 2种移位模式
🔄 启用轻量化时间移位: 128通道, 16组, 2种移位模式
🔗 轻量化跨分支交互模块已配置: 2个交互层级 (64→128通道), 参数减少60%
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: haar (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m✅ 启用动态跨分支特征交互(DCBF):[0m
  [96m- 三层级动态特征交互增强[0m 🎯
  [96m- 自适应权重学习机制[0m 🔍
  [96m- 参数增加约10.9%，性能提升显著[0m 📈
  [1;95m- 创新架构，推荐用于性能优化[0m ⭐

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 953 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6697[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.7419[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.7419[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1_dcbf_ltsm/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1_dcbf_ltsm/sub17/sub17.pth

[1m[1;96m📅 Epoch 2/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.8515[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.7419[0m
[1;92m💾 保存最佳模型，准确率: 0.7419[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1_dcbf_ltsm/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1_dcbf_ltsm/sub17/sub17.pth

[1m[1;96m📅 Epoch 3/953[0m
[1;96m---------------[0m
