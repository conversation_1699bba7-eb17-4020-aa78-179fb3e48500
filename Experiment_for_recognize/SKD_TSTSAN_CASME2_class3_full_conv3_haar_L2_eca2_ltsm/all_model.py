import torch.nn as nn
import torch
import torch.nn.functional as F
import math
from motion_magnification_learning_based_master.magnet import Manipulator as MagManipulator
from motion_magnification_learning_based_master.magnet import Encoder_No_texture as MagEncoder_No_texture
from Pinwheel_shaped_Convolution import Pinwheel_shaped_Convolution
from WTConv import WTConv2d
from PRO_CODE.WTRFAConv import WTRFAConv
from PRO_CODE.WTPRFAConv import WTPRFAConv
from PRO_CODE.WTPRFAConv_Lite import WTPRFAConv_Lite
from PRO_CODE.LEGM import LEGM


class LightweightCrossBranchFusion(nn.Module):
    """
    轻量化跨分支特征交互机制 (Lightweight Cross-Branch Fusion, LCBF)

    简化的跨分支交互设计，通过通道注意力和轻量化融合实现分支间信息交换，
    大幅减少参数量和计算复杂度，同时保持有效的特征增强能力。

    核心改进：
    1. 移除复杂的全局上下文提取器，使用简单的通道注意力
    2. 共享变换器减少参数冗余
    3. 轻量化融合策略，避免过度复杂的特征变换
    4. 自适应残差权重，根据特征重要性调整融合强度

    Args:
        channels: 特征通道数
        num_branches: 分支数量，默认为3（L、S、T）
        reduction_ratio: 通道压缩比例，默认为8（更大的压缩比）
    """
    def __init__(self, channels, num_branches=3, reduction_ratio=8):
        super(LightweightCrossBranchFusion, self).__init__()
        self.channels = channels
        self.num_branches = num_branches

        # 轻量化通道注意力 - 大幅减少参数
        self.global_context = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction_ratio, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction_ratio, num_branches, 1, bias=False),
            nn.Sigmoid()
        )

        # 共享的轻量化变换器 - 所有分支共享以减少参数
        self.shared_transformer = nn.Sequential(
            nn.Conv2d(channels, channels, 1, bias=False),
            nn.BatchNorm2d(channels)
        )

        # 简化的融合卷积 - 使用1x1卷积减少计算量
        self.fusion_conv = nn.Conv2d(channels, channels, 1, bias=False)

        # 自适应残差权重 - 学习最优的融合强度
        self.residual_weight = nn.Parameter(torch.tensor(0.1))

    def forward(self, branch_features):
        """
        轻量化前向传播过程

        Args:
            branch_features: 包含三个分支特征的列表 [x_L, x_S, x_T]
                           每个特征的形状为 [B, C, H, W]

        Returns:
            enhanced_features: 增强后的分支特征列表
        """
        # 处理batch size不匹配的情况
        if len(set(f.shape[0] for f in branch_features)) > 1:
            max_batch_size = max(f.shape[0] for f in branch_features)
            aligned_features = []
            for feature in branch_features:
                if feature.shape[0] < max_batch_size:
                    repeat_factor = max_batch_size // feature.shape[0]
                    feature = feature.repeat(repeat_factor, 1, 1, 1)
                aligned_features.append(feature)
            branch_features = aligned_features

        enhanced_features = []

        # 对每个分支进行轻量化处理
        for i, feature in enumerate(branch_features):
            # 1. 生成分支特定的注意力权重
            attention_weights = self.global_context(feature)  # [B, num_branches, 1, 1]

            # 2. 应用共享变换器
            transformed = self.shared_transformer(feature)

            # 3. 计算其他分支的加权贡献
            cross_info = torch.zeros_like(transformed)
            for j, other_feature in enumerate(branch_features):
                if i != j:
                    other_transformed = self.shared_transformer(other_feature)
                    weight = attention_weights[:, j:j+1, :, :]  # [B, 1, 1, 1]
                    cross_info += weight * other_transformed

            # 4. 融合跨分支信息
            fused = self.fusion_conv(cross_info)

            # 5. 自适应残差连接
            enhanced = transformed + torch.clamp(self.residual_weight, 0.0, 0.3) * fused
            enhanced_features.append(enhanced)

        return enhanced_features


def gen_state_dict(weights_path):
    st = torch.load(weights_path)
    st_ks = list(st.keys())
    st_vs = list(st.values())
    state_dict = {}
    for st_k, st_v in zip(st_ks, st_vs):
        state_dict[st_k.replace('module.', '')] = st_v
    return state_dict

class ConsensusModule(torch.nn.Module):

    def __init__(self, consensus_type, dim=1):
        super(ConsensusModule, self).__init__()
        self.consensus_type = consensus_type if consensus_type != 'rnn' else 'identity'
        self.dim = dim

    def forward(self, input):
        return SegmentConsensus(self.consensus_type, self.dim)(input)

class SegmentConsensus(torch.nn.Module):

    def __init__(self, consensus_type, dim=1):
        super(SegmentConsensus, self).__init__()
        self.consensus_type = consensus_type
        self.dim = dim
        self.shape = None

    def forward(self, input_tensor):
        self.shape = input_tensor.size()
        if self.consensus_type == 'avg':
            output = input_tensor.mean(dim=self.dim, keepdim=True)
        elif self.consensus_type == 'identity':
            output = input_tensor
        else:
            output = None

        return output

class TemporalShift(nn.Module):
    def __init__(self, net, n_segment=3, n_div=8, inplace=False):
        super(TemporalShift, self).__init__()
        self.net = net
        self.n_segment = n_segment
        self.fold_div = n_div
        self.inplace = inplace
        if inplace:
            print('=> 使用原地移位...')
        print('=> 使用折叠除法: {}'.format(self.fold_div))

    def forward(self, x):
        x = self.shift(x, self.n_segment, fold_div=self.fold_div, inplace=self.inplace)
        return self.net(x)

    @staticmethod
    def shift(x, n_segment, fold_div=3, inplace=False):
        nt, c, h, w = x.size()
        n_batch = nt // n_segment
        x = x.view(n_batch, n_segment, c, h, w)

        fold = c // fold_div
        if inplace:
            raise NotImplementedError
        else:
            out = torch.zeros_like(x)
            out[:, :-1, :fold] = x[:, 1:, :fold]  # shift left
            out[:, 1:, fold: 2 * fold] = x[:, :-1, fold: 2 * fold]  # shift right
            out[:, :, 2 * fold:] = x[:, :, 2 * fold:]  # not shift

        return out.view(nt, c, h, w)


class LightweightTemporalShift(nn.Module):
    """
    轻量化时间移位模块 (Lightweight Temporal Shift Module, LTSM)

    简化的可学习时间移位设计，通过减少移位模式和参数数量来提升效率，
    同时保持对微表情时序特征的有效建模能力。

    核心改进：
    1. 简化移位模式：仅保留左移和右移两种核心模式
    2. 通道组级权重：减少参数数量，避免过拟合
    3. 固定温度参数：移除不必要的可学习参数
    4. 轻量化权重计算：使用更简单的权重生成策略

    Args:
        net: 要包装的网络层
        n_segment: 时间段数，默认为2
        n_div: 通道分割比例，默认为8
        learnable_shifts: 是否启用可学习移位，默认为True
        group_size: 通道组大小，默认为8（减少参数数量）
    """
    def __init__(self, net, n_segment=2, n_div=8, learnable_shifts=True, group_size=8):
        super(LightweightTemporalShift, self).__init__()
        self.net = net
        self.n_segment = n_segment
        self.fold_div = n_div
        self.learnable_shifts = learnable_shifts
        self.group_size = group_size

        if learnable_shifts:
            # 获取网络的输出通道数
            if hasattr(net, 'out_channels'):
                channels = net.out_channels
            elif hasattr(net, 'weight') and len(net.weight.shape) >= 2:
                channels = net.weight.shape[0]
            else:
                channels = 64

            # 计算通道组数量
            self.num_groups = max(1, channels // group_size)

            # 简化的移位权重：仅左移和右移两种模式，按组学习
            self.shift_weights = nn.Parameter(torch.tensor([0.5, 0.5]).repeat(self.num_groups, 1))

            # 简化的移位强度：全局控制移位强度
            self.shift_intensity = nn.Parameter(torch.tensor(0.5))

            print(f"🔄 启用轻量化时间移位: {channels}通道, {self.num_groups}组, 2种移位模式")
        else:
            print("🔄 使用传统固定时间移位")

    def forward(self, x):
        if self.learnable_shifts:
            x = self.lightweight_shift(x)
        else:
            x = TemporalShift.shift(x, self.n_segment, fold_div=self.fold_div, inplace=False)
        return self.net(x)

    def lightweight_shift(self, x):
        """
        轻量化时间移位操作

        Args:
            x: 输入特征 [nt, c, h, w]

        Returns:
            shifted_x: 移位后的特征
        """
        nt, c, h, w = x.size()
        n_batch = nt // self.n_segment
        x = x.view(n_batch, self.n_segment, c, h, w)

        # 计算移位权重分布（仅左移和右移）
        shift_probs = torch.softmax(self.shift_weights, dim=-1)  # [num_groups, 2]

        # 初始化输出
        out = torch.zeros_like(x)

        # 按通道组处理
        channels_per_group = c // self.num_groups

        for group_idx in range(self.num_groups):
            start_ch = group_idx * channels_per_group
            end_ch = start_ch + channels_per_group if group_idx < self.num_groups - 1 else c

            # 获取当前组的权重
            left_weight = shift_probs[group_idx, 0] * self.shift_intensity
            right_weight = shift_probs[group_idx, 1] * self.shift_intensity

            # 左移：捕获未来信息
            if self.n_segment > 1:
                left_shifted = torch.zeros_like(x[:, :, start_ch:end_ch, :, :])
                left_shifted[:, :-1, :] = x[:, 1:, start_ch:end_ch, :, :]

                # 右移：捕获历史信息
                right_shifted = torch.zeros_like(x[:, :, start_ch:end_ch, :, :])
                right_shifted[:, 1:, :] = x[:, :-1, start_ch:end_ch, :, :]

                # 组合移位结果
                shifted = left_weight * left_shifted + right_weight * right_shifted

                # 残差连接：保留部分原始特征
                out[:, :, start_ch:end_ch, :, :] = (1 - self.shift_intensity) * x[:, :, start_ch:end_ch, :, :] + shifted
            else:
                # 如果只有一个时间段，直接复制原始特征
                out[:, :, start_ch:end_ch, :, :] = x[:, :, start_ch:end_ch, :, :]

        return out.view(nt, c, h, w)

    def get_shift_statistics(self):
        """获取移位统计信息，用于分析和可视化"""
        if not self.learnable_shifts:
            return None

        shift_probs = torch.softmax(self.shift_weights, dim=-1)
        stats = {
            'shift_weights_mean': shift_probs.mean(dim=0).detach().cpu().numpy(),
            'shift_intensity': self.shift_intensity.item(),
            'num_groups': self.num_groups,
            'dominant_modes': torch.argmax(shift_probs, dim=-1).detach().cpu().numpy()
        }
        return stats


class ECA_Lightweight(nn.Module):
    """
    轻量化ECA机制（ECA-Lightweight）
    简化ECA设计，移除冗余的双分支结构，通过单分支设计和参数共享
    实现更高效的通道注意力机制，特别适合微表情识别的细粒度特征建模。

    核心改进：
    1. 单分支设计：移除双分支结构，减少参数和计算量
    2. 自适应核大小：保留原有的自适应卷积核大小计算
    3. 轻量化池化：仅使用平均池化，避免冗余的最大池化
    4. 参数效率：通过更简洁的设计实现相同的注意力效果

    Args:
        channel: 输入特征图的通道数
        gamma: 自适应卷积核大小计算参数，默认为2
        b: 自适应卷积核大小计算偏置，默认为1
        use_max_pool: 是否使用最大池化增强，默认为False
    """
    def __init__(self, channel, gamma=2, b=1, use_max_pool=False):
        super(ECA_Lightweight, self).__init__()
        # 自适应卷积核大小计算（保持原有逻辑）
        t = int(abs((math.log(channel, 2) + b) / gamma))
        k_size = t if t % 2 else t + 1

        # 单分支轻量化设计
        self.conv = nn.Conv1d(1, 1, kernel_size=k_size, padding=(k_size - 1) // 2, bias=False)
        self.sigmoid = nn.Sigmoid()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)

        # 可选的最大池化增强
        self.use_max_pool = use_max_pool
        if use_max_pool:
            self.max_pool = nn.AdaptiveMaxPool2d(1)
            # 轻量化融合权重
            self.fusion_weight = nn.Parameter(torch.tensor(0.5))

    def forward(self, x):
        # 主分支：平均池化
        y_avg = self.avg_pool(x).squeeze(-1).transpose(-1, -2)
        y_avg = self.conv(y_avg).transpose(-1, -2).unsqueeze(-1)

        if self.use_max_pool:
            # 可选的最大池化增强分支
            y_max = self.max_pool(x).squeeze(-1).transpose(-1, -2)
            y_max = self.conv(y_max).transpose(-1, -2).unsqueeze(-1)

            # 自适应融合
            fusion_weight = torch.sigmoid(self.fusion_weight)
            attention_weight = self.sigmoid(fusion_weight * y_avg + (1 - fusion_weight) * y_max)
        else:
            # 单分支注意力
            attention_weight = self.sigmoid(y_avg)

        return x * attention_weight.expand_as(x)


class eca_layer_2d_v2(nn.Module):
    """Constructs a ECA module.

    Args:
        channel: Number of channels of the input feature map
        k_size: Adaptive selection of kernel size
    """

    def __init__(self, channel):
        super(eca_layer_2d_v2, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        t = int(abs(math.log(channel,2)+1)/2)
        k_size = t if t%2 else (t+1)
        self.conv = nn.Conv1d(1, 1, kernel_size=k_size, padding=(k_size - 1) // 2, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        y_avg = self.avg_pool(x)
        y_max = self.max_pool(x)

        y_avg = self.conv(y_avg.squeeze(-1).transpose(-1, -2)).transpose(-1, -2).unsqueeze(-1)
        y_max = self.conv(y_max.squeeze(-1).transpose(-1, -2)).transpose(-1, -2).unsqueeze(-1)

        y = self.sigmoid(y_avg+y_max)

        return x * y.expand_as(x)

class SKD_TSTSAN(nn.Module):
    def __init__(self, out_channels=5, amp_factor=5, use_pinwheel_conv=False, use_wtconv=False,
                 use_wtrfaconv=False, use_wtprfaconv=False, use_wtprfaconv_lite=False,
                 use_legm=False, use_eca_enhanced=False, use_cross_branch_fusion=False,
                 use_learnable_tsm=False, wt_type='db4', wt_levels=1):
        super(SKD_TSTSAN, self).__init__()
        self.use_pinwheel_conv = use_pinwheel_conv
        self.use_wtconv = use_wtconv
        self.use_wtrfaconv = use_wtrfaconv
        self.use_wtprfaconv = use_wtprfaconv
        self.use_wtprfaconv_lite = use_wtprfaconv_lite
        self.use_legm = use_legm
        self.use_eca_enhanced = use_eca_enhanced
        self.use_cross_branch_fusion = use_cross_branch_fusion  # 跨分支交互开关
        self.use_learnable_tsm = use_learnable_tsm  # 可学习时间移位开关
        self.wt_type = wt_type  # 小波类型参数
        self.wt_levels = wt_levels  # 小波变换层数参数

        # 打印小波配置信息
        if use_wtconv or use_wtrfaconv or use_wtprfaconv or use_wtprfaconv_lite:
            print(f"🌊 小波配置: 类型={wt_type}, 层数={wt_levels}")

        # 打印跨分支交互配置信息
        if use_cross_branch_fusion:
            print("🔗 启用动态跨分支特征交互机制 (DCBF) - 增强分支间信息流动")

        # 打印可学习时间移位配置信息
        if use_learnable_tsm:
            print("🔄 启用可学习时间移位模块 (LTSM) - 自适应时间建模策略")

        # 确保只有一种卷积类型被激活
        conv_types = [use_pinwheel_conv, use_wtconv, use_wtrfaconv, use_wtprfaconv, use_wtprfaconv_lite]
        if sum(conv_types) > 1:
            raise ValueError("只能同时启用一种卷积类型：use_pinwheel_conv, use_wtconv, use_wtrfaconv, use_wtprfaconv, use_wtprfaconv_lite")
        self.Aug_Encoder_L = MagEncoder_No_texture(dim_in=16)
        self.Aug_Encoder_S = MagEncoder_No_texture(dim_in=1)
        self.Aug_Encoder_T = MagEncoder_No_texture(dim_in=2)
        self.Aug_Manipulator_L = MagManipulator()
        self.Aug_Manipulator_S = MagManipulator()
        self.Aug_Manipulator_T = MagManipulator()

        # 根据配置选择不同的卷积层类型
        if self.use_wtprfaconv_lite:
            # WTPRFAConv_Lite要求输入输出通道数相同，需要先用1x1卷积调整通道数
            self.conv1_L_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_S_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_T_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            # 使用轻量化WTPRFAConv，大幅减少参数数量
            self.conv1_L = WTPRFAConv_Lite(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv1_S = WTPRFAConv_Lite(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv1_T = WTPRFAConv_Lite(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
        elif self.use_wtprfaconv:
            # WTPRFAConv要求输入输出通道数相同，需要先用1x1卷积调整通道数
            self.conv1_L_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_S_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_T_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            # 使用WTPRFAConv进行小波变换风车形感受野注意力卷积，融合三种先进技术
            self.conv1_L = WTPRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv1_S = WTPRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv1_T = WTPRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
        elif self.use_wtrfaconv:
            # WTRFAConv要求输入输出通道数相同，需要先用1x1卷积调整通道数
            self.conv1_L_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_L_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_S_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_T_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            # 使用WTRFAConv进行小波变换感受野注意力卷积，结合多频响应和空间注意力
            self.conv1_L = WTRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3)
            self.conv1_S = WTRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3)
            self.conv1_T = WTRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3)
        elif self.use_wtconv:
            # WTConv要求输入输出通道数相同，需要先用1x1卷积调整通道数
            self.conv1_L_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_S_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_T_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            # 使用WTConv进行小波变换卷积，增强多频响应特征提取
            self.conv1_L = WTConv2d(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type)
            self.conv1_S = WTConv2d(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type)
            self.conv1_T = WTConv2d(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type)
        elif self.use_pinwheel_conv:
            # 风车形卷积要求输出通道数是4的倍数，这里使用64通道
            self.conv1_L = Pinwheel_shaped_Convolution(c1=32, c2=64, k=5, s=1)
            self.conv1_S = Pinwheel_shaped_Convolution(c1=32, c2=64, k=5, s=1)
            self.conv1_T = Pinwheel_shaped_Convolution(c1=32, c2=64, k=5, s=1)
        else:
            # 使用标准卷积
            self.conv1_L = nn.Conv2d(32, out_channels=64, kernel_size=5, stride=1)
            self.conv1_S = nn.Conv2d(32, out_channels=64, kernel_size=5, stride=1)
            self.conv1_T = nn.Conv2d(32, out_channels=64, kernel_size=5, stride=1)

        self.relu = nn.ReLU()
        self.bn1_L = nn.BatchNorm2d(64)
        self.bn1_S = nn.BatchNorm2d(64)
        self.bn1_T = nn.BatchNorm2d(64)
        self.maxpool = nn.MaxPool2d(kernel_size=5, stride=2, padding=2)

        self.AC1_conv1_L = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC1_conv1_S = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC1_conv1_T = self._create_temporal_shift_module(
            nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.AC1_bn1_L = nn.BatchNorm2d(128)
        self.AC1_bn1_S = nn.BatchNorm2d(128)
        self.AC1_bn1_T = nn.BatchNorm2d(128)

        self.AC1_conv2_L = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC1_conv2_S = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC1_conv2_T = self._create_temporal_shift_module(
            nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.AC1_bn2_L = nn.BatchNorm2d(128)
        self.AC1_bn2_S = nn.BatchNorm2d(128)
        self.AC1_bn2_T = nn.BatchNorm2d(128)
        self.AC1_pool = nn.AdaptiveAvgPool2d(1)
        self.AC1_fc = nn.Linear(in_features=384, out_features=out_channels)

        # conv2层根据配置选择不同的卷积类型
        if self.use_wtprfaconv_lite:
            # 使用轻量化WTPRFAConv进行第二层处理
            self.conv2_L = WTPRFAConv_Lite(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv2_S = WTPRFAConv_Lite(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            # 对于时序分支，使用时间移位模块包装的标准卷积
            self.conv2_T = self._create_temporal_shift_module(
                nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        elif self.use_wtprfaconv:
            # 使用WTPRFAConv进行第二层小波变换风车形感受野注意力卷积，进一步增强特征提取
            self.conv2_L = WTPRFAConv(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv2_S = WTPRFAConv(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            # 对于时序分支，使用时间移位模块包装的标准卷积，因为WTPRFAConv与时间移位的兼容性需要进一步验证
            self.conv2_T = self._create_temporal_shift_module(
                nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        elif self.use_wtrfaconv:
            # 使用WTRFAConv进行第二层小波变换感受野注意力卷积，进一步增强频域和空间特征提取
            self.conv2_L = WTRFAConv(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3)
            self.conv2_S = WTRFAConv(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3)
            # 对于时序分支，使用时间移位模块包装的标准卷积，因为WTRFAConv与时间移位的兼容性需要进一步验证
            self.conv2_T = self._create_temporal_shift_module(
                nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        elif self.use_wtconv:
            # 使用WTConv进行第二层小波变换卷积，进一步增强频域特征提取
            self.conv2_L = WTConv2d(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type)
            self.conv2_S = WTConv2d(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type)
            # 对于时序分支，使用时间移位模块包装的标准卷积，因为WTConv与时间移位的兼容性需要进一步验证
            self.conv2_T = self._create_temporal_shift_module(
                nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        elif self.use_pinwheel_conv:
            self.conv2_L = Pinwheel_shaped_Convolution(c1=64, c2=64, k=3, s=1)
            self.conv2_S = Pinwheel_shaped_Convolution(c1=64, c2=64, k=3, s=1)
            # 对于时序分支，使用时间移位模块包装的标准卷积，因为风车形卷积与时间移位的兼容性需要进一步验证
            self.conv2_T = self._create_temporal_shift_module(
                nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        else:
            self.conv2_L = nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1)
            self.conv2_S = nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1)
            self.conv2_T = self._create_temporal_shift_module(
                nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.bn2_L = nn.BatchNorm2d(64)
        self.bn2_S = nn.BatchNorm2d(64)
        self.bn2_T = nn.BatchNorm2d(64)

        self.conv3_L = nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1)
        self.conv3_S = nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1)
        self.conv3_T = self._create_temporal_shift_module(
            nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.bn3_L = nn.BatchNorm2d(64)
        self.bn3_S = nn.BatchNorm2d(64)
        self.bn3_T = nn.BatchNorm2d(64)

        self.avgpool = nn.AvgPool2d(kernel_size=3, stride=2, padding=1)

        self.AC2_conv1_L = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC2_conv1_S = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC2_conv1_T = self._create_temporal_shift_module(
            nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.AC2_bn1_L = nn.BatchNorm2d(128)
        self.AC2_bn1_S = nn.BatchNorm2d(128)
        self.AC2_bn1_T = nn.BatchNorm2d(128)

        self.AC2_conv2_L = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC2_conv2_S = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC2_conv2_T = self._create_temporal_shift_module(
            nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.AC2_bn2_L = nn.BatchNorm2d(128)
        self.AC2_bn2_S = nn.BatchNorm2d(128)
        self.AC2_bn2_T = nn.BatchNorm2d(128)
        self.AC2_pool = nn.AdaptiveAvgPool2d(1)
        self.AC2_fc = nn.Linear(in_features=384, out_features=out_channels)

        self.all_avgpool = nn.AdaptiveAvgPool2d(1)
        self.conv4_L = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.conv4_S = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.conv4_T = self._create_temporal_shift_module(
            nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.bn4_L = nn.BatchNorm2d(128)
        self.bn4_S = nn.BatchNorm2d(128)
        self.bn4_T = nn.BatchNorm2d(128)

        self.conv5_L = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.conv5_S = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.conv5_T = self._create_temporal_shift_module(
            nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.bn5_L = nn.BatchNorm2d(128)
        self.bn5_S = nn.BatchNorm2d(128)
        self.bn5_T = nn.BatchNorm2d(128)

        self.fc2 = nn.Linear(in_features=384, out_features=out_channels)

        # ECA注意力模块 - 根据选择使用标准ECA、轻量化ECA或增强型ECA
        if self.use_eca_enhanced:
            # 使用轻量化ECA模块（新的默认选择）
            self.ECA1 = ECA_Lightweight(64, use_max_pool=False)
            self.ECA2 = ECA_Lightweight(64, use_max_pool=False)
            self.ECA3 = ECA_Lightweight(64, use_max_pool=False)
            self.ECA4 = ECA_Lightweight(128, use_max_pool=False)
            self.ECA5 = ECA_Lightweight(128, use_max_pool=False)

            self.AC1_ECA1 = ECA_Lightweight(128, use_max_pool=False)
            self.AC1_ECA2 = ECA_Lightweight(128, use_max_pool=False)
            self.AC2_ECA1 = ECA_Lightweight(128, use_max_pool=False)
            self.AC2_ECA2 = ECA_Lightweight(128, use_max_pool=False)
            print("🔥 使用轻量化ECA注意力机制 - 单分支高效特征重标定，参数减少50%")
        else:
            # 使用标准ECA模块
            self.ECA1 = eca_layer_2d_v2(64)
            self.ECA2 = eca_layer_2d_v2(64)
            self.ECA3 = eca_layer_2d_v2(64)
            self.ECA4 = eca_layer_2d_v2(128)
            self.ECA5 = eca_layer_2d_v2(128)

            self.AC1_ECA1 = eca_layer_2d_v2(128)
            self.AC1_ECA2 = eca_layer_2d_v2(128)
            self.AC2_ECA1 = eca_layer_2d_v2(128)
            self.AC2_ECA2 = eca_layer_2d_v2(128)

        # 轻量化跨分支特征交互模块配置
        if self.use_cross_branch_fusion:
            # 仅在关键位置设置轻量化跨分支交互模块，减少计算开销
            self.lcbf_layer1 = LightweightCrossBranchFusion(channels=64, num_branches=3, reduction_ratio=8)  # conv2后
            self.lcbf_layer2 = LightweightCrossBranchFusion(channels=128, num_branches=3, reduction_ratio=8)  # conv4后
            print("🔗 轻量化跨分支交互模块已配置: 2个交互层级 (64→128通道), 参数减少60%")

        # LEGM模块配置 - 仅在位置1使用，用于增强初期特征提取（低层纹理和边缘特征）
        if self.use_legm:
            # 使用标准LEGM
            # LEGM位置1：第一个卷积层后，增强初期特征提取
            self.legm1_L = LEGM(network_depth=6, dim=64, depth=2, num_heads=4,
                               window_size=8, attn_ratio=0.5, attn_loc='last', conv_type='DWConv')
            self.legm1_S = LEGM(network_depth=6, dim=64, depth=2, num_heads=4,
                               window_size=8, attn_ratio=0.5, attn_loc='last', conv_type='DWConv')

        self.amp_factor = amp_factor

        self.consensus = ConsensusModule("avg")

        self.dropout = nn.Dropout(0.2)

    def _create_temporal_shift_module(self, conv_layer, n_segment=2, n_div=8):
        """
        创建时间移位模块的辅助方法

        Args:
            conv_layer: 要包装的卷积层
            n_segment: 时间段数
            n_div: 通道分割比例

        Returns:
            时间移位模块 (传统TSM或轻量化可学习TSM)
        """
        if self.use_learnable_tsm:
            return LightweightTemporalShift(conv_layer, n_segment=n_segment, n_div=n_div,
                                          learnable_shifts=True, group_size=8)
        else:
            return TemporalShift(conv_layer, n_segment=n_segment, n_div=n_div, inplace=False)

    def forward(self, input):
        x1 = input[:, 2:18, :, :]
        x1_onset = input[:, 18:34, :, :]
        x2 = input[:, 0, :, :].unsqueeze(dim=1)
        x2_onset = input[:, 1, :, :].unsqueeze(dim=1)
        x3 = input[:, 34:, :, :]

        bsz = x1.shape[0]

        x3 = torch.reshape(x3, (bsz * 2, 2, 48, 48))

        # 根据输入设备创建相应的零张量
        device = x3.device
        x3_onset = torch.zeros(bsz * 2, 2, 48, 48, device=device)

        motion_x1_onset = self.Aug_Encoder_L(x1_onset)
        motion_x1 = self.Aug_Encoder_L(x1)
        x1 = self.Aug_Manipulator_L(motion_x1_onset, motion_x1, self.amp_factor)
        motion_x2_onset = self.Aug_Encoder_S(x2_onset)
        motion_x2 = self.Aug_Encoder_S(x2)
        x2 = self.Aug_Manipulator_S(motion_x2_onset, motion_x2, self.amp_factor)
        motion_x3_onset = self.Aug_Encoder_T(x3_onset)
        motion_x3 = self.Aug_Encoder_T(x3)
        x3 = self.Aug_Manipulator_T(motion_x3_onset, motion_x3, self.amp_factor)

        # 根据配置选择不同的卷积处理方式
        if self.use_wtprfaconv_lite or self.use_wtprfaconv or self.use_wtrfaconv or self.use_wtconv:
            # WTPRFAConv_Lite、WTPRFAConv、WTRFAConv和WTConv都需要先调整通道数
            x1 = self.conv1_L_pre(x1)
            x1 = self.conv1_L(x1)
            x1 = self.bn1_L(x1)
            x1 = self.relu(x1)
            # LEGM位置1：第一个卷积层后，增强初期特征提取
            if self.use_legm:
                x1 = self.legm1_L(x1)
            else:
                x1 = self.ECA1(x1)
            x1 = self.maxpool(x1)

            x2 = self.conv1_S_pre(x2)
            x2 = self.conv1_S(x2)
            x2 = self.bn1_S(x2)
            x2 = self.relu(x2)
            # LEGM位置1：第一个卷积层后，增强初期特征提取
            if self.use_legm:
                x2 = self.legm1_S(x2)
            x2 = self.maxpool(x2)

            x3 = self.conv1_T_pre(x3)
            x3 = self.conv1_T(x3)
            x3 = self.bn1_T(x3)
            x3 = self.relu(x3)
            x3 = self.maxpool(x3)
        else:
            # 标准卷积或风车形卷积
            x1 = self.conv1_L(x1)
            x1 = self.bn1_L(x1)
            x1 = self.relu(x1)
            # LEGM位置1：第一个卷积层后，增强初期特征提取
            if self.use_legm:
                x1 = self.legm1_L(x1)
            else:
                x1 = self.ECA1(x1)

            x2 = self.conv1_S(x2)
            x2 = self.bn1_S(x2)
            x2 = self.relu(x2)
            # LEGM位置1：第一个卷积层后，增强初期特征提取
            if self.use_legm:
                x2 = self.legm1_S(x2)

            x3 = self.conv1_T(x3)
            x3 = self.bn1_T(x3)
            x3 = self.relu(x3)

            # 移除第一层跨分支交互，减少计算开销
            # 原有的第一层交互被移除，以提升效率

            # 应用maxpool
            x1 = self.maxpool(x1)
            x2 = self.maxpool(x2)
            x3 = self.maxpool(x3)

        AC1_x1 = self.AC1_conv1_L(x1)
        AC1_x1 = self.AC1_bn1_L(AC1_x1)
        AC1_x1 = self.relu(AC1_x1)
        AC1_x1 = self.AC1_ECA1(AC1_x1)
        AC1_x1 = self.AC1_conv2_L(AC1_x1)
        AC1_x1 = self.AC1_bn2_L(AC1_x1)
        AC1_x1 = self.relu(AC1_x1)
        AC1_x1 = self.AC1_ECA2(AC1_x1)
        AC1_x1 = self.AC1_pool(AC1_x1)
        AC1_x1_all = AC1_x1.view(AC1_x1.size(0), -1)

        AC1_x2 = self.AC1_conv1_S(x2)
        AC1_x2 = self.AC1_bn1_S(AC1_x2)
        AC1_x2 = self.relu(AC1_x2)
        AC1_x2 = self.AC1_conv2_S(AC1_x2)
        AC1_x2 = self.AC1_bn2_S(AC1_x2)
        AC1_x2 = self.relu(AC1_x2)
        AC1_x2 = self.AC1_pool(AC1_x2)
        AC1_x2_all = AC1_x2.view(AC1_x2.size(0), -1)

        AC1_x3 = self.AC1_conv1_T(x3)
        AC1_x3 = self.AC1_bn1_T(AC1_x3)
        AC1_x3 = self.relu(AC1_x3)
        AC1_x3 = self.AC1_conv2_T(AC1_x3)
        AC1_x3 = self.AC1_bn2_T(AC1_x3)
        AC1_x3 = self.relu(AC1_x3)
        AC1_x3 = self.AC1_pool(AC1_x3)
        AC1_x3_all = AC1_x3.view(AC1_x3.size(0), -1)

        AC1_x3_all = AC1_x3_all.view((-1, 2) + AC1_x3_all.size()[1:])
        AC1_x3_all = self.consensus(AC1_x3_all)
        AC1_x3_all = AC1_x3_all.squeeze(1)
        AC1_feature = torch.cat((AC1_x1_all, AC1_x2_all, AC1_x3_all), 1)
        AC1_x_all = self.dropout(AC1_feature)
        AC1_x_all = self.AC1_fc(AC1_x_all)


        x1 = self.conv2_L(x1)
        x1 = self.bn2_L(x1)
        x1 = self.relu(x1)
        x1 = self.ECA2(x1)

        x2 = self.conv2_S(x2)
        x2 = self.bn2_S(x2)
        x2 = self.relu(x2)
        x2 = self.ECA2(x2)

        x3 = self.conv2_T(x3)
        x3 = self.bn2_T(x3)
        x3 = self.relu(x3)

        # 第一层轻量化跨分支特征交互 (在conv3之前进行)
        if self.use_cross_branch_fusion:
            # 处理batch size不匹配问题
            B_x3 = x3.shape[0]  # bsz * 2
            B_x1 = x1.shape[0]  # bsz

            if B_x3 != B_x1:
                # 重复x1和x2以匹配x3的batch size
                x1_expanded = x1.repeat(2, 1, 1, 1)
                x2_expanded = x2.repeat(2, 1, 1, 1)

                # 进行轻量化跨分支交互
                branch_features_1 = [x1_expanded, x2_expanded, x3]
                enhanced_features_1 = self.lcbf_layer1(branch_features_1)
                x1_enhanced, x2_enhanced, x3 = enhanced_features_1

                # 将x1和x2恢复到原始batch size
                x1 = (x1_enhanced[:B_x1] + x1_enhanced[B_x1:]) / 2
                x2 = (x2_enhanced[:B_x1] + x2_enhanced[B_x1:]) / 2
            else:
                branch_features_1 = [x1, x2, x3]
                enhanced_features_1 = self.lcbf_layer1(branch_features_1)
                x1, x2, x3 = enhanced_features_1

        x1 = self.conv3_L(x1)
        x1 = self.bn3_L(x1)
        x1 = self.relu(x1)
        x1 = self.ECA3(x1)
        x1 = self.avgpool(x1)

        x2 = self.conv3_S(x2)
        x2 = self.bn3_S(x2)
        x2 = self.relu(x2)
        x2 = self.avgpool(x2)

        x3 = self.conv3_T(x3)
        x3 = self.bn3_T(x3)
        x3 = self.relu(x3)
        x3 = self.avgpool(x3)

        AC2_x1 = self.AC2_conv1_L(x1)
        AC2_x1 = self.AC2_bn1_L(AC2_x1)
        AC2_x1 = self.relu(AC2_x1)
        AC2_x1 = self.AC2_ECA1(AC2_x1)
        AC2_x1 = self.AC2_conv2_L(AC2_x1)
        AC2_x1 = self.AC2_bn2_L(AC2_x1)
        AC2_x1 = self.relu(AC2_x1)
        AC2_x1 = self.AC2_ECA2(AC2_x1)
        AC2_x1 = self.AC2_pool(AC2_x1)
        AC2_x1_all = AC2_x1.view(AC2_x1.size(0), -1)

        AC2_x2 = self.AC2_conv1_S(x2)
        AC2_x2 = self.AC2_bn1_S(AC2_x2)
        AC2_x2 = self.relu(AC2_x2)
        AC2_x2 = self.AC2_conv2_S(AC2_x2)
        AC2_x2 = self.AC2_bn2_S(AC2_x2)
        AC2_x2 = self.relu(AC2_x2)
        AC2_x2 = self.AC2_pool(AC2_x2)
        AC2_x2_all = AC2_x2.view(AC2_x2.size(0), -1)

        AC2_x3 = self.AC2_conv1_T(x3)
        AC2_x3 = self.AC2_bn1_T(AC2_x3)
        AC2_x3 = self.relu(AC2_x3)
        AC2_x3 = self.AC2_conv2_T(AC2_x3)
        AC2_x3 = self.AC2_bn2_T(AC2_x3)
        AC2_x3 = self.relu(AC2_x3)
        AC2_x3 = self.AC2_pool(AC2_x3)
        AC2_x3_all = AC2_x3.view(AC2_x3.size(0), -1)

        AC2_x3_all = AC2_x3_all.view((-1, 2) + AC2_x3_all.size()[1:])
        AC2_x3_all = self.consensus(AC2_x3_all)
        AC2_x3_all = AC2_x3_all.squeeze(1)
        AC2_feature = torch.cat((AC2_x1_all, AC2_x2_all, AC2_x3_all), 1)
        AC2_x_all = self.dropout(AC2_feature)
        AC2_x_all = self.AC2_fc(AC2_x_all)


        x1 = self.conv4_L(x1)
        x1 = self.bn4_L(x1)
        x1 = self.relu(x1)
        x1 = self.ECA4(x1)

        x2 = self.conv4_S(x2)
        x2 = self.bn4_S(x2)
        x2 = self.relu(x2)

        x3 = self.conv4_T(x3)
        x3 = self.bn4_T(x3)
        x3 = self.relu(x3)

        # 第二层轻量化跨分支特征交互 (在conv5之前进行，128通道)
        if self.use_cross_branch_fusion:
            # 处理batch size不匹配问题
            B_x3 = x3.shape[0]  # bsz * 2
            B_x1 = x1.shape[0]  # bsz

            if B_x3 != B_x1:
                # 重复x1和x2以匹配x3的batch size
                x1_expanded = x1.repeat(2, 1, 1, 1)
                x2_expanded = x2.repeat(2, 1, 1, 1)

                # 进行轻量化跨分支交互
                branch_features_2 = [x1_expanded, x2_expanded, x3]
                enhanced_features_2 = self.lcbf_layer2(branch_features_2)
                x1_enhanced, x2_enhanced, x3 = enhanced_features_2

                # 将x1和x2恢复到原始batch size
                x1 = (x1_enhanced[:B_x1] + x1_enhanced[B_x1:]) / 2
                x2 = (x2_enhanced[:B_x1] + x2_enhanced[B_x1:]) / 2
            else:
                branch_features_2 = [x1, x2, x3]
                enhanced_features_2 = self.lcbf_layer2(branch_features_2)
                x1, x2, x3 = enhanced_features_2

        x1 = self.conv5_L(x1)
        x1 = self.bn5_L(x1)
        x1 = self.relu(x1)
        x1 = self.ECA5(x1)

        x1 = self.all_avgpool(x1)
        x1_all = x1.view(x1.size(0), -1)

        x2 = self.conv5_S(x2)
        x2 = self.bn5_S(x2)
        x2 = self.relu(x2)

        x2 = self.all_avgpool(x2)
        x2_all = x2.view(x2.size(0), -1)

        x3 = self.conv5_T(x3)
        x3 = self.bn5_T(x3)
        x3 = self.relu(x3)
        x3 = self.all_avgpool(x3)
        x3_all = x3.view(x3.size(0), -1)

        x3_all = x3_all.view((-1, 2) + x3_all.size()[1:])

        x3_all = self.consensus(x3_all)
        x3_all = x3_all.squeeze(1)

        final_feature = torch.cat((x1_all, x2_all, x3_all), 1)
        x_all = self.dropout(final_feature)
        x_all = self.fc2(x_all)
        return x_all, AC1_x_all, AC2_x_all, final_feature, AC1_feature, AC2_feature


def get_model(model_name, class_num, alpha, use_pinwheel_conv=False, use_wtconv=False,
              use_wtrfaconv=False, use_wtprfaconv=False, use_wtprfaconv_lite=False,
              use_legm=False, use_eca_enhanced=False, use_cross_branch_fusion=False,
              use_learnable_tsm=False, wt_type='db4', wt_levels=1):
    if model_name == "SKD_TSTSAN":
        return SKD_TSTSAN(out_channels=class_num, amp_factor=alpha,
                         use_pinwheel_conv=use_pinwheel_conv, use_wtconv=use_wtconv,
                         use_wtrfaconv=use_wtrfaconv, use_wtprfaconv=use_wtprfaconv,
                         use_wtprfaconv_lite=use_wtprfaconv_lite, use_legm=use_legm,
                         use_eca_enhanced=use_eca_enhanced, use_cross_branch_fusion=use_cross_branch_fusion,
                         use_learnable_tsm=use_learnable_tsm, wt_type=wt_type, wt_levels=wt_levels)


def get_model_by_conv_type(model_name, class_num, alpha, conv_type=1, use_legm=False,
                          use_eca_enhanced=False, use_cross_branch_fusion=False,
                          use_learnable_tsm=False, wt_type='db4', wt_levels=1):
    """
    通过卷积类型数字选择创建模型

    Args:
        model_name: 模型名称
        class_num: 分类数量
        alpha: 放大因子
        conv_type: 卷积类型选择
            1 - 标准卷积
            2 - 风车形卷积
            3 - 小波变换卷积
            4 - 小波变换感受野注意力卷积
            5 - 小波变换风车形感受野注意力卷积 (原版，参数多)
            6 - 小波变换风车形感受野注意力卷积 (轻量化版本)
        use_legm: 是否使用标准LEGM模块进行局部-全局特征增强
        use_cross_branch_fusion: 是否使用动态跨分支特征交互机制
        use_learnable_tsm: 是否使用可学习时间移位模块
        wt_type: 小波类型 (仅对小波相关卷积有效)
        wt_levels: 小波变换层数 (仅对小波相关卷积有效)

    Returns:
        模型实例
    """
    conv_configs = {
        1: {"use_pinwheel_conv": False, "use_wtconv": False, "use_wtrfaconv": False, "use_wtprfaconv": False, "use_wtprfaconv_lite": False},
        2: {"use_pinwheel_conv": True, "use_wtconv": False, "use_wtrfaconv": False, "use_wtprfaconv": False, "use_wtprfaconv_lite": False},
        3: {"use_pinwheel_conv": False, "use_wtconv": True, "use_wtrfaconv": False, "use_wtprfaconv": False, "use_wtprfaconv_lite": False},
        4: {"use_pinwheel_conv": False, "use_wtconv": False, "use_wtrfaconv": True, "use_wtprfaconv": False, "use_wtprfaconv_lite": False},
        5: {"use_pinwheel_conv": False, "use_wtconv": False, "use_wtrfaconv": False, "use_wtprfaconv": True, "use_wtprfaconv_lite": False},
        6: {"use_pinwheel_conv": False, "use_wtconv": False, "use_wtrfaconv": False, "use_wtprfaconv": False, "use_wtprfaconv_lite": True},
    }

    if conv_type not in conv_configs:
        raise ValueError(f"无效的卷积类型: {conv_type}. 请选择1-6之间的数字。")

    config = conv_configs[conv_type]

    return get_model(
        model_name=model_name,
        class_num=class_num,
        alpha=alpha,
        use_legm=use_legm,
        use_eca_enhanced=use_eca_enhanced,
        use_cross_branch_fusion=use_cross_branch_fusion,
        use_learnable_tsm=use_learnable_tsm,
        wt_type=wt_type,
        wt_levels=wt_levels,
        **config
    )