
================================================================================
【训练开始】
================================================================================

【硬件信息】使用单个GPU进行训练
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

【损失函数配置】
使用加权Focal Loss (4分类)
类别权重: ['0.4082', '0.1651', '0.2041', '0.2226']
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: S30
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 4
跳过fear/sadness类别测试数据: 3

S30测试标签: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3]
创建受试者目录: S30
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积1_class4_conv1_legm1/S30/logs
正在初始化 标准卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 使用标准卷积层
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.5734

开始验证...
验证集准确率: 0.5714 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.5714
当前主体训练完成
最佳预测结果: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 0, 3, 3, 3, 0, 3, 3, 0, 3, 0]
真实标签: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 4
TP: 4, FP: 6, FN: 0, TN: 25
F1分数: 0.5714, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 14
TP: 13, FP: 0, FN: 1, TN: 21
F1分数: 0.9630, 召回率: 0.9286

厌恶(disgust)评估结果:
样本数: 12
TP: 0, FP: 0, FN: 12, TN: 23
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 5
TP: 3, FP: 9, FN: 2, TN: 21
F1分数: 0.3529, 召回率: 0.6000

总体评估结果:
UF1: 0.4718
UAR: 0.6321

快乐(happiness)评估结果:
样本数: 4
TP: 4, FP: 6, FN: 0, TN: 25
F1分数: 0.5714, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 14
TP: 13, FP: 0, FN: 1, TN: 21
F1分数: 0.9630, 召回率: 0.9286

厌恶(disgust)评估结果:
样本数: 12
TP: 0, FP: 0, FN: 12, TN: 23
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 5
TP: 3, FP: 9, FN: 2, TN: 21
F1分数: 0.3529, 召回率: 0.6000

总体评估结果:
UF1: 0.4718
UAR: 0.6321
UF1: 0.4718 | UAR: 0.6321
最佳 UF1: 0.4718 | 最佳 UAR: 0.6321

【数据增强统计】
标签 0: 增强了 224 个样本
标签 1: 增强了 150 个样本
标签 2: 增强了 180 个样本
标签 3: 增强了 183 个样本

========================================
【当前处理受试者】: S06
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 3

S06测试标签: [0, 0, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
创建受试者目录: S06
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积1_class4_conv1_legm1/S06/logs
正在初始化 标准卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 使用标准卷积层
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.5203

开始验证...
验证集准确率: 0.4706 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.4706
当前主体训练完成
最佳预测结果: [0, 0, 3, 3, 3, 3, 0, 3, 0, 3, 3, 3, 3, 0, 0, 3, 0]
真实标签: [0, 0, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 2
TP: 2, FP: 5, FN: 0, TN: 10
F1分数: 0.4444, 召回率: 1.0000
惊讶(surprise): 没有样本

厌恶(disgust)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 13
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 11
TP: 6, FP: 4, FN: 5, TN: 2
F1分数: 0.5714, 召回率: 0.5455

总体评估结果:
UF1: 0.3386
UAR: 0.5152

快乐(happiness)评估结果:
样本数: 2
TP: 2, FP: 5, FN: 0, TN: 10
F1分数: 0.4444, 召回率: 1.0000
惊讶(surprise): 没有样本

厌恶(disgust)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 13
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 11
TP: 6, FP: 4, FN: 5, TN: 2
F1分数: 0.5714, 召回率: 0.5455

总体评估结果:
UF1: 0.3386
UAR: 0.5152
UF1: 0.4896 | UAR: 0.6228
最佳 UF1: 0.4896 | 最佳 UAR: 0.6228

【数据增强统计】
标签 0: 增强了 462 个样本
标签 1: 增强了 328 个样本
标签 2: 增强了 384 个样本
标签 3: 增强了 348 个样本

========================================
【当前处理受试者】: S13
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 3

S13测试标签: [1, 1, 1, 1, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 3]
创建受试者目录: S13
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积1_class4_conv1_legm1/S13/logs
正在初始化 标准卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 使用标准卷积层
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.5297

开始验证...
验证集准确率: 0.4000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.4000
当前主体训练完成
最佳预测结果: [1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0]
真实标签: [1, 1, 1, 1, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 3
TP: 3, FP: 8, FN: 0, TN: 4
F1分数: 0.4286, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 4
TP: 3, FP: 1, FN: 1, TN: 10
F1分数: 0.7500, 召回率: 0.7500

厌恶(disgust)评估结果:
样本数: 7
TP: 0, FP: 0, FN: 7, TN: 8
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 14
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.2946
UAR: 0.4375

快乐(happiness)评估结果:
样本数: 3
TP: 3, FP: 8, FN: 0, TN: 4
F1分数: 0.4286, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 4
TP: 3, FP: 1, FN: 1, TN: 10
F1分数: 0.7500, 召回率: 0.7500

厌恶(disgust)评估结果:
样本数: 7
TP: 0, FP: 0, FN: 7, TN: 8
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 14
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.2946
UAR: 0.4375
UF1: 0.4656 | UAR: 0.6046
最佳 UF1: 0.4656 | 最佳 UAR: 0.6046

【数据增强统计】
标签 0: 增强了 693 个样本
标签 1: 增强了 498 个样本
标签 2: 增强了 579 个样本
标签 3: 增强了 543 个样本

========================================
【当前处理受试者】: S09
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 3

S09测试标签: [1, 1, 1, 1, 1, 0, 2, 2, 2, 2, 2, 2, 2, 3]
创建受试者目录: S09
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积1_class4_conv1_legm1/S09/logs
正在初始化 标准卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 使用标准卷积层
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.4828

开始验证...
验证集准确率: 0.0714 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.0714
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
真实标签: [1, 1, 1, 1, 1, 0, 2, 2, 2, 2, 2, 2, 2, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 1
TP: 1, FP: 13, FN: 0, TN: 0
F1分数: 0.1333, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 9
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 7
TP: 0, FP: 0, FN: 7, TN: 7
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 13
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0333
UAR: 0.2500

快乐(happiness)评估结果:
样本数: 1
TP: 1, FP: 13, FN: 0, TN: 0
F1分数: 0.1333, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 9
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 7
TP: 0, FP: 0, FN: 7, TN: 7
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 13
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0333
UAR: 0.2500
UF1: 0.4087 | UAR: 0.5489
最佳 UF1: 0.4087 | 最佳 UAR: 0.5489

【数据增强统计】
标签 0: 增强了 938 个样本
标签 1: 增强了 666 个样本
标签 2: 增强了 774 个样本
标签 3: 增强了 738 个样本

========================================
【当前处理受试者】: S10
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 4
跳过fear/sadness类别测试数据: 3

S10测试标签: [1, 1, 0, 0, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3]
创建受试者目录: S10
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积1_class4_conv1_legm1/S10/logs
正在初始化 标准卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 使用标准卷积层
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.5312

开始验证...
验证集准确率: 0.3571 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.3571
当前主体训练完成
最佳预测结果: [1, 1, 0, 0, 0, 1, 1, 2, 0, 0, 2, 0, 0, 1]
真实标签: [1, 1, 0, 0, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 2
TP: 2, FP: 5, FN: 0, TN: 7
F1分数: 0.4444, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 2, FP: 3, FN: 0, TN: 9
F1分数: 0.5714, 召回率: 1.0000

厌恶(disgust)评估结果:
样本数: 6
TP: 1, FP: 1, FN: 5, TN: 7
F1分数: 0.2500, 召回率: 0.1667

其他(others)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 10
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.3165
UAR: 0.5417

快乐(happiness)评估结果:
样本数: 2
TP: 2, FP: 5, FN: 0, TN: 7
F1分数: 0.4444, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 2, FP: 3, FN: 0, TN: 9
F1分数: 0.5714, 召回率: 1.0000

厌恶(disgust)评估结果:
样本数: 6
TP: 1, FP: 1, FN: 5, TN: 7
F1分数: 0.2500, 召回率: 0.1667

其他(others)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 10
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.3165
UAR: 0.5417
UF1: 0.4053 | UAR: 0.5392
最佳 UF1: 0.4053 | 最佳 UAR: 0.5392

【数据增强统计】
标签 0: 增强了 1176 个样本
标签 1: 增强了 840 个样本
标签 2: 增强了 972 个样本
标签 3: 增强了 924 个样本

========================================
【当前处理受试者】: S16
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...

S16测试标签: [1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3]
创建受试者目录: S16
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积1_class4_conv1_legm1/S16/logs
正在初始化 标准卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 使用标准卷积层
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.5531

开始验证...
验证集准确率: 0.3571 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.3571
当前主体训练完成
最佳预测结果: [1, 1, 3, 0, 3, 0, 0, 2, 3, 3, 3, 3, 3, 3]
真实标签: [1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3]
当前评估结果:
快乐(happiness): 没有样本

惊讶(surprise)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 12
F1分数: 1.0000, 召回率: 1.0000

厌恶(disgust)评估结果:
样本数: 10
TP: 1, FP: 0, FN: 9, TN: 4
F1分数: 0.1818, 召回率: 0.1000

其他(others)评估结果:
样本数: 2
TP: 2, FP: 6, FN: 0, TN: 6
F1分数: 0.4000, 召回率: 1.0000

总体评估结果:
UF1: 0.5273
UAR: 0.7000
快乐(happiness): 没有样本

惊讶(surprise)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 12
F1分数: 1.0000, 召回率: 1.0000

厌恶(disgust)评估结果:
样本数: 10
TP: 1, FP: 0, FN: 9, TN: 4
F1分数: 0.1818, 召回率: 0.1000

其他(others)评估结果:
样本数: 2
TP: 2, FP: 6, FN: 0, TN: 6
F1分数: 0.4000, 召回率: 1.0000

总体评估结果:
UF1: 0.5273
UAR: 0.7000
UF1: 0.4121 | UAR: 0.5606
最佳 UF1: 0.4121 | 最佳 UAR: 0.5606

【数据增强统计】
标签 0: 增强了 1428 个样本
标签 1: 增强了 1014 个样本
标签 2: 增强了 1158 个样本
标签 3: 增强了 1116 个样本

========================================
【当前处理受试者】: S05
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 3

S05测试标签: [1, 1, 0, 0, 2, 2, 2, 2, 3, 3, 3, 3]
创建受试者目录: S05
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积1_class4_conv1_legm1/S05/logs
正在初始化 标准卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 使用标准卷积层
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.5141

开始验证...
验证集准确率: 0.5000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.5000
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 0, 0, 3, 0, 3, 3, 3, 3]
真实标签: [1, 1, 0, 0, 2, 2, 2, 2, 3, 3, 3, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 2
TP: 2, FP: 5, FN: 0, TN: 5
F1分数: 0.4444, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 10
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 8
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 4
TP: 4, FP: 1, FN: 0, TN: 7
F1分数: 0.8889, 召回率: 1.0000

总体评估结果:
UF1: 0.3333
UAR: 0.5000

快乐(happiness)评估结果:
样本数: 2
TP: 2, FP: 5, FN: 0, TN: 5
F1分数: 0.4444, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 10
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 8
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 4
TP: 4, FP: 1, FN: 0, TN: 7
F1分数: 0.8889, 召回率: 1.0000

总体评估结果:
UF1: 0.3333
UAR: 0.5000
UF1: 0.4225 | UAR: 0.5663
最佳 UF1: 0.4225 | 最佳 UAR: 0.5663

【数据增强统计】
标签 0: 增强了 1666 个样本
标签 1: 增强了 1188 个样本
标签 2: 增强了 1362 个样本
标签 3: 增强了 1302 个样本

========================================
【当前处理受试者】: S11
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 4

S11测试标签: [1, 1, 1, 1, 1, 0, 2, 2, 2, 2, 3]
创建受试者目录: S11
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积1_class4_conv1_legm1/S11/logs
正在初始化 标准卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 使用标准卷积层
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.4766

开始验证...
验证集准确率: 0.3636 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.3636
当前主体训练完成
最佳预测结果: [0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 3]
真实标签: [1, 1, 1, 1, 1, 0, 2, 2, 2, 2, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 1
TP: 1, FP: 7, FN: 0, TN: 3
F1分数: 0.2222, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 2, FP: 0, FN: 3, TN: 6
F1分数: 0.5714, 召回率: 0.4000

厌恶(disgust)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 7
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 10
F1分数: 1.0000, 召回率: 1.0000

总体评估结果:
UF1: 0.4484
UAR: 0.6000

快乐(happiness)评估结果:
样本数: 1
TP: 1, FP: 7, FN: 0, TN: 3
F1分数: 0.2222, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 2, FP: 0, FN: 3, TN: 6
F1分数: 0.5714, 召回率: 0.4000

厌恶(disgust)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 7
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 10
F1分数: 1.0000, 召回率: 1.0000

总体评估结果:
UF1: 0.4484
UAR: 0.6000
UF1: 0.4154 | UAR: 0.559
最佳 UF1: 0.4154 | 最佳 UAR: 0.559

【数据增强统计】
标签 0: 增强了 1911 个样本
标签 1: 增强了 1356 个样本
标签 2: 增强了 1566 个样本
标签 3: 增强了 1497 个样本

========================================
【当前处理受试者】: S21
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 3

S21测试标签: [1, 1, 1, 1, 1, 0, 0, 0, 3, 3, 3, 3]
创建受试者目录: S21
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积1_class4_conv1_legm1/S21/logs
正在初始化 标准卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 使用标准卷积层
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.4875

开始验证...
验证集准确率: 0.6667 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.6667
当前主体训练完成
最佳预测结果: [0, 1, 1, 1, 1, 0, 0, 0, 0, 3, 0, 0]
真实标签: [1, 1, 1, 1, 1, 0, 0, 0, 3, 3, 3, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 3
TP: 3, FP: 4, FN: 0, TN: 5
F1分数: 0.6000, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 4, FP: 0, FN: 1, TN: 7
F1分数: 0.8889, 召回率: 0.8000
厌恶(disgust): 没有样本

其他(others)评估结果:
样本数: 4
TP: 1, FP: 0, FN: 3, TN: 8
F1分数: 0.4000, 召回率: 0.2500

总体评估结果:
UF1: 0.6296
UAR: 0.6833

快乐(happiness)评估结果:
样本数: 3
TP: 3, FP: 4, FN: 0, TN: 5
F1分数: 0.6000, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 4, FP: 0, FN: 1, TN: 7
F1分数: 0.8889, 召回率: 0.8000
厌恶(disgust): 没有样本

其他(others)评估结果:
样本数: 4
TP: 1, FP: 0, FN: 3, TN: 8
F1分数: 0.4000, 召回率: 0.2500

总体评估结果:
UF1: 0.6296
UAR: 0.6833
UF1: 0.4252 | UAR: 0.5547
最佳 UF1: 0.4252 | 最佳 UAR: 0.5547

【数据增强统计】
标签 0: 增强了 2142 个样本
标签 1: 增强了 1524 个样本
标签 2: 增强了 1782 个样本
标签 3: 增强了 1683 个样本

========================================
【当前处理受试者】: S15
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 4
跳过fear/sadness类别测试数据: 3

S15测试标签: [1, 1, 1, 0, 0, 0, 2, 3, 3, 3]
创建受试者目录: S15
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积1_class4_conv1_legm1/S15/logs
正在初始化 标准卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 使用标准卷积层
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.5000

开始验证...
验证集准确率: 0.6000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.6000
当前主体训练完成
最佳预测结果: [0, 0, 1, 0, 0, 0, 0, 3, 3, 0]
真实标签: [1, 1, 1, 0, 0, 0, 2, 3, 3, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 3
TP: 3, FP: 4, FN: 0, TN: 3
F1分数: 0.6000, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 3
TP: 1, FP: 0, FN: 2, TN: 7
F1分数: 0.5000, 召回率: 0.3333

厌恶(disgust)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 9
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 3
TP: 2, FP: 0, FN: 1, TN: 7
F1分数: 0.8000, 召回率: 0.6667

总体评估结果:
UF1: 0.4750
UAR: 0.5000

快乐(happiness)评估结果:
样本数: 3
TP: 3, FP: 4, FN: 0, TN: 3
F1分数: 0.6000, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 3
TP: 1, FP: 0, FN: 2, TN: 7
F1分数: 0.5000, 召回率: 0.3333

厌恶(disgust)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 9
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 3
TP: 2, FP: 0, FN: 1, TN: 7
F1分数: 0.8000, 召回率: 0.6667

总体评估结果:
UF1: 0.4750
UAR: 0.5000
UF1: 0.4318 | UAR: 0.5517
最佳 UF1: 0.4318 | 最佳 UAR: 0.5517

【数据增强统计】
标签 0: 增强了 2373 个样本
标签 1: 增强了 1696 个样本
标签 2: 增强了 1995 个样本
标签 3: 增强了 1872 个样本

========================================
【当前处理受试者】: S03
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...

S03测试标签: [1, 1, 1, 1, 1, 1, 1, 3, 3, 3, 3, 3]
创建受试者目录: S03
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积1_class4_conv1_legm1/S03/logs
正在初始化 标准卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 使用标准卷积层
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.4781

开始验证...
验证集准确率: 0.0833 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.0833
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 0, 1, 0, 0, 2, 0, 2, 2]
真实标签: [1, 1, 1, 1, 1, 1, 1, 3, 3, 3, 3, 3]
当前评估结果:
快乐(happiness): 没有样本

惊讶(surprise)评估结果:
样本数: 7
TP: 1, FP: 0, FN: 6, TN: 5
F1分数: 0.2500, 召回率: 0.1429
厌恶(disgust): 没有样本

其他(others)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 7
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.1250
UAR: 0.0714
快乐(happiness): 没有样本

惊讶(surprise)评估结果:
样本数: 7
TP: 1, FP: 0, FN: 6, TN: 5
F1分数: 0.2500, 召回率: 0.1429
厌恶(disgust): 没有样本

其他(others)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 7
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.1250
UAR: 0.0714
UF1: 0.4034 | UAR: 0.5178
最佳 UF1: 0.4034 | 最佳 UAR: 0.5178

【数据增强统计】
标签 0: 增强了 2625 个样本
标签 1: 增强了 1860 个样本
标签 2: 增强了 2211 个样本
标签 3: 增强了 2055 个样本

========================================
【当前处理受试者】: S08
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 3

S08测试标签: [1, 0, 0, 0, 0, 0, 2, 2, 3, 3]
创建受试者目录: S08
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积1_class4_conv1_legm1/S08/logs
正在初始化 标准卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 使用标准卷积层
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.5328

开始验证...
验证集准确率: 0.7000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.7000
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 0, 0, 2, 2, 0, 2]
真实标签: [1, 0, 0, 0, 0, 0, 2, 2, 3, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 5
TP: 5, FP: 2, FN: 0, TN: 3
F1分数: 0.8333, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 9
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 2
TP: 2, FP: 1, FN: 0, TN: 7
F1分数: 0.8000, 召回率: 1.0000

其他(others)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 8
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.4083
UAR: 0.5000

快乐(happiness)评估结果:
样本数: 5
TP: 5, FP: 2, FN: 0, TN: 3
F1分数: 0.8333, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 9
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 2
TP: 2, FP: 1, FN: 0, TN: 7
F1分数: 0.8000, 召回率: 1.0000

其他(others)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 8
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.4083
UAR: 0.5000
UF1: 0.4234 | UAR: 0.518
最佳 UF1: 0.4234 | 最佳 UAR: 0.518

【数据增强统计】
标签 0: 增强了 2842 个样本
标签 1: 增强了 2036 个样本
标签 2: 增强了 2421 个样本
标签 3: 增强了 2247 个样本

========================================
【当前处理受试者】: S01
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 4

S01测试标签: [1, 1, 0, 0, 0, 3, 3, 3, 3, 3]
创建受试者目录: S01
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积1_class4_conv1_legm1/S01/logs
正在初始化 标准卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 使用标准卷积层
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.5375

开始验证...
验证集准确率: 0.6000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.6000
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 0, 3, 3, 0, 3, 2]
真实标签: [1, 1, 0, 0, 0, 3, 3, 3, 3, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 3
TP: 3, FP: 3, FN: 0, TN: 4
F1分数: 0.6667, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 8
F1分数: 0.0000, 召回率: 0.0000
厌恶(disgust): 没有样本

其他(others)评估结果:
样本数: 5
TP: 3, FP: 0, FN: 2, TN: 5
F1分数: 0.7500, 召回率: 0.6000

总体评估结果:
UF1: 0.4722
UAR: 0.5333

快乐(happiness)评估结果:
样本数: 3
TP: 3, FP: 3, FN: 0, TN: 4
F1分数: 0.6667, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 8
F1分数: 0.0000, 召回率: 0.0000
厌恶(disgust): 没有样本

其他(others)评估结果:
样本数: 5
TP: 3, FP: 0, FN: 2, TN: 5
F1分数: 0.7500, 召回率: 0.6000

总体评估结果:
UF1: 0.4722
UAR: 0.5333
UF1: 0.4294 | UAR: 0.5167
最佳 UF1: 0.4294 | 最佳 UAR: 0.5167

【数据增强统计】
标签 0: 增强了 3073 个样本
标签 1: 增强了 2210 个样本
标签 2: 增强了 2637 个样本
标签 3: 增强了 2430 个样本

========================================
【当前处理受试者】: S18
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...

S18测试标签: [1, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3]
创建受试者目录: S18
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积1_class4_conv1_legm1/S18/logs
正在初始化 标准卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 使用标准卷积层
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.5125

开始验证...
验证集准确率: 0.1818 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.1818
当前主体训练完成
最佳预测结果: [1, 0, 0, 0, 1, 3, 0, 0, 3, 0, 0]
真实标签: [1, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3]
当前评估结果:
快乐(happiness): 没有样本

惊讶(surprise)评估结果:
样本数: 5
TP: 2, FP: 0, FN: 3, TN: 6
F1分数: 0.5714, 召回率: 0.4000

厌恶(disgust)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 7
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 2
TP: 0, FP: 2, FN: 2, TN: 7
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.1905
UAR: 0.1333
快乐(happiness): 没有样本

惊讶(surprise)评估结果:
样本数: 5
TP: 2, FP: 0, FN: 3, TN: 6
F1分数: 0.5714, 召回率: 0.4000

厌恶(disgust)评估结果:
样本数: 4
TP: 0, FP: 0, FN: 4, TN: 7
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 2
TP: 0, FP: 2, FN: 2, TN: 7
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.1905
UAR: 0.1333
UF1: 0.4151 | UAR: 0.508
最佳 UF1: 0.4151 | 最佳 UAR: 0.508

【数据增强统计】
标签 0: 增强了 3325 个样本
标签 1: 增强了 2378 个样本
标签 2: 增强了 2841 个样本
标签 3: 增强了 2622 个样本

========================================
【当前处理受试者】: S02
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...

S02测试标签: [1, 1, 0, 0, 2, 3, 3, 3, 3, 3]
创建受试者目录: S02
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积1_class4_conv1_legm1/S02/logs
正在初始化 标准卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 使用标准卷积层
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.4688

开始验证...
验证集准确率: 0.4000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.4000
当前主体训练完成
最佳预测结果: [1, 0, 0, 0, 2, 2, 0, 2, 2, 2]
真实标签: [1, 1, 0, 0, 2, 3, 3, 3, 3, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 2
TP: 2, FP: 2, FN: 0, TN: 6
F1分数: 0.6667, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 1, FP: 0, FN: 1, TN: 8
F1分数: 0.6667, 召回率: 0.5000

厌恶(disgust)评估结果:
样本数: 1
TP: 1, FP: 4, FN: 0, TN: 5
F1分数: 0.3333, 召回率: 1.0000

其他(others)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 5
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.4167
UAR: 0.6250

快乐(happiness)评估结果:
样本数: 2
TP: 2, FP: 2, FN: 0, TN: 6
F1分数: 0.6667, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 1, FP: 0, FN: 1, TN: 8
F1分数: 0.6667, 召回率: 0.5000

厌恶(disgust)评估结果:
样本数: 1
TP: 1, FP: 4, FN: 0, TN: 5
F1分数: 0.3333, 召回率: 1.0000

其他(others)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 5
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.4167
UAR: 0.6250
UF1: 0.4161 | UAR: 0.5015
最佳 UF1: 0.4161 | 最佳 UAR: 0.5015

【数据增强统计】
标签 0: 增强了 3563 个样本
标签 1: 增强了 2552 个样本
标签 2: 增强了 3054 个样本
标签 3: 增强了 2805 个样本

========================================
【当前处理受试者】: S04
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 4

S04测试标签: [1, 1, 1, 0, 2, 2, 2, 3, 3]
创建受试者目录: S04
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积1_class4_conv1_legm1/S04/logs
正在初始化 标准卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 使用标准卷积层
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.4594

开始验证...
验证集准确率: 0.1111 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.1111
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 3, 0, 0, 0, 0]
真实标签: [1, 1, 1, 0, 2, 2, 2, 3, 3]
当前评估结果:

快乐(happiness)评估结果:
样本数: 1
TP: 1, FP: 7, FN: 0, TN: 1
F1分数: 0.2222, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 3
TP: 0, FP: 0, FN: 3, TN: 6
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 3
TP: 0, FP: 0, FN: 3, TN: 6
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 2
TP: 0, FP: 1, FN: 2, TN: 6
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0556
UAR: 0.2500

快乐(happiness)评估结果:
样本数: 1
TP: 1, FP: 7, FN: 0, TN: 1
F1分数: 0.2222, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 3
TP: 0, FP: 0, FN: 3, TN: 6
F1分数: 0.0000, 召回率: 0.0000

厌恶(disgust)评估结果:
样本数: 3
TP: 0, FP: 0, FN: 3, TN: 6
F1分数: 0.0000, 召回率: 0.0000

其他(others)评估结果:
样本数: 2
TP: 0, FP: 1, FN: 2, TN: 6
F1分数: 0.0000, 召回率: 0.0000

总体评估结果:
UF1: 0.0556
UAR: 0.2500
UF1: 0.4035 | UAR: 0.4907
最佳 UF1: 0.4035 | 最佳 UAR: 0.4907

【数据增强统计】
标签 0: 增强了 3808 个样本
标签 1: 增强了 2724 个样本
标签 2: 增强了 3261 个样本
标签 3: 增强了 2997 个样本

========================================
【当前处理受试者】: S12
========================================
组别: 小样本组(3-6个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 4

S12测试标签: [1, 1, 1, 1, 3]
创建受试者目录: S12
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积1_class4_conv1_legm1/S12/logs
正在初始化 标准卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 使用标准卷积层
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.5547

开始验证...
验证集准确率: 1.0000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 1.0000
达到完美准确率,提前结束训练
当前主体训练完成
最佳预测结果: [1, 1, 1, 1, 3]
真实标签: [1, 1, 1, 1, 3]
当前评估结果:
快乐(happiness): 没有样本

惊讶(surprise)评估结果:
样本数: 4
TP: 4, FP: 0, FN: 0, TN: 1
F1分数: 1.0000, 召回率: 1.0000
厌恶(disgust): 没有样本

其他(others)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 4
F1分数: 1.0000, 召回率: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
快乐(happiness): 没有样本

惊讶(surprise)评估结果:
样本数: 4
TP: 4, FP: 0, FN: 0, TN: 1
F1分数: 1.0000, 召回率: 1.0000
厌恶(disgust): 没有样本

其他(others)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 4
F1分数: 1.0000, 召回率: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 0.4131 | UAR: 0.5009
最佳 UF1: 0.4131 | 最佳 UAR: 0.5009

【数据增强统计】
标签 0: 增强了 4060 个样本
标签 1: 增强了 2894 个样本
标签 2: 增强了 3477 个样本
标签 3: 增强了 3192 个样本

========================================
【当前处理受试者】: S20
========================================
组别: 小样本组(3-6个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 4

S20测试标签: [1, 1, 2, 2, 2]
创建受试者目录: S20
创建日志目录: ./Experiment_for_recognize/MMEWC4卷积1_class4_conv1_legm1/S20/logs
正在初始化 标准卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 使用标准卷积层
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1
