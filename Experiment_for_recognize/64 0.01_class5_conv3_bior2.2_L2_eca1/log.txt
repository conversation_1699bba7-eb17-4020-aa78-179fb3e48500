日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 64
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签: [4, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/64 0.01_class5_conv3_bior2.2_L2_eca1/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/64 0.01_class5_conv3_bior2.2_L2_eca1/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m✅ 启用标准LEGM模块:[0m
  [96m- 在conv1后增强初期特征提取[0m 🎯
  [96m- 专注于低层纹理和边缘特征捕获[0m 🔍
  [96m- 参数增加约6.5%，性能提升显著[0m 📈
  [1;95m- 推荐选择，性能与效率平衡[0m ⭐

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 64
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 1539 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/1539[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 1 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.3354[0m (273/814)
   • 中间层1准确率: [96m0.2948[0m (240/814)
   • 中间层2准确率: [96m0.3292[0m (268/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 1 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.0000[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.5000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/64 0.01_class5_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/64 0.01_class5_conv3_bior2.2_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 2/1539[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 2 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.4914[0m (400/814)
   • 中间层1准确率: [96m0.4410[0m (359/814)
   • 中间层2准确率: [96m0.4951[0m (403/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 2 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.5000[0m
============================================================


[1m[1;96m📅 Epoch 3/1539[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 3 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.6081[0m (495/814)
   • 中间层1准确率: [96m0.5885[0m (479/814)
   • 中间层2准确率: [96m0.6229[0m (507/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 3 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.5000[0m
============================================================


[1m[1;96m📅 Epoch 4/1539[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 4 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.6916[0m (563/814)
   • 中间层1准确率: [96m0.6757[0m (550/814)
   • 中间层2准确率: [96m0.6855[0m (558/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 4 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2647[0m (9/34)
🏆 [1m最佳准确率:[0m [1;93m0.5000[0m
============================================================


[1m[1;96m📅 Epoch 5/1539[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 5 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7187[0m (585/814)
   • 中间层1准确率: [96m0.7088[0m (577/814)
   • 中间层2准确率: [96m0.7138[0m (581/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 5 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.1471[0m (5/34)
🏆 [1m最佳准确率:[0m [1;93m0.5000[0m
============================================================


[1m[1;96m📅 Epoch 6/1539[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 6 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7273[0m (592/814)
   • 中间层1准确率: [96m0.7199[0m (586/814)
   • 中间层2准确率: [96m0.7285[0m (593/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 6 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2941[0m (10/34)
🏆 [1m最佳准确率:[0m [1;93m0.5000[0m
============================================================


[1m[1;96m📅 Epoch 7/1539[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 7 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7088[0m (577/814)
   • 中间层1准确率: [96m0.7162[0m (583/814)
   • 中间层2准确率: [96m0.6978[0m (568/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 7 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2647[0m (9/34)
🏆 [1m最佳准确率:[0m [1;93m0.5000[0m
============================================================


[1m[1;96m📅 Epoch 8/1539[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 8 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.6904[0m (562/814)
   • 中间层1准确率: [96m0.6978[0m (568/814)
   • 中间层2准确率: [96m0.6953[0m (566/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 8 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.5000[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.6765[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/64 0.01_class5_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/64 0.01_class5_conv3_bior2.2_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 9/1539[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 9 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7273[0m (592/814)
   • 中间层1准确率: [96m0.7236[0m (589/814)
   • 中间层2准确率: [96m0.7322[0m (596/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 9 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2059[0m (7/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 10/1539[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 10 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7113[0m (579/814)
   • 中间层1准确率: [96m0.7088[0m (577/814)
   • 中间层2准确率: [96m0.7199[0m (586/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 10 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 11/1539[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 11 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7482[0m (609/814)
   • 中间层1准确率: [96m0.7555[0m (615/814)
   • 中间层2准确率: [96m0.7457[0m (607/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m
正在关闭TensorBoard写入器...
TensorBoard写入器已关闭

训练过程出错: DataLoader worker (pid(s) 1194095, 1194097, 1194108, 1194125) exited unexpectedly
详细错误信息: Traceback (most recent call last):
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1131, in _try_get_data
    data = self._data_queue.get(timeout=timeout)
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/multiprocessing/queues.py", line 108, in get
    raise Empty
_queue.Empty

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSANCASME2.py", line 1219, in <module>
    results_dict = main_SKD_TSTSAN_with_Aug_with_SKD(config)
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN_functions_CASME2.py", line 1918, in main_SKD_TSTSAN_with_Aug_with_SKD
    for batch_idx, batch in enumerate(test_dl):
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 630, in __next__
    data = self._next_data()
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1327, in _next_data
    idx, data = self._get_data()
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1293, in _get_data
    success, data = self._try_get_data()
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/utils/data/dataloader.py", line 1144, in _try_get_data
    raise RuntimeError(f'DataLoader worker (pid(s) {pids_str}) exited unexpectedly') from e
RuntimeError: DataLoader worker (pid(s) 1194095, 1194097, 1194108, 1194125) exited unexpectedly


【实验结果报告】
================================================================================

================================================================================
【实验结果报告 - 5分类 (快乐、惊讶、厌恶、压抑、其他)】
================================================================================

【基本信息】
实验名称: 64 0.01_class5_conv3_bior2.2_L2_eca1
分类方案: 5分类 (快乐、惊讶、厌恶、压抑、其他)
时间: 2025-07-25 11:19:08
总训练时间: 00:01:01
数据集: CASME2_LOSO_full


【系统环境】
操作系统: 💻 Linux 6.8.0-1030-nvidia
处理器: ⚡ 32核心处理器
内存: 🧠 62.7GB RAM
GPU: 🚀 NVIDIA A800 80GB PCIe (79.3GB)
GPU数量: 1
训练模式: 单设备

【模型配置】
模型架构: SKD_TSTSAN
分类数量: 5
是否使用预训练: 是
预训练模型: SKD_TSTSAN_CK+_pretrain_experiment_class7_pretrain_conv3_bior2.2_L2_legm1.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.01
基础批次大小: 64
最大迭代次数: 20000
损失函数: FocalLoss_weighted
随机种子: 1337

【GPU性能配置】
CUDNN Benchmark: 禁用
CUDNN Deterministic: 启用
混合精度训练: 禁用
TF32加速: 启用
BatchNorm修复: 禁用
卷积类型: 小波变换卷积

【数据增强配置】
使用训练数据增强: 是
使用测试数据增强: 是
旋转角度范围: 3,8
训练增强倍数: 6,8,3,8,2
测试增强倍数: 6,8,3,8,2
测试数据镜像训练: 启用
镜像训练受试者: sub02,sub05

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: 0.0000
- UAR分数: 0.0000

【各表情类别准确率】
--------------------------------------------------
- 暂无各类别准确率数据


【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
    平均        nan        nan        nan    

================================================================================

================================================================================

【邮件通知】正在发送训练结果...

【邮件通知】邮件发送成功!
- 发件人: <EMAIL>
- 收件人: <EMAIL>
- 主题: [5分类 (快乐、惊讶、厌恶、压抑、其他)实验总结] 64 0.01_class5_conv3_bior2.2_L2_eca1 - UF1=0.0000, UAR=0.0000

正在关闭日志系统...
