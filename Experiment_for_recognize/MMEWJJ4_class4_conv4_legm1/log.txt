
================================================================================
【训练开始】
================================================================================

【硬件信息】使用单个GPU进行训练
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

【损失函数配置】
使用加权Focal Loss (4分类)
类别权重: ['0.4082', '0.1651', '0.2041', '0.2226']
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: S30
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 4
跳过fear/sadness类别测试数据: 3

S30测试标签: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3]
创建受试者目录: S30
创建日志目录: ./Experiment_for_recognize/MMEWJJ4_class4_conv4_legm1/S30/logs
正在初始化 小波变换感受野注意力卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用小波变换感受野注意力卷积功能:
  - 结合小波变换和感受野注意力机制
  - 实现频域-空域联合优化
  - 自适应特征权重分配
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共646个epoch...

Epoch 1/646

训练集准确率: 0.5098

开始验证...
验证集准确率: 0.7714 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.7714

Epoch 2/646

训练集准确率: 0.7731

开始验证...
验证集准确率: 0.6286 | 最佳准确率: 0.7714

Epoch 3/646

训练集准确率: 0.8808

开始验证...
验证集准确率: 0.8000 | 最佳准确率: 0.7714
保存最佳模型,准确率: 0.8000

Epoch 4/646

训练集准确率: 0.9057

开始验证...
验证集准确率: 0.7143 | 最佳准确率: 0.8000

Epoch 5/646

训练集准确率: 0.9492

开始验证...
验证集准确率: 0.6286 | 最佳准确率: 0.8000

Epoch 6/646

训练集准确率: 0.9710

开始验证...
验证集准确率: 0.5714 | 最佳准确率: 0.8000

Epoch 7/646

训练集准确率: 0.9793

开始验证...
验证集准确率: 0.6286 | 最佳准确率: 0.8000

Epoch 8/646

训练集准确率: 0.9699

开始验证...
验证集准确率: 0.6857 | 最佳准确率: 0.8000

Epoch 9/646

训练集准确率: 0.9855

开始验证...
验证集准确率: 0.6286 | 最佳准确率: 0.8000

Epoch 10/646

训练集准确率: 0.9813

开始验证...
验证集准确率: 0.6000 | 最佳准确率: 0.8000

Epoch 11/646

训练集准确率: 0.9699

开始验证...
验证集准确率: 0.6857 | 最佳准确率: 0.8000

Epoch 12/646

训练集准确率: 0.9793

开始验证...
验证集准确率: 0.7429 | 最佳准确率: 0.8000

Epoch 13/646

训练集准确率: 0.9938

开始验证...
验证集准确率: 0.7143 | 最佳准确率: 0.8000

Epoch 14/646

训练集准确率: 0.9927

开始验证...
验证集准确率: 0.7429 | 最佳准确率: 0.8000

Epoch 15/646

训练集准确率: 0.9731

开始验证...
验证集准确率: 0.7143 | 最佳准确率: 0.8000

Epoch 16/646

训练集准确率: 0.9503

开始验证...
验证集准确率: 0.6286 | 最佳准确率: 0.8000

Epoch 17/646

训练集准确率: 0.9855

开始验证...
验证集准确率: 0.6857 | 最佳准确率: 0.8000

Epoch 18/646

训练集准确率: 0.9938

开始验证...
验证集准确率: 0.7714 | 最佳准确率: 0.8000

Epoch 19/646

训练集准确率: 0.9917

开始验证...
验证集准确率: 0.6571 | 最佳准确率: 0.8000

Epoch 20/646

训练集准确率: 0.9959

开始验证...
验证集准确率: 0.7143 | 最佳准确率: 0.8000

Epoch 21/646

训练集准确率: 0.9938

开始验证...
验证集准确率: 0.6571 | 最佳准确率: 0.8000

Epoch 22/646

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7714 | 最佳准确率: 0.8000

Epoch 23/646
