日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签: [4, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C5X95V10_class5_conv3_bior2.2_L2_eca1_mish_frelu_attn/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C5X95V10_class5_conv3_bior2.2_L2_eca1_mish_frelu_attn/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔥 激活函数配置: 浅层Mish + 深层FReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m✅ 启用标准LEGM模块:[0m
  [96m- 在conv1后增强初期特征提取[0m 🎯
  [96m- 专注于低层纹理和边缘特征捕获[0m 🔍
  [96m- 参数增加约6.5%，性能提升显著[0m 📈
  [1;95m- 推荐选择，性能与效率平衡[0m ⭐
[1;92m🔥 启用三模态特征融合:[0m
  [1;96m多头注意力融合[0m 🧠
  [96m- 建立跨模态注意力关系[0m 🔗
  [96m- 动态关注重要特征[0m 🎯
  [1;96m- 注意力头数: 8[0m 🎯
[1;92m🛑 启用早停机制:[0m
  [1;96m智能训练优化[0m ⚡
  [96m- 监控指标: accuracy[0m 📊
  [96m- 耐心值: 200 epochs[0m ⏳
  [96m- 改善阈值: 0.001[0m 📈
  [96m- 自动保存最佳模型[0m 💾
  [96m- 防止过拟合，提高训练效率[0m 🎯

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

[1m[1;93m🛑 早停配置[0m
[1;93m--------[0m
[1;96m📊 监控指标: accuracy[0m
[1;96m⏳ 耐心值: 200 epochs[0m
[1;96m📈 最小改善阈值: 0.001[0m
[1;96m🎯 模式: 最大化[0m

[1m[1;96m📊 损失输出配置[0m
[1;96m----------[0m
[1;96m🔍 详细损失信息: 禁用[0m
[1;96m📈 Epoch损失统计: 禁用[0m
[1;96m📉 验证损失统计: 禁用[0m
[1;96m🚨 过拟合监控: 禁用[0m
[1;96m📦 批次损失详情: 禁用[0m
[1;96m❌ Epoch损失统计将显示: 否[0m
[1;96m❌ 验证损失统计将显示: 否[0m
[1;96m❌ 过拟合监控将显示: 否[0m

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m
正在关闭TensorBoard写入器...
TensorBoard写入器已关闭

训练过程出错: Given groups=64, weight of size [64, 1, 3, 3], expected input[32, 128, 6, 6] to have 64 channels, but got 128 channels instead
详细错误信息: Traceback (most recent call last):
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSANCASME2.py", line 1514, in <module>
    results_dict = main_SKD_TSTSAN_with_Aug_with_SKD(config)
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN_functions_CASME2.py", line 1834, in main_SKD_TSTSAN_with_Aug_with_SKD
    yhat, AC1_out, AC2_out, final_feature, AC1_feature, AC2_feature = model(x)
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/all_model.py", line 707, in forward
    x1 = self.deep_activation_L(x1)  # 深层激活函数：FReLU或ReLU
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/activation_selector.py", line 53, in forward
    return torch.max(x, self.bn(self.conv(x)))
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/nn/modules/conv.py", line 458, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/nn/modules/conv.py", line 454, in _conv_forward
    return F.conv2d(input, weight, bias, self.stride,
RuntimeError: Given groups=64, weight of size [64, 1, 3, 3], expected input[32, 128, 6, 6] to have 64 channels, but got 128 channels instead


【实验结果报告】
================================================================================

================================================================================
【实验结果报告 - 5分类 (快乐、惊讶、厌恶、压抑、其他)】
================================================================================

【基本信息】
实验名称: CASME2C5X95V10_class5_conv3_bior2.2_L2_eca1_mish_frelu_attn
分类方案: 5分类 (快乐、惊讶、厌恶、压抑、其他)
时间: 2025-07-25 15:08:29
总训练时间: 00:00:22
数据集: CASME2_LOSO_full


【系统环境】
操作系统: 💻 Linux 6.8.0-1030-nvidia
处理器: ⚡ 32核心处理器
内存: 🧠 62.7GB RAM
GPU: 🚀 NVIDIA A800 80GB PCIe (79.3GB)
GPU数量: 1
训练模式: 单设备

【模型配置】
模型架构: SKD_TSTSAN
分类数量: 5
是否使用预训练: 是
预训练模型: SKD_TSTSAN_CK+_pretrain_experiment_class7_pretrain_conv3_bior2.2_L2_legm1.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
基础批次大小: 32
最大迭代次数: 20000
损失函数: FocalLoss_weighted
随机种子: 2577

【GPU性能配置】
CUDNN Benchmark: 禁用
CUDNN Deterministic: 启用
混合精度训练: 禁用
TF32加速: 启用
BatchNorm修复: 禁用
卷积类型: 小波变换卷积

【数据增强配置】
使用训练数据增强: 是
使用测试数据增强: 是
旋转角度范围: 3,8
训练增强倍数: 6,8,3,8,2
测试增强倍数: 6,8,3,8,2
测试数据镜像训练: 启用
镜像训练受试者: sub02,sub05

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: 0.0000
- UAR分数: 0.0000

【各表情类别准确率】
--------------------------------------------------
- 暂无各类别准确率数据


【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
    平均        nan        nan        nan    

================================================================================

================================================================================

【邮件通知】正在发送训练结果...

【邮件通知】邮件发送成功!
- 发件人: <EMAIL>
- 收件人: <EMAIL>
- 主题: [5分类 (快乐、惊讶、厌恶、压抑、其他)实验总结] CASME2C5X95V10_class5_conv3_bior2.2_L2_eca1_mish_frelu_attn - UF1=0.0000, UAR=0.0000

正在关闭日志系统...
