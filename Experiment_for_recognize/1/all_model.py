# 导入必要的PyTorch库
import torch.nn as nn
import torch
import torch.nn.functional as F
import math
# 导入运动放大相关的自定义模块
from motion_magnification_learning_based_master.magnet import Manipulator as MagManipulator
from motion_magnification_learning_based_master.magnet import Encoder_No_texture as MagEncoder_No_texture


def gen_state_dict(weights_path):
    """生成状态字典,用于加载预训练模型权重"""
    st = torch.load(weights_path)  # 加载权重文件
    st_ks = list(st.keys())  # 获取所有键
    st_vs = list(st.values())  # 获取所有值
    state_dict = {}
    # 移除'module.'前缀,适配非并行训练的模型
    for st_k, st_v in zip(st_ks, st_vs):
        state_dict[st_k.replace('module.', '')] = st_v
    return state_dict

class ConsensusModule(torch.nn.Module):
    """时序共识模块,用于整合时序特征"""
    def __init__(self, consensus_type, dim=1):
        super(ConsensusModule, self).__init__()
        # 如果共识类型是rnn则使用identity,否则使用指定类型
        self.consensus_type = consensus_type if consensus_type != 'rnn' else 'identity'
        self.dim = dim  # 指定在哪个维度上进行共识运算

    def forward(self, input):
        # 调用SegmentConsensus进行实际的共识计算
        return SegmentConsensus(self.consensus_type, self.dim)(input)

class SegmentConsensus(torch.nn.Module):
    """片段共识模块,实现具体的共识计算逻辑"""
    def __init__(self, consensus_type, dim=1):
        super(SegmentConsensus, self).__init__()
        self.consensus_type = consensus_type  # 共识类型
        self.dim = dim  # 计算维度
        self.shape = None  # 保存输入张量形状

    def forward(self, input_tensor):
        self.shape = input_tensor.size()
        if self.consensus_type == 'avg':
            # 在指定维度上计算平均值
            output = input_tensor.mean(dim=self.dim, keepdim=True)
        elif self.consensus_type == 'identity':
            # 直接返回输入,不做处理
            output = input_tensor
        else:
            output = None
        return output

class TemporalShift(nn.Module):
    """时序位移模块,实现特征的时间维度位移"""
    def __init__(self, net, n_segment=3, n_div=8, inplace=False):
        super(TemporalShift, self).__init__()
        self.net = net  # 底层网络
        self.n_segment = n_segment  # 时序片段数
        self.fold_div = n_div  # 通道分组数
        self.inplace = inplace  # 是否原地操作
        if inplace:
            print('=> 使用原地位移操作...')
        print('=> 使用通道分组数: {}'.format(self.fold_div))

    def forward(self, x):
        # 先进行时序位移,再通过网络
        x = self.shift(x, self.n_segment, fold_div=self.fold_div, inplace=self.inplace)
        return self.net(x)

    @staticmethod
    def shift(x, n_segment, fold_div=3, inplace=False):
        """实现时序位移的核心方法"""
        nt, c, h, w = x.size()  # 获取输入尺寸
        n_batch = nt // n_segment  # 计算批次大小
        x = x.view(n_batch, n_segment, c, h, w)  # 重塑张量形状

        fold = c // fold_div  # 计算每组通道数
        if inplace:
            raise NotImplementedError
        else:
            out = torch.zeros_like(x)  # 创建输出张量
            out[:, :-1, :fold] = x[:, 1:, :fold]  # 向左位移
            out[:, 1:, fold: 2 * fold] = x[:, :-1, fold: 2 * fold]  # 向右位移
            out[:, :, 2 * fold:] = x[:, :, 2 * fold:]  # 保持不变

        return out.view(nt, c, h, w)  # 恢复原始形状

class eca_layer_2d_v2(nn.Module):
    """高效通道注意力模块的改进版本
    
    参数:
        channel: 输入特征图的通道数
        k_size: 自适应选择的卷积核大小
    """

    def __init__(self, channel):
        super(eca_layer_2d_v2, self).__init__()
        # 全局平均池化,将特征图压缩为1x1
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        # 全局最大池化,将特征图压缩为1x1
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        # 根据通道数自适应计算卷积核大小
        t = int(abs(math.log(channel,2)+1)/2)
        # 确保卷积核大小为奇数
        k_size = t if t%2 else (t+1)
        # 1D卷积层,用于通道注意力的计算
        self.conv = nn.Conv1d(1, 1, kernel_size=k_size, padding=(k_size - 1) // 2, bias=False)
        # sigmoid激活函数,将注意力权重归一化到0-1
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        # 计算平均池化结果
        y_avg = self.avg_pool(x)
        # 计算最大池化结果
        y_max = self.max_pool(x)

        # 对平均池化结果进行处理:压缩最后一维->转置->卷积->转置回来->增加维度
        y_avg = self.conv(y_avg.squeeze(-1).transpose(-1, -2)).transpose(-1, -2).unsqueeze(-1)
        # 对最大池化结果进行相同处理
        y_max = self.conv(y_max.squeeze(-1).transpose(-1, -2)).transpose(-1, -2).unsqueeze(-1)

        # 将平均池化和最大池化的结果相加并通过sigmoid
        y = self.sigmoid(y_avg+y_max)

        # 将注意力权重与输入特征图相乘
        return x * y.expand_as(x)

class SKD_TSTSAN(nn.Module):
    def __init__(self, out_channels=5, amp_factor=5):
        super(SKD_TSTSAN, self).__init__()
        # 初始化三个编码器,分别处理不同维度的输入
        self.Aug_Encoder_L = MagEncoder_No_texture(dim_in=16)  # 处理16维输入的编码器
        self.Aug_Encoder_S = MagEncoder_No_texture(dim_in=1)   # 处理1维输入的编码器
        self.Aug_Encoder_T = MagEncoder_No_texture(dim_in=2)   # 处理2维输入的编码器
        
        # 初始化三个特征操作器
        self.Aug_Manipulator_L = MagManipulator()  # 处理L分支特征的操作器
        self.Aug_Manipulator_S = MagManipulator()  # 处理S分支特征的操作器
        self.Aug_Manipulator_T = MagManipulator()  # 处理T分支特征的操作器

        # 第一层卷积,将特征维度从32升到64
        self.conv1_L = nn.Conv2d(32, out_channels=64, kernel_size=5, stride=1)  # L分支第一层卷积
        self.conv1_S = nn.Conv2d(32, out_channels=64, kernel_size=5, stride=1)  # S分支第一层卷积
        self.conv1_T = nn.Conv2d(32, out_channels=64, kernel_size=5, stride=1)  # T分支第一层卷积

        # ReLU激活函数
        self.relu = nn.ReLU()
        # 第一层的批归一化
        self.bn1_L = nn.BatchNorm2d(64)  # L分支第一层批归一化
        self.bn1_S = nn.BatchNorm2d(64)  # S分支第一层批归一化
        self.bn1_T = nn.BatchNorm2d(64)  # T分支第一层批归一化
        # 最大池化层
        self.maxpool = nn.MaxPool2d(kernel_size=5, stride=2, padding=2)

        # AC1模块的第一层卷积
        self.AC1_conv1_L = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)  # L分支
        self.AC1_conv1_S = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)  # S分支
        self.AC1_conv1_T = TemporalShift(nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)  # T分支带时序位移
        # AC1模块的第一层批归一化
        self.AC1_bn1_L = nn.BatchNorm2d(128)  # L分支
        self.AC1_bn1_S = nn.BatchNorm2d(128)  # S分支
        self.AC1_bn1_T = nn.BatchNorm2d(128)  # T分支

        # AC1模块的第二层卷积
        self.AC1_conv2_L = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)  # L分支
        self.AC1_conv2_S = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)  # S分支
        self.AC1_conv2_T = TemporalShift(nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)  # T分支带时序位移
        # AC1模块的第二层批归一化
        self.AC1_bn2_L = nn.BatchNorm2d(128)  # L分支
        self.AC1_bn2_S = nn.BatchNorm2d(128)  # S分支
        self.AC1_bn2_T = nn.BatchNorm2d(128)  # T分支
        # AC1模块的池化层和全连接层
        self.AC1_pool = nn.AdaptiveAvgPool2d(1)  # 自适应平均池化
        self.AC1_fc = nn.Linear(in_features=384, out_features=out_channels)  # 全连接层

        # 第二层卷积
        self.conv2_L = nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1)  # L分支
        self.conv2_S = nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1)  # S分支
        self.conv2_T = TemporalShift(nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)  # T分支带时序位移
        # 第二层批归一化
        self.bn2_L = nn.BatchNorm2d(64)  # L分支
        self.bn2_S = nn.BatchNorm2d(64)  # S分支
        self.bn2_T = nn.BatchNorm2d(64)  # T分支

        # 第三层卷积
        self.conv3_L = nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1)  # L分支
        self.conv3_S = nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1)  # S分支
        self.conv3_T = TemporalShift(nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)  # T分支带时序位移
        # 第三层批归一化
        self.bn3_L = nn.BatchNorm2d(64)  # L分支
        self.bn3_S = nn.BatchNorm2d(64)  # S分支
        self.bn3_T = nn.BatchNorm2d(64)  # T分支

        # 平均池化层
        self.avgpool = nn.AvgPool2d(kernel_size=3, stride=2, padding=1)

        # AC2模块的第一层卷积
        self.AC2_conv1_L = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)  # L分支
        self.AC2_conv1_S = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)  # S分支
        self.AC2_conv1_T = TemporalShift(nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)  # T分支带时序位移
        # AC2模块的第一层批归一化
        self.AC2_bn1_L = nn.BatchNorm2d(128)  # L分支
        self.AC2_bn1_S = nn.BatchNorm2d(128)  # S分支
        self.AC2_bn1_T = nn.BatchNorm2d(128)  # T分支

        # AC2模块的第二层卷积
        self.AC2_conv2_L = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)  # L分支
        self.AC2_conv2_S = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)  # S分支
        self.AC2_conv2_T = TemporalShift(nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)  # T分支带时序位移
        # AC2模块的第二层批归一化
        self.AC2_bn2_L = nn.BatchNorm2d(128)  # L分支
        self.AC2_bn2_S = nn.BatchNorm2d(128)  # S分支
        self.AC2_bn2_T = nn.BatchNorm2d(128)  # T分支
        # AC2模块的池化层和全连接层
        self.AC2_pool = nn.AdaptiveAvgPool2d(1)  # 自适应平均池化
        self.AC2_fc = nn.Linear(in_features=384, out_features=out_channels)  # 全连接层

        # 全局平均池化层
        self.all_avgpool = nn.AdaptiveAvgPool2d(1)
        # 第四层卷积
        self.conv4_L = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)  # L分支
        self.conv4_S = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)  # S分支
        self.conv4_T = TemporalShift(nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)  # T分支带时序位移
        # 第四层批归一化
        self.bn4_L = nn.BatchNorm2d(128)  # L分支
        self.bn4_S = nn.BatchNorm2d(128)  # S分支
        self.bn4_T = nn.BatchNorm2d(128)  # T分支

        # 第五层卷积
        self.conv5_L = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)  # L分支
        self.conv5_S = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)  # S分支
        self.conv5_T = TemporalShift(nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)  # T分支带时序位移
        # 第五层批归一化
        self.bn5_L = nn.BatchNorm2d(128)  # L分支
        self.bn5_S = nn.BatchNorm2d(128)  # S分支
        self.bn5_T = nn.BatchNorm2d(128)  # T分支

        # 最终的全连接层
        self.fc2 = nn.Linear(in_features=384, out_features=out_channels)

        # ECA注意力模块
        self.ECA1 = eca_layer_2d_v2(64)  # 第一个ECA模块,作用于64通道
        self.ECA2 = eca_layer_2d_v2(64)  # 第二个ECA模块,作用于64通道
        self.ECA3 = eca_layer_2d_v2(64)  # 第三个ECA模块,作用于64通道
        self.ECA4 = eca_layer_2d_v2(128)  # 第四个ECA模块,作用于128通道
        self.ECA5 = eca_layer_2d_v2(128)  # 第五个ECA模块,作用于128通道

        # AC1和AC2模块中的ECA注意力
        self.AC1_ECA1 = eca_layer_2d_v2(128)  # AC1模块的第一个ECA
        self.AC1_ECA2 = eca_layer_2d_v2(128)  # AC1模块的第二个ECA
        self.AC2_ECA1 = eca_layer_2d_v2(128)  # AC2模块的第一个ECA
        self.AC2_ECA2 = eca_layer_2d_v2(128)  # AC2模块的第二个ECA

        # 特征放大因子,用于控制特征增强的程度
        self.amp_factor = amp_factor

        # 共识模块,用于整合多个时序片段的预测结果
        # 使用平均池化方式进行整合
        self.consensus = ConsensusModule("avg")

        # Dropout层,用于随机失活神经元防止过拟合
        # 失活概率为0.2
        self.dropout = nn.Dropout(0.2)

    def forward(self, input):
        # 将输入数据分解为不同的部分
        x1 = input[:, 2:18, :, :]  # 提取第2-17通道作为主要特征
        x1_onset = input[:, 18:34, :, :]  # 提取第18-33通道作为onset特征
        x2 = input[:, 0, :, :].unsqueeze(dim=1)  # 提取第0通道并扩展维度
        x2_onset = input[:, 1, :, :].unsqueeze(dim=1)  # 提取第1通道并扩展维度
        x3 = input[:, 34:, :, :]  # 提取第34通道之后的所有特征

        # 获取批次大小
        bsz = x1.shape[0]

        # 重塑x3张量形状为(batch_size*2, 2, 48, 48)
        x3 = torch.reshape(x3, (bsz * 2, 2, 48, 48))

        # 创建与x3相同大小的全零张量作为x3_onset
        x3_onset = torch.zeros(bsz * 2, 2, 48, 48).cuda()

        # 使用运动编码器处理onset特征和主要特征
        motion_x1_onset = self.Aug_Encoder_L(x1_onset)  # L分支onset特征编码
        motion_x1 = self.Aug_Encoder_L(x1)  # L分支主要特征编码
        x1 = self.Aug_Manipulator_L(motion_x1_onset, motion_x1, self.amp_factor)  # L分支特征增强

        motion_x2_onset = self.Aug_Encoder_S(x2_onset)  # S分支onset特征编码
        motion_x2 = self.Aug_Encoder_S(x2)  # S分支主要特征编码
        x2 = self.Aug_Manipulator_S(motion_x2_onset, motion_x2, self.amp_factor)  # S分支特征增强

        motion_x3_onset = self.Aug_Encoder_T(x3_onset)  # T分支onset特征编码
        motion_x3 = self.Aug_Encoder_T(x3)  # T分支主要特征编码
        x3 = self.Aug_Manipulator_T(motion_x3_onset, motion_x3, self.amp_factor)  # T分支特征增强

        # 第一层卷积处理 - L分支
        x1 = self.conv1_L(x1)  # 卷积操作
        x1 = self.bn1_L(x1)  # 批归一化
        x1 = self.relu(x1)  # ReLU激活
        x1 = self.ECA1(x1)  # ECA注意力机制
        x1 = self.maxpool(x1)  # 最大池化

        # 第一层卷积处理 - S分支
        x2 = self.conv1_S(x2)  # 卷积操作
        x2 = self.bn1_S(x2)  # 批归一化
        x2 = self.relu(x2)  # ReLU激活
        x2 = self.maxpool(x2)  # 最大池化

        # 第一层卷积处理 - T分支
        x3 = self.conv1_T(x3)  # 时序位移卷积
        x3 = self.bn1_T(x3)  # 批归一化
        x3 = self.relu(x3)  # ReLU激活
        x3 = self.maxpool(x3)  # 最大池化

        # AC1模块处理 - L分支
        AC1_x1 = self.AC1_conv1_L(x1)  # 第一层卷积
        AC1_x1 = self.AC1_bn1_L(AC1_x1)  # 批归一化
        AC1_x1 = self.relu(AC1_x1)  # ReLU激活
        AC1_x1 = self.AC1_ECA1(AC1_x1)  # ECA注意力
        AC1_x1 = self.AC1_conv2_L(AC1_x1)  # 第二层卷积
        AC1_x1 = self.AC1_bn2_L(AC1_x1)  # 批归一化
        AC1_x1 = self.relu(AC1_x1)  # ReLU激活
        AC1_x1 = self.AC1_ECA2(AC1_x1)  # ECA注意力
        AC1_x1 = self.AC1_pool(AC1_x1)  # 平均池化
        AC1_x1_all = AC1_x1.view(AC1_x1.size(0), -1)  # 展平特征

        # AC1模块处理 - S分支
        AC1_x2 = self.AC1_conv1_S(x2)  # 第一层卷积
        AC1_x2 = self.AC1_bn1_S(AC1_x2)  # 批归一化
        AC1_x2 = self.relu(AC1_x2)  # ReLU激活
        AC1_x2 = self.AC1_conv2_S(AC1_x2)  # 第二层卷积
        AC1_x2 = self.AC1_bn2_S(AC1_x2)  # 批归一化
        AC1_x2 = self.relu(AC1_x2)  # ReLU激活
        AC1_x2 = self.AC1_pool(AC1_x2)  # 平均池化
        AC1_x2_all = AC1_x2.view(AC1_x2.size(0), -1)  # 展平特征

        # AC1模块处理 - T分支
        AC1_x3 = self.AC1_conv1_T(x3)  # 第一层时序位移卷积
        AC1_x3 = self.AC1_bn1_T(AC1_x3)  # 批归一化
        AC1_x3 = self.relu(AC1_x3)  # ReLU激活
        AC1_x3 = self.AC1_conv2_T(AC1_x3)  # 第二层时序位移卷积
        AC1_x3 = self.AC1_bn2_T(AC1_x3)  # 批归一化
        AC1_x3 = self.relu(AC1_x3)  # ReLU激活
        AC1_x3 = self.AC1_pool(AC1_x3)  # 平均池化
        AC1_x3_all = AC1_x3.view(AC1_x3.size(0), -1)  # 展平特征

        # 处理T分支的时序信息
        AC1_x3_all = AC1_x3_all.view((-1, 2) + AC1_x3_all.size()[1:])  # 重塑张量以处理时序
        AC1_x3_all = self.consensus(AC1_x3_all)  # 应用时序共识
        AC1_x3_all = AC1_x3_all.squeeze(1)  # 压缩维度

        # 合并AC1模块的三个分支特征
        AC1_feature = torch.cat((AC1_x1_all, AC1_x2_all, AC1_x3_all), 1)  # 特征拼接
        AC1_x_all = self.dropout(AC1_feature)  # 应用dropout
        AC1_x_all = self.AC1_fc(AC1_x_all)  # 全连接层处理

        # 主干网络继续处理 - L分支
        x1 = self.conv2_L(x1)  # 第二层卷积
        x1 = self.bn2_L(x1)  # 批归一化
        x1 = self.relu(x1)  # ReLU激活
        x1 = self.ECA2(x1)  # ECA注意力
        x1 = self.conv3_L(x1)  # 第三层卷积
        x1 = self.bn3_L(x1)  # 批归一化
        x1 = self.relu(x1)  # ReLU激活
        x1 = self.ECA3(x1)  # ECA注意力
        x1 = self.avgpool(x1)  # 平均池化

        # 主干网络继续处理 - S分支
        x2 = self.conv2_S(x2)  # 第二层卷积
        x2 = self.bn2_S(x2)  # 批归一化
        x2 = self.relu(x2)  # ReLU激活
        x2 = self.conv3_S(x2)  # 第三层卷积
        x2 = self.bn3_S(x2)  # 批归一化
        x2 = self.relu(x2)  # ReLU激活
        x2 = self.avgpool(x2)  # 平均池化

        # 主干网络继续处理 - T分支
        x3 = self.conv2_T(x3)  # 第二层时序位移卷积
        x3 = self.bn2_T(x3)  # 批归一化
        x3 = self.relu(x3)  # ReLU激活
        x3 = self.conv3_T(x3)  # 第三层时序位移卷积
        x3 = self.bn3_T(x3)  # 批归一化
        x3 = self.relu(x3)  # ReLU激活
        x3 = self.avgpool(x3)  # 平均池化

        # AC2模块处理 - L分支
        AC2_x1 = self.AC2_conv1_L(x1)  # 第一层卷积
        AC2_x1 = self.AC2_bn1_L(AC2_x1)  # 批归一化
        AC2_x1 = self.relu(AC2_x1)  # ReLU激活
        AC2_x1 = self.AC2_ECA1(AC2_x1)  # ECA注意力
        AC2_x1 = self.AC2_conv2_L(AC2_x1)  # 第二层卷积
        AC2_x1 = self.AC2_bn2_L(AC2_x1)  # 批归一化
        AC2_x1 = self.relu(AC2_x1)  # ReLU激活
        AC2_x1 = self.AC2_ECA2(AC2_x1)  # ECA注意力
        AC2_x1 = self.AC2_pool(AC2_x1)  # 平均池化
        AC2_x1_all = AC2_x1.view(AC2_x1.size(0), -1)  # 展平特征

        # AC2模块处理 - S分支
        AC2_x2 = self.AC2_conv1_S(x2)  # 第一层卷积
        AC2_x2 = self.AC2_bn1_S(AC2_x2)  # 批归一化
        AC2_x2 = self.relu(AC2_x2)  # ReLU激活
        AC2_x2 = self.AC2_conv2_S(AC2_x2)  # 第二层卷积
        AC2_x2 = self.AC2_bn2_S(AC2_x2)  # 批归一化
        AC2_x2 = self.relu(AC2_x2)  # ReLU激活
        AC2_x2 = self.AC2_pool(AC2_x2)  # 平均池化
        AC2_x2_all = AC2_x2.view(AC2_x2.size(0), -1)  # 展平特征

        # AC2模块处理 - T分支
        AC2_x3 = self.AC2_conv1_T(x3)  # 第一层时序位移卷积
        AC2_x3 = self.AC2_bn1_T(AC2_x3)  # 批归一化
        AC2_x3 = self.relu(AC2_x3)  # ReLU激活
        AC2_x3 = self.AC2_conv2_T(AC2_x3)  # 第二层时序位移卷积
        AC2_x3 = self.AC2_bn2_T(AC2_x3)  # 批归一化
        AC2_x3 = self.relu(AC2_x3)  # ReLU激活
        AC2_x3 = self.AC2_pool(AC2_x3)  # 平均池化
        AC2_x3_all = AC2_x3.view(AC2_x3.size(0), -1)  # 展平特征

        # 处理AC2模块T分支的时序信息
        AC2_x3_all = AC2_x3_all.view((-1, 2) + AC2_x3_all.size()[1:])  # 重塑张量以处理时序
        AC2_x3_all = self.consensus(AC2_x3_all)  # 应用时序共识
        AC2_x3_all = AC2_x3_all.squeeze(1)  # 压缩维度

        # 合并AC2模块的三个分支特征
        AC2_feature = torch.cat((AC2_x1_all, AC2_x2_all, AC2_x3_all), 1)  # 特征拼接
        AC2_x_all = self.dropout(AC2_feature)  # 应用dropout
        AC2_x_all = self.AC2_fc(AC2_x_all)  # 全连接层处理

        # 主干网络最后处理 - L分支
        x1 = self.conv4_L(x1)  # 第四层卷积
        x1 = self.bn4_L(x1)  # 批归一化
        x1 = self.relu(x1)  # ReLU激活
        x1 = self.ECA4(x1)  # ECA注意力
        x1 = self.conv5_L(x1)  # 第五层卷积
        x1 = self.bn5_L(x1)  # 批归一化
        x1 = self.relu(x1)  # ReLU激活
        x1 = self.ECA5(x1)  # ECA注意力
        x1 = self.all_avgpool(x1)  # 全局平均池化
        x1_all = x1.view(x1.size(0), -1)  # 展平特征

        # 主干网络最后处理 - S分支
        x2 = self.conv4_S(x2)  # 第四层卷积
        x2 = self.bn4_S(x2)  # 批归一化
        x2 = self.relu(x2)  # ReLU激活
        x2 = self.conv5_S(x2)  # 第五层卷积
        x2 = self.bn5_S(x2)  # 批归一化
        x2 = self.relu(x2)  # ReLU激活
        x2 = self.all_avgpool(x2)  # 全局平均池化
        x2_all = x2.view(x2.size(0), -1)  # 展平特征

        # 主干网络最后处理 - T分支
        x3 = self.conv4_T(x3)  # 第四层时序位移卷积
        x3 = self.bn4_T(x3)  # 批归一化
        x3 = self.relu(x3)  # ReLU激活
        x3 = self.conv5_T(x3)  # 第五层时序位移卷积
        x3 = self.bn5_T(x3)  # 批归一化
        x3 = self.relu(x3)  # ReLU激活
        x3 = self.all_avgpool(x3)  # 全局平均池化
        x3_all = x3.view(x3.size(0), -1)  # 展平特征

        # 处理最终T分支的时序信息
        x3_all = x3_all.view((-1, 2) + x3_all.size()[1:])  # 重塑张量以处理时序
        x3_all = self.consensus(x3_all)  # 应用时序共识
        x3_all = x3_all.squeeze(1)  # 压缩维度

        # 合并最终的三个分支特征
        final_feature = torch.cat((x1_all, x2_all, x3_all), 1)  # 特征拼接
        x_all = self.dropout(final_feature)  # 应用dropout
        x_all = self.fc2(x_all)  # 最终全连接层处理

        # 返回所有预测结果和中间特征
        return x_all, AC1_x_all, AC2_x_all, final_feature, AC1_feature, AC2_feature


def get_model(model_name, class_num, alpha):
    # 根据模型名称返回相应的模型实例
    if model_name == "SKD_TSTSAN":
        return SKD_TSTSAN(class_num, alpha)  # 返回SKD_TSTSAN模型实例
