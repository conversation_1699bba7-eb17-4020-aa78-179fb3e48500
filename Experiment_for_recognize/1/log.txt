
================================================================================
【训练开始】
================================================================================

【硬件信息】使用单个GPU进行训练

【损失函数配置】
使用加权Focal Loss
类别权重: ['0.3794', '0.1349', '0.4857']
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================

【样本分布统计】
标签 0: 7 个样本
标签 1: 1 个样本
标签 2: 14 个样本
标签 3: 9 个样本
标签 4: 3 个样本
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签(3分类): [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
创建受试者目录: sub17
创建日志目录: ./Experiment_for_recognize/1/sub17/logs
初始化模型...
加载预训练模型...
开始训练,总共1001个epoch...

Epoch 1/1001

==================================================
【网络维度信息】
==================================================

输入数据维度: torch.Size([32, 38, 48, 48])

【特征分解维度】
x1 (主要特征): torch.Size([32, 16, 48, 48])
x1_onset (onset特征): torch.Size([32, 16, 48, 48])
x2: torch.Size([32, 1, 48, 48])
x2_onset: torch.Size([32, 1, 48, 48])
x3: torch.Size([32, 4, 48, 48])

LightCrossBranchAttention (L branch) input shapes:
x_main: torch.Size([32, 64, 10, 10])
x_other1: torch.Size([32, 64, 10, 10])
x_other2: torch.Size([64, 64, 10, 10])

After conv shapes:
query: torch.Size([32, 16, 10, 10])
key_1: torch.Size([32, 16, 10, 10])
key_2: torch.Size([64, 16, 10, 10])

After reshape shapes:
query: torch.Size([32, 16, 100])
key_1: torch.Size([32, 16, 100])
key_2: torch.Size([32, 32, 100])
value_1: torch.Size([32, 64, 100])
value_2: torch.Size([32, 128, 100])

================================================================================
【训练开始】
================================================================================

【硬件信息】使用单个GPU进行训练

【损失函数配置】
使用加权Focal Loss
类别权重: ['0.3794', '0.1349', '0.4857']
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================

【样本分布统计】
标签 0: 7 个样本
标签 1: 1 个样本
标签 2: 14 个样本
标签 3: 9 个样本
标签 4: 3 个样本
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签(3分类): [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
初始化增强模型...
模型参数量: 6,680,042
模型大小: 25.48 MB
使用增强模块: True
✅ 启用增强功能:
  - 自适应Dropout (训练过程中动态调整)
  - 改进的跨分支注意力机制
  - 多尺度特征提取
  - 增强的知识蒸馏
  - 改进的特征归一化
加载预训练模型...
开始训练,总共953个epoch...

Epoch 1/953

================================================================================
【训练开始】
================================================================================

【硬件信息】使用CPU进行训练

【损失函数配置】
使用加权Focal Loss
类别权重: ['0.3794', '0.1349', '0.4857']
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================

【样本分布统计】
标签 0: 7 个样本
标签 1: 1 个样本
标签 2: 14 个样本
标签 3: 9 个样本
标签 4: 3 个样本
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签(3分类): [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
初始化模型...
=> 使用通道分组数: 8
=> 使用通道分组数: 8
=> 使用通道分组数: 8
=> 使用通道分组数: 8
=> 使用通道分组数: 8
=> 使用通道分组数: 8
=> 使用通道分组数: 8
=> 使用通道分组数: 8
加载预训练模型...
