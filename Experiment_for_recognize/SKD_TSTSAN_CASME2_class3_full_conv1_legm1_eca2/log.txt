日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (3分类)[0m
[1;96m🎯 类别权重: ['0.3794', '0.1349', '0.4857'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub17测试标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv1_legm1_eca2/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv1_legm1_eca2/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 标准卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 使用增强型ECA注意力机制 - 双分支自适应特征重标定
[96m使用标准卷积层[0m 🔧
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 953 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.2258[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.2258[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv1_legm1_eca2/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv1_legm1_eca2/sub17/sub17.pth

[1m[1;96m📅 Epoch 2/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9318[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.6774[0m | [1m最佳准确率:[0m [1;93m0.2258[0m
[1;92m💾 保存最佳模型，准确率: 0.6774[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv1_legm1_eca2/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv1_legm1_eca2/sub17/sub17.pth

[1m[1;96m📅 Epoch 3/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9697[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6129[0m | [1m最佳准确率:[0m [1;93m0.6774[0m

[1m[1;96m📅 Epoch 4/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9182[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.3548[0m | [1m最佳准确率:[0m [1;93m0.6774[0m

[1m[1;96m📅 Epoch 5/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9182[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6452[0m | [1m最佳准确率:[0m [1;93m0.6774[0m

[1m[1;96m📅 Epoch 6/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9636[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.7419[0m | [1m最佳准确率:[0m [1;93m0.6774[0m
[1;92m💾 保存最佳模型，准确率: 0.7419[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv1_legm1_eca2/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv1_legm1_eca2/sub17/sub17.pth

[1m[1;96m📅 Epoch 7/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9894[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.7742[0m | [1m最佳准确率:[0m [1;93m0.7419[0m
[1;92m💾 保存最佳模型，准确率: 0.7742[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv1_legm1_eca2/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv1_legm1_eca2/sub17/sub17.pth

[1m[1;96m📅 Epoch 8/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5806[0m | [1m最佳准确率:[0m [1;93m0.7742[0m

[1m[1;96m📅 Epoch 9/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.8710[0m | [1m最佳准确率:[0m [1;93m0.7742[0m
[1;92m💾 保存最佳模型，准确率: 0.8710[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv1_legm1_eca2/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv1_legm1_eca2/sub17/sub17.pth

[1m[1;96m📅 Epoch 10/953[0m
[1;96m----------------[0m
正在关闭TensorBoard写入器...
TensorBoard写入器已关闭

正在关闭日志系统...
