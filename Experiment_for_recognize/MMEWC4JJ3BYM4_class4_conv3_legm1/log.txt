
[1;96m==================[0m
[1m[1;96m🚀 MMEW 微表情识别训练开始 🚀[0m
[1;96m==================[0m


[1m[1;92m🖥️ 硬件信息 - 检测到1个GPU[0m
[1;92m-------------------[0m
[1;96mℹ️ GPU 0: NVIDIA A800 80GB PCIe (79.3GB)[0m

[1m[1;94m🔧 单GPU配置[0m
[1;94m----------[0m
[1;92m✅ 单GPU内存管理已配置[0m

[1m[1;93m⚡ 性能优化配置[0m
[1;93m----------[0m
[1;92m✅ TF32加速已启用[0m
[1;96mℹ️ 混合精度训练已禁用[0m
[1;96mℹ️ BatchNorm修复已禁用 (使用原始BatchNorm层)[0m

[1m[1;96m⚖️ 损失函数配置[0m
[1;96m----------[0m
[1;92m✅ 使用加权Focal Loss (4分类)[0m
[1;96mℹ️ 类别权重: ['0.4082', '0.1651', '0.2041', '0.2226'][0m
开始训练...
根据配置,不使用Visdom可视化

[1;92m================[0m
[1m[1;92m👤 当前处理受试者: S30 👤[0m
[1;92m================[0m

[1;96mℹ️ 组别: 大样本组(>12个样本)[0m

[1m[1;94m📥 加载训练数据[0m
[1;94m----------[0m
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3

[1m[1;94m📤 加载测试数据[0m
[1;94m----------[0m
跳过fear/sadness类别测试数据: 4
跳过fear/sadness类别测试数据: 3

S30测试标签: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3]
创建受试者目录: S30
创建日志目录: ./Experiment_for_recognize/MMEWC4JJ3BYM4_class4_conv3_legm1/S30/logs
正在初始化 小波变换卷积 模型...
🌊 小波配置: 类型=sym4, 层数=1
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用小波变换卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用WTConv
  - 在中间卷积层(conv2_L, conv2_S)中使用WTConv
  - 小波类型: sym4 (层数: 1)
  - 通过小波变换实现多频响应特征提取
  - 同时捕获微表情的全局结构和局部细节
  - 实现非常大的感受野而不会过度参数化
  - 特别适合微表情的频域特征分析
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
