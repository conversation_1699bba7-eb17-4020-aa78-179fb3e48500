
================================================================================
【MMEW数据集训练开始】
================================================================================
使用设备: cuda
训练顺序: ['S30', 'S06', 'S13', 'S09', 'S10', 'S21', 'S16', 'S05', 'S11', 'S15', 'S03', 'S08', 'S01', 'S18', 'S02', 'S04', 'S12', 'S20', 'S22', 'S24', 'S25', 'S29', 'S07', 'S17', 'S19', 'S27', 'S28', 'S14', 'S23', 'S26']
总共需要训练 30 个受试者

============================================================
训练受试者 1/30: S30
受试者组别: 大样本组(>15个测试样本)
============================================================
正在加载MMEW数据集，测试受试者: S30
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S01 训练数据: 391 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S02 训练数据: 392 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 82 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S03 训练数据: 395 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 69 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S04 训练数据: 393 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S05 训练数据: 387 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 55 个原始样本, 扩充倍数: 1
  加载 S06 训练数据: 382 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S07 训练数据: 402 个样本
    情绪类别 0: 31 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 70 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S08 训练数据: 385 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 65 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S09 训练数据: 384 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 66 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S10 训练数据: 383 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S11 训练数据: 390 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S12 训练数据: 402 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 65 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S13 训练数据: 379 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S14 训练数据: 403 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 63 个原始样本, 扩充倍数: 1
  加载 S15 训练数据: 390 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 62 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S16 训练数据: 383 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S17 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S18 训练数据: 392 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S19 训练数据: 404 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 69 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S20 训练数据: 399 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S21 训练数据: 389 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 70 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S22 训练数据: 399 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S23 训练数据: 406 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S24 训练数据: 401 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S25 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S26 训练数据: 405 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S27 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S28 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S29 训练数据: 403 个样本
    情绪类别 0: 4 个原始样本, 扩充倍数: 1
    情绪类别 1: 14 个原始样本, 扩充倍数: 1
    情绪类别 2: 12 个原始样本, 扩充倍数: 1
    情绪类别 3: 5 个原始样本, 扩充倍数: 1
  加载 S30 测试数据: 35 个样本
数据加载完成 - 训练: 11451, 测试: 35
训练数据最终形状: (11451, 48, 48, 36)
测试数据最终形状: (35, 48, 48, 36)
4分类标签映射完成
训练标签分布: {np.int64(0): np.int64(3036), np.int64(1): np.int64(2506), np.int64(2): np.int64(4056), np.int64(3): np.int64(1853)}
测试标签分布: {np.int64(0): np.int64(4), np.int64(1): np.int64(14), np.int64(2): np.int64(12), np.int64(3): np.int64(5)}
训练样本数: 11451, 测试样本数: 35
训练标签分布: {np.int64(0): np.int64(3036), np.int64(1): np.int64(2506), np.int64(2): np.int64(4056), np.int64(3): np.int64(1853)}
测试标签分布: {np.int64(0): np.int64(4), np.int64(1): np.int64(14), np.int64(2): np.int64(12), np.int64(3): np.int64(5)}
加载预训练模型: /home/<USER>/data/ajq/SKD-TSTSAN-new/Pretrained_model/SKD-TSTSAN.pth
跳过不存在的层: bn1_L.weight
跳过不存在的层: bn1_L.bias
跳过不存在的层: bn1_L.running_mean
跳过不存在的层: bn1_L.running_var
跳过不存在的层: bn1_L.num_batches_tracked
跳过不存在的层: bn1_S.weight
跳过不存在的层: bn1_S.bias
跳过不存在的层: bn1_S.running_mean
跳过不存在的层: bn1_S.running_var
跳过不存在的层: bn1_S.num_batches_tracked
跳过不存在的层: bn1_T.weight
跳过不存在的层: bn1_T.bias
跳过不存在的层: bn1_T.running_mean
跳过不存在的层: bn1_T.running_var
跳过不存在的层: bn1_T.num_batches_tracked
跳过不存在的层: AC1_fc.weight
跳过不存在的层: AC1_fc.bias
跳过不存在的层: AC2_fc.weight
跳过不存在的层: AC2_fc.bias
跳过形状不匹配的层: fc2.weight (预训练: torch.Size([7, 384]), 当前: torch.Size([4, 384]))
跳过形状不匹配的层: fc2.bias (预训练: torch.Size([7]), 当前: torch.Size([4]))
预训练模型部分加载成功，加载了 234/255 层
开始训练受试者 S30，总共1个epoch...
自适应参数 - Alpha: 0.1000, Temperature: 3.0, Beta: 1.00e-06

📊 资源使用情况:
  CPU使用率: 99.3%
  内存使用率: 35.0%
  可用内存: 41.1GB
  GPU内存: 已分配: 0.0GB, 已保留: 0.0GB

Epoch 1/1
Batch 0/1289, Total: 4.5946, Main: 1.4217, KD: 0.0497
Batch 10/1289, Total: 3.3404, Main: 1.2650, KD: 0.0338
Batch 20/1289, Total: 3.4446, Main: 1.2649, KD: 0.0795
Batch 30/1289, Total: 3.9062, Main: 1.0989, KD: 0.0587
Batch 40/1289, Total: 3.0901, Main: 1.1242, KD: 0.0567
Batch 50/1289, Total: 2.0381, Main: 0.8054, KD: 0.0425
Batch 60/1289, Total: 3.0587, Main: 1.0832, KD: 0.0304
Batch 70/1289, Total: 2.6102, Main: 1.0028, KD: 0.0391
Batch 80/1289, Total: 3.3451, Main: 1.1896, KD: 0.0486
Batch 90/1289, Total: 2.0822, Main: 0.7447, KD: 0.0469
Batch 100/1289, Total: 3.3684, Main: 1.1164, KD: 0.0507
Batch 110/1289, Total: 1.9399, Main: 0.5797, KD: 0.0615
Batch 120/1289, Total: 2.5504, Main: 0.8934, KD: 0.0424
Batch 130/1289, Total: 3.3409, Main: 1.0243, KD: 0.0240
Batch 140/1289, Total: 2.3592, Main: 0.7643, KD: 0.0409
Batch 150/1289, Total: 1.1426, Main: 0.4599, KD: 0.0446
Batch 160/1289, Total: 2.2750, Main: 0.7257, KD: 0.0230
Batch 170/1289, Total: 2.5341, Main: 0.8418, KD: 0.0394
Batch 180/1289, Total: 1.3749, Main: 0.4783, KD: 0.0327
Batch 190/1289, Total: 1.1385, Main: 0.5039, KD: 0.0414

训练过程出错: name 'train_losses' is not defined

【MMEW数据集实验结果报告】
================================================================================

================================================================================
【MMEW数据集实验结果报告】
================================================================================

【基本信息】
实验名称: MMEW_4class_feature_fixed
时间: 2025-07-11 15:14:05
总训练时间: 00:01:30
数据集: MMEW_LOSO_full


【系统环境】
操作系统: Linux 6.8.0-1030-nvidia
处理器: 32核
内存: 63.3GB
GPU: NVIDIA A800 80GB PCIe (79.3GB)

【模型配置】
模型架构: SKD_TSTSAN
是否使用预训练: 是
预训练模型: SKD-TSTSAN.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
批次大小: 8
最大迭代次数: 200
损失函数: FocalLoss_weighted
随机种子: 1337

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: N/A
- UAR分数: N/A

【各表情类别准确率】
--------------------------------------------------


【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
    平均        nan        nan        nan    


【最优预测结果与原始标签对比】
--------------------------------------------------

================================================================================

================================================================================

【邮件通知】正在发送训练结果...

【邮件通知】邮件发送成功!
- 发件人: <EMAIL>
- 收件人: <EMAIL>
- 主题: [MMEW实验总结] MMEW_4class_feature_fixed - UF1=N/A, UAR=N/A
