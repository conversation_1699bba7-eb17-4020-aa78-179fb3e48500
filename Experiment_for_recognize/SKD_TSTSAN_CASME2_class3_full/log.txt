
================================================================================
【训练开始】
================================================================================

【硬件信息】使用单个GPU进行训练

【损失函数配置】
使用加权Focal Loss (3分类)
类别权重: ['0.3794', '0.1349', '0.4857']
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub17测试标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
创建受试者目录: sub17
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共953个epoch...

Epoch 1/953

当前蒸馏参数 (3分类):
- 温度: 2.4000
- Alpha: 0.0800
- Beta: 0.00000080

================================================================================
【训练开始】
================================================================================

【硬件信息】使用单个GPU进行训练
✓ TF32加速已启用
✓ 混合精度训练已启用

【损失函数配置】
使用加权Focal Loss (3分类)
类别权重: ['0.3794', '0.1349', '0.4857']
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub17测试标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8

================================================================================
【训练开始】
================================================================================

【硬件信息】使用单个GPU进行训练
✓ TF32加速已启用
✓ 混合精度训练已启用

【损失函数配置】
使用加权Focal Loss (3分类)
类别权重: ['0.3794', '0.1349', '0.4857']
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub17测试标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
✓ 模型编译优化已启用
加载预训练模型...
开始训练,总共953个epoch...

Epoch 1/953

================================================================================
【训练开始】
================================================================================

训练过程出错: cannot access local variable 'torch' where it is not associated with a value

【实验结果报告】
================================================================================

================================================================================
【实验结果报告】
================================================================================

【基本信息】
实验名称: SKD_TSTSAN_CASME2_class3_full
时间: 2025-07-12 16:22:38
总训练时间: 00:00:00
数据集: CASME2_LOSO_full


【系统环境】
操作系统: Linux 6.8.0-1030-nvidia
处理器: 32核
内存: 63.3GB
GPU: NVIDIA A800 80GB PCIe (79.3GB)

【模型配置】
模型架构: SKD_TSTSAN
是否使用预训练: 是
预训练模型: SKD-TSTSAN.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
批次大小: 32
最大迭代次数: 20000
损失函数: FocalLoss_weighted
随机种子: 1337

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: N/A
- UAR分数: N/A

【各表情类别准确率】
--------------------------------------------------


【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
    平均        nan        nan        nan    


【最优预测结果与原始标签对比】
--------------------------------------------------

================================================================================

================================================================================

【邮件通知】正在发送训练结果...

【邮件通知】邮件发送成功!
- 发件人: <EMAIL>
- 收件人: <EMAIL>
- 主题: [实验总结] SKD_TSTSAN_CASME2_class3_full - UF1=N/A, UAR=N/A

================================================================================
【训练开始】
================================================================================

【硬件信息】使用单个GPU进行训练
✗ TF32加速已禁用
✗ 混合精度训练已禁用

【损失函数配置】
使用加权Focal Loss (3分类)
类别权重: ['0.3794', '0.1349', '0.4857']
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub17测试标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共953个epoch...

Epoch 1/953

训练集准确率: 0.7288

开始验证...
验证集准确率: 0.6129 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.6129

Epoch 2/953

训练集准确率: 0.9227

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.6129
保存最佳模型,准确率: 0.6774

Epoch 3/953

训练集准确率: 0.9773

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.6774
保存最佳模型,准确率: 0.6774

Epoch 4/953

训练集准确率: 0.9894

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.6774
保存最佳模型,准确率: 0.7419

Epoch 5/953

训练集准确率: 0.9955

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.7419

Epoch 6/953

训练集准确率: 0.9955

开始验证...
验证集准确率: 0.8065 | 最佳准确率: 0.7419
保存最佳模型,准确率: 0.8065

Epoch 7/953

训练集准确率: 0.9970

开始验证...
验证集准确率: 0.6452 | 最佳准确率: 0.8065

Epoch 8/953

训练集准确率: 0.9970

开始验证...
验证集准确率: 0.8065 | 最佳准确率: 0.8065
保存最佳模型,准确率: 0.8065

Epoch 9/953

训练集准确率: 0.9909

开始验证...
验证集准确率: 0.8065 | 最佳准确率: 0.8065
保存最佳模型,准确率: 0.8065

Epoch 10/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.8065

Epoch 11/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.8065

Epoch 12/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.8065

Epoch 13/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.8387 | 最佳准确率: 0.8065
保存最佳模型,准确率: 0.8387

Epoch 14/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.8387

Epoch 15/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6452 | 最佳准确率: 0.8387

Epoch 16/953

训练集准确率: 0.9924

开始验证...
验证集准确率: 0.6452 | 最佳准确率: 0.8387

Epoch 17/953

训练集准确率: 0.9606

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.8387

Epoch 18/953

训练集准确率: 0.9697

开始验证...
验证集准确率: 0.6129 | 最佳准确率: 0.8387

Epoch 19/953

训练集准确率: 0.9879

开始验证...
验证集准确率: 0.8065 | 最佳准确率: 0.8387

Epoch 20/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.6129 | 最佳准确率: 0.8387

Epoch 21/953

训练集准确率: 0.9894

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.8387

Epoch 22/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.8387

Epoch 23/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.8387

Epoch 24/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.8387

Epoch 25/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.8387

Epoch 26/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.8065 | 最佳准确率: 0.8387

Epoch 27/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7742 | 最佳准确率: 0.8387

Epoch 28/953

训练集准确率: 0.9955

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.8387

Epoch 29/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.8387

Epoch 30/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.8387

Epoch 31/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.8387

Epoch 32/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7742 | 最佳准确率: 0.8387

Epoch 33/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.8710 | 最佳准确率: 0.8387
保存最佳模型,准确率: 0.8710

Epoch 34/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.8065 | 最佳准确率: 0.8710

Epoch 35/953

训练集准确率: 0.9970

开始验证...
验证集准确率: 0.8387 | 最佳准确率: 0.8710

Epoch 36/953

训练集准确率: 0.9970

开始验证...
验证集准确率: 0.5806 | 最佳准确率: 0.8710

Epoch 37/953

训练集准确率: 0.9833

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.8710

Epoch 38/953

训练集准确率: 0.9833

开始验证...
验证集准确率: 0.4516 | 最佳准确率: 0.8710

Epoch 39/953

训练集准确率: 0.9909

开始验证...
验证集准确率: 0.6452 | 最佳准确率: 0.8710

Epoch 40/953

训练集准确率: 0.9970

开始验证...
验证集准确率: 0.7742 | 最佳准确率: 0.8710

Epoch 41/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.7742 | 最佳准确率: 0.8710

Epoch 42/953

训练集准确率: 0.9939

开始验证...
验证集准确率: 0.5806 | 最佳准确率: 0.8710

Epoch 43/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.6452 | 最佳准确率: 0.8710

Epoch 44/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.8710

Epoch 45/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.8710

Epoch 46/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.8387 | 最佳准确率: 0.8710

Epoch 47/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.8710

Epoch 48/953

训练集准确率: 0.9970

开始验证...
验证集准确率: 0.8065 | 最佳准确率: 0.8710

Epoch 49/953

训练集准确率: 0.9818

开始验证...
验证集准确率: 0.4839 | 最佳准确率: 0.8710

Epoch 50/953

训练集准确率: 0.9803

开始验证...
验证集准确率: 0.8387 | 最佳准确率: 0.8710

Epoch 51/953

训练集准确率: 0.9970

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.8710

Epoch 52/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.8065 | 最佳准确率: 0.8710

Epoch 53/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.9032 | 最佳准确率: 0.8710
保存最佳模型,准确率: 0.9032

Epoch 54/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.9032

Epoch 55/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.9032

Epoch 56/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7742 | 最佳准确率: 0.9032

Epoch 57/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.8387 | 最佳准确率: 0.9032

Epoch 58/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.8065 | 最佳准确率: 0.9032

Epoch 59/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.8065 | 最佳准确率: 0.9032

Epoch 60/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.9032

Epoch 61/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.8387 | 最佳准确率: 0.9032

Epoch 62/953

训练集准确率: 0.9788

开始验证...
验证集准确率: 0.6452 | 最佳准确率: 0.9032

Epoch 63/953

训练集准确率: 0.9924

开始验证...
验证集准确率: 0.8065 | 最佳准确率: 0.9032

Epoch 64/953

训练集准确率: 0.9879

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.9032

Epoch 65/953

训练集准确率: 0.9970

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.9032

Epoch 66/953

训练集准确率: 0.9879

开始验证...
验证集准确率: 0.6452 | 最佳准确率: 0.9032

Epoch 67/953

训练集准确率: 0.9970

开始验证...
验证集准确率: 0.4516 | 最佳准确率: 0.9032

Epoch 68/953

训练集准确率: 0.9939

开始验证...
验证集准确率: 0.6452 | 最佳准确率: 0.9032

Epoch 69/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6129 | 最佳准确率: 0.9032

Epoch 70/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6452 | 最佳准确率: 0.9032

Epoch 71/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6129 | 最佳准确率: 0.9032

Epoch 72/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6129 | 最佳准确率: 0.9032

Epoch 73/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.9032

Epoch 74/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6452 | 最佳准确率: 0.9032

Epoch 75/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.9032

Epoch 76/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.5806 | 最佳准确率: 0.9032

Epoch 77/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.9032

Epoch 78/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.8065 | 最佳准确率: 0.9032

Epoch 79/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.8065 | 最佳准确率: 0.9032

Epoch 80/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6452 | 最佳准确率: 0.9032

Epoch 81/953

训练集准确率: 0.9970

开始验证...
验证集准确率: 0.8065 | 最佳准确率: 0.9032

Epoch 82/953

训练集准确率: 0.9894

开始验证...
验证集准确率: 0.9355 | 最佳准确率: 0.9032
保存最佳模型,准确率: 0.9355

Epoch 83/953

训练集准确率: 0.9955

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.9355

Epoch 84/953

训练集准确率: 0.9924

开始验证...
验证集准确率: 0.5161 | 最佳准确率: 0.9355

Epoch 85/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.9355

Epoch 86/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.4839 | 最佳准确率: 0.9355

Epoch 87/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.9355

Epoch 88/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.9355

Epoch 89/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7742 | 最佳准确率: 0.9355

Epoch 90/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7742 | 最佳准确率: 0.9355

Epoch 91/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.9355

Epoch 92/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7742 | 最佳准确率: 0.9355

Epoch 93/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.9355

Epoch 94/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.9355

Epoch 95/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.9355

Epoch 96/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.8387 | 最佳准确率: 0.9355

Epoch 97/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.8387 | 最佳准确率: 0.9355

Epoch 98/953

训练集准确率: 0.9924

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.9355

Epoch 99/953

训练集准确率: 0.9879

开始验证...
验证集准确率: 0.7742 | 最佳准确率: 0.9355

Epoch 100/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.9032 | 最佳准确率: 0.9355

Epoch 101/953

训练集准确率: 0.9924

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.9355

Epoch 102/953

训练集准确率: 0.9848

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.9355

Epoch 103/953

训练集准确率: 0.9970

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.9355

Epoch 104/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.5806 | 最佳准确率: 0.9355

Epoch 105/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.5806 | 最佳准确率: 0.9355

Epoch 106/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.9355

Epoch 107/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.5806 | 最佳准确率: 0.9355

Epoch 108/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.8710 | 最佳准确率: 0.9355

Epoch 109/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.4839 | 最佳准确率: 0.9355

Epoch 110/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.9355

Epoch 111/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.9355

Epoch 112/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.9355

Epoch 113/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6452 | 最佳准确率: 0.9355

Epoch 114/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.5806 | 最佳准确率: 0.9355

Epoch 115/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.9032 | 最佳准确率: 0.9355

Epoch 116/953

训练集准确率: 0.9970

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.9355

Epoch 117/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.9355

Epoch 118/953

训练集准确率: 0.9894

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.9355

Epoch 119/953

训练集准确率: 0.9894

开始验证...
验证集准确率: 0.5806 | 最佳准确率: 0.9355

Epoch 120/953

训练集准确率: 0.9697

开始验证...
验证集准确率: 0.6129 | 最佳准确率: 0.9355

Epoch 121/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.5484 | 最佳准确率: 0.9355

Epoch 122/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.8710 | 最佳准确率: 0.9355

Epoch 123/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6129 | 最佳准确率: 0.9355

Epoch 124/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.9355

Epoch 125/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.9355

Epoch 126/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.9355

Epoch 127/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.9355

Epoch 128/953

================================================================================
【训练开始】
================================================================================

【硬件信息】使用单个GPU进行训练
✓ TF32加速已启用
✗ 混合精度训练已禁用

【损失函数配置】
使用加权Focal Loss (3分类)
类别权重: ['0.3794', '0.1349', '0.4857']
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub17测试标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共953个epoch...

Epoch 1/953

训练集准确率: 0.7318

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.6774

Epoch 2/953

训练集准确率: 0.9258

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.6774
保存最佳模型,准确率: 0.6774

Epoch 3/953

训练集准确率: 0.9773

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.6774
保存最佳模型,准确率: 0.6774

Epoch 4/953

================================================================================
【训练开始】
================================================================================

【硬件信息】使用单个GPU进行训练
✓ TF32加速已启用
✓ 混合精度训练已启用

【损失函数配置】
使用加权Focal Loss (3分类)
类别权重: ['0.3794', '0.1349', '0.4857']
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub17测试标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共953个epoch...

Epoch 1/953

训练集准确率: 0.6818

开始验证...
验证集准确率: 0.4839 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.4839

Epoch 2/953

训练集准确率: 0.9152

开始验证...
验证集准确率: 0.6129 | 最佳准确率: 0.4839
保存最佳模型,准确率: 0.6129

Epoch 3/953

训练集准确率: 0.9652

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.6129
保存最佳模型,准确率: 0.7097

Epoch 4/953

训练集准确率: 0.9848

开始验证...
验证集准确率: 0.6452 | 最佳准确率: 0.7097

Epoch 5/953

训练集准确率: 0.9909

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.7097

Epoch 6/953

训练集准确率: 0.9803

开始验证...
验证集准确率: 0.8065 | 最佳准确率: 0.7097
保存最佳模型,准确率: 0.8065

Epoch 7/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.8065

Epoch 8/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.8065

Epoch 9/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.8387 | 最佳准确率: 0.8065
保存最佳模型,准确率: 0.8387

Epoch 10/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.8387

Epoch 11/953

训练集准确率: 0.9939

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.8387

Epoch 12/953

训练集准确率: 0.9818

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.8387

Epoch 13/953

训练集准确率: 0.9909

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.8387

Epoch 14/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.8387 | 最佳准确率: 0.8387
保存最佳模型,准确率: 0.8387

================================================================================
【训练开始】
================================================================================

【硬件信息】使用单个GPU进行训练
✓ TF32加速已启用
✓ 混合精度训练已启用

【损失函数配置】
使用加权Focal Loss (3分类)
类别权重: ['0.3794', '0.1349', '0.4857']
开始训练...
根据配置,不使用Visdom可视化

========================================
【跳过无效受试者】: sub10
========================================
原因: 该受试者在3分类模式下只包含'其他'类别样本
跳过处理，继续下一个受试者...

========================================
【跳过无效受试者】: sub18
========================================
原因: 该受试者在3分类模式下只包含'其他'类别样本
跳过处理，继续下一个受试者...

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub17测试标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共953个epoch...

Epoch 1/953

训练集准确率: 0.6894

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.6774

Epoch 2/953

训练集准确率: 0.9121

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.6774
保存最佳模型,准确率: 0.7097

Epoch 3/953

训练集准确率: 0.9652

开始验证...
验证集准确率: 0.6452 | 最佳准确率: 0.7097

Epoch 4/953

训练集准确率: 0.9833

开始验证...
验证集准确率: 0.7742 | 最佳准确率: 0.7097
保存最佳模型,准确率: 0.7742

Epoch 5/953

训练集准确率: 0.9955

开始验证...
验证集准确率: 0.6452 | 最佳准确率: 0.7742

Epoch 6/953

训练集准确率: 0.9970

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.7742

Epoch 7/953

训练集准确率: 0.9939

开始验证...
验证集准确率: 0.7742 | 最佳准确率: 0.7742
保存最佳模型,准确率: 0.7742

Epoch 8/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.9355 | 最佳准确率: 0.7742
保存最佳模型,准确率: 0.9355

Epoch 9/953

训练集准确率: 0.9939

开始验证...
验证集准确率: 0.8065 | 最佳准确率: 0.9355

Epoch 10/953

训练集准确率: 0.9848

开始验证...
验证集准确率: 0.9032 | 最佳准确率: 0.9355

Epoch 11/953

训练集准确率: 0.9955

开始验证...
验证集准确率: 0.8065 | 最佳准确率: 0.9355

Epoch 12/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.9355

Epoch 13/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.9355 | 最佳准确率: 0.9355
保存最佳模型,准确率: 0.9355

Epoch 14/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7742 | 最佳准确率: 0.9355

Epoch 15/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.9355 | 最佳准确率: 0.9355
保存最佳模型,准确率: 0.9355

Epoch 16/953

训练集准确率: 0.9970

开始验证...
验证集准确率: 0.8065 | 最佳准确率: 0.9355

Epoch 17/953

训练集准确率: 0.9939

开始验证...
验证集准确率: 0.7742 | 最佳准确率: 0.9355

Epoch 18/953

================================================================================
【训练开始】
================================================================================

【硬件信息】使用单个GPU进行训练
✓ TF32加速已启用
✗ 混合精度训练已禁用

【损失函数配置】
使用加权Focal Loss (3分类)
类别权重: ['0.3794', '0.1349', '0.4857']
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub17测试标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.7297

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.6774
当前主体训练完成
最佳预测结果: [2, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 2, 0, 0, 0, 0, 0, 0]
真实标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 7
TP: 7, FP: 9, FN: 0, TN: 15
F1分数: 0.6087, 召回率: 1.0000

负性(negative)评估结果:
样本数: 23
TP: 13, FP: 0, FN: 10, TN: 8
F1分数: 0.7222, 召回率: 0.5652

惊讶(surprise)评估结果:
样本数: 1
TP: 1, FP: 1, FN: 0, TN: 29
F1分数: 0.6667, 召回率: 1.0000

总体评估结果:
UF1: 0.6659
UAR: 0.8551

正性(positive)评估结果:
样本数: 7
TP: 7, FP: 9, FN: 0, TN: 15
F1分数: 0.6087, 召回率: 1.0000

负性(negative)评估结果:
样本数: 23
TP: 13, FP: 0, FN: 10, TN: 8
F1分数: 0.7222, 召回率: 0.5652

惊讶(surprise)评估结果:
样本数: 1
TP: 1, FP: 1, FN: 0, TN: 29
F1分数: 0.6667, 召回率: 1.0000

总体评估结果:
UF1: 0.6659
UAR: 0.8551
UF1: 0.6659 | UAR: 0.8551
最佳 UF1: 0.6659 | 最佳 UAR: 0.8551

【数据增强统计】
标签 0: 增强了 200 个样本
标签 1: 增强了 130 个样本
标签 2: 增强了 216 个样本

========================================
【当前处理受试者】: sub05
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub05测试标签: [2, 2, 2, 2, 2, 0, 1]
创建受试者目录: sub05
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub05/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.6844

开始验证...
验证集准确率: 0.8571 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.8571
当前主体训练完成
最佳预测结果: [2, 2, 2, 2, 2, 0, 2]
真实标签: [2, 2, 2, 2, 2, 0, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 6
F1分数: 1.0000, 召回率: 1.0000

负性(negative)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 6
F1分数: 0.0000, 召回率: 0.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 5, FP: 1, FN: 0, TN: 1
F1分数: 0.9091, 召回率: 1.0000

总体评估结果:
UF1: 0.6364
UAR: 0.6667

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 6
F1分数: 1.0000, 召回率: 1.0000

负性(negative)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 6
F1分数: 0.0000, 召回率: 0.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 5, FP: 1, FN: 0, TN: 1
F1分数: 0.9091, 召回率: 1.0000

总体评估结果:
UF1: 0.6364
UAR: 0.6667
UF1: 0.7333 | UAR: 0.8472
最佳 UF1: 0.7333 | 最佳 UAR: 0.8472

【数据增强统计】
标签 0: 增强了 448 个样本
标签 1: 增强了 304 个样本
标签 2: 增强了 396 个样本

========================================
【当前处理受试者】: sub26
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub26测试标签: [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1]
创建受试者目录: sub26
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub26/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.6531

开始验证...
验证集准确率: 0.8182 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.8182
当前主体训练完成
最佳预测结果: [0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1]
真实标签: [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 2
TP: 2, FP: 2, FN: 0, TN: 7
F1分数: 0.6667, 召回率: 1.0000

负性(negative)评估结果:
样本数: 9
TP: 7, FP: 0, FN: 2, TN: 2
F1分数: 0.8750, 召回率: 0.7778
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.7708
UAR: 0.8889

正性(positive)评估结果:
样本数: 2
TP: 2, FP: 2, FN: 0, TN: 7
F1分数: 0.6667, 召回率: 1.0000

负性(negative)评估结果:
样本数: 9
TP: 7, FP: 0, FN: 2, TN: 2
F1分数: 0.8750, 召回率: 0.7778
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.7708
UAR: 0.8889
UF1: 0.7523 | UAR: 0.8687
最佳 UF1: 0.7523 | 最佳 UAR: 0.8687

【数据增强统计】
标签 0: 增强了 688 个样本
标签 1: 增强了 462 个样本
标签 2: 增强了 621 个样本

========================================
【当前处理受试者】: sub19
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub19测试标签: [2, 2, 2, 2, 2, 0, 0, 0, 1, 1, 1]
创建受试者目录: sub19
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub19/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.7047

开始验证...
验证集准确率: 0.7273 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.7273
当前主体训练完成
最佳预测结果: [2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 0]
真实标签: [2, 2, 2, 2, 2, 0, 0, 0, 1, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 3
TP: 3, FP: 3, FN: 0, TN: 5
F1分数: 0.6667, 召回率: 1.0000

负性(negative)评估结果:
样本数: 3
TP: 0, FP: 0, FN: 3, TN: 8
F1分数: 0.0000, 召回率: 0.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 6
F1分数: 1.0000, 召回率: 1.0000

总体评估结果:
UF1: 0.5556
UAR: 0.6667

正性(positive)评估结果:
样本数: 3
TP: 3, FP: 3, FN: 0, TN: 5
F1分数: 0.6667, 召回率: 1.0000

负性(negative)评估结果:
样本数: 3
TP: 0, FP: 0, FN: 3, TN: 8
F1分数: 0.0000, 召回率: 0.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 6
F1分数: 1.0000, 召回率: 1.0000

总体评估结果:
UF1: 0.5556
UAR: 0.6667
UF1: 0.7603 | UAR: 0.8519
最佳 UF1: 0.7603 | 最佳 UAR: 0.8519

【数据增强统计】
标签 0: 增强了 920 个样本
标签 1: 增强了 632 个样本
标签 2: 增强了 801 个样本

========================================
【当前处理受试者】: sub09
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub09测试标签: [0, 0, 0, 0, 0, 1, 1, 1, 1, 1]
创建受试者目录: sub09
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub09/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.7078

开始验证...
验证集准确率: 0.4000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.4000
当前主体训练完成
最佳预测结果: [1, 1, 0, 1, 1, 2, 1, 2, 1, 1]
真实标签: [0, 0, 0, 0, 0, 1, 1, 1, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 5
TP: 1, FP: 0, FN: 4, TN: 5
F1分数: 0.3333, 召回率: 0.2000

负性(negative)评估结果:
样本数: 5
TP: 3, FP: 4, FN: 2, TN: 1
F1分数: 0.5000, 召回率: 0.6000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.4167
UAR: 0.4000

正性(positive)评估结果:
样本数: 5
TP: 1, FP: 0, FN: 4, TN: 5
F1分数: 0.3333, 召回率: 0.2000

负性(negative)评估结果:
样本数: 5
TP: 3, FP: 4, FN: 2, TN: 1
F1分数: 0.5000, 召回率: 0.6000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.4167
UAR: 0.4000
UF1: 0.7104 | UAR: 0.7796
最佳 UF1: 0.7104 | 最佳 UAR: 0.7796

【数据增强统计】
标签 0: 增强了 1136 个样本
标签 1: 增强了 798 个样本
标签 2: 增强了 1026 个样本

========================================
【当前处理受试者】: sub02
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub02测试标签: [2, 2, 2, 0, 1, 1, 1, 1, 1]
创建受试者目录: sub02
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub02/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.6891

开始验证...
验证集准确率: 0.3333 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.3333
当前主体训练完成
最佳预测结果: [2, 2, 2, 2, 0, 0, 0, 0, 0]
真实标签: [2, 2, 2, 0, 1, 1, 1, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 1
TP: 0, FP: 5, FN: 1, TN: 3
F1分数: 0.0000, 召回率: 0.0000

负性(negative)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 4
F1分数: 0.0000, 召回率: 0.0000

惊讶(surprise)评估结果:
样本数: 3
TP: 3, FP: 1, FN: 0, TN: 5
F1分数: 0.8571, 召回率: 1.0000

总体评估结果:
UF1: 0.2857
UAR: 0.3333

正性(positive)评估结果:
样本数: 1
TP: 0, FP: 5, FN: 1, TN: 3
F1分数: 0.0000, 召回率: 0.0000

负性(negative)评估结果:
样本数: 5
TP: 0, FP: 0, FN: 5, TN: 4
F1分数: 0.0000, 召回率: 0.0000

惊讶(surprise)评估结果:
样本数: 3
TP: 3, FP: 1, FN: 0, TN: 5
F1分数: 0.8571, 召回率: 1.0000

总体评估结果:
UF1: 0.2857
UAR: 0.3333
UF1: 0.6724 | UAR: 0.7456
最佳 UF1: 0.6724 | 最佳 UAR: 0.7456

【数据增强统计】
标签 0: 增强了 1384 个样本
标签 1: 增强了 964 个样本
标签 2: 增强了 1224 个样本

========================================
【当前处理受试者】: sub23
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub23测试标签: [0, 1, 1, 1, 1, 1, 1, 1]
创建受试者目录: sub23
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub23/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.6672

开始验证...
验证集准确率: 0.1250 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.1250
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 0, 0, 0, 0]
真实标签: [0, 1, 1, 1, 1, 1, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 7, FN: 0, TN: 0
F1分数: 0.2222, 召回率: 1.0000

负性(negative)评估结果:
样本数: 7
TP: 0, FP: 0, FN: 7, TN: 1
F1分数: 0.0000, 召回率: 0.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.1111
UAR: 0.5000

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 7, FN: 0, TN: 0
F1分数: 0.2222, 召回率: 1.0000

负性(negative)评估结果:
样本数: 7
TP: 0, FP: 0, FN: 7, TN: 1
F1分数: 0.0000, 召回率: 0.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.1111
UAR: 0.5000
UF1: 0.6384 | UAR: 0.728
最佳 UF1: 0.6384 | 最佳 UAR: 0.728

【数据增强统计】
标签 0: 增强了 1632 个样本
标签 1: 增强了 1126 个样本
标签 2: 增强了 1449 个样本

========================================
【当前处理受试者】: sub12
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub12测试标签: [2, 2, 2, 2, 0, 0, 1, 1, 1, 1, 1]
创建受试者目录: sub12
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub12/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.6844

开始验证...
验证集准确率: 1.0000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 1.0000
达到完美准确率,提前结束训练
当前主体训练完成
最佳预测结果: [2, 2, 2, 2, 0, 0, 1, 1, 1, 1, 1]
真实标签: [2, 2, 2, 2, 0, 0, 1, 1, 1, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 9
F1分数: 1.0000, 召回率: 1.0000

负性(negative)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 6
F1分数: 1.0000, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 4
TP: 4, FP: 0, FN: 0, TN: 7
F1分数: 1.0000, 召回率: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 9
F1分数: 1.0000, 召回率: 1.0000

负性(negative)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 6
F1分数: 1.0000, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 4
TP: 4, FP: 0, FN: 0, TN: 7
F1分数: 1.0000, 召回率: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 0.6744 | UAR: 0.7518
最佳 UF1: 0.6744 | 最佳 UAR: 0.7518

【数据增强统计】
标签 0: 增强了 1872 个样本
标签 1: 增强了 1292 个样本
标签 2: 增强了 1638 个样本

========================================
【当前处理受试者】: sub11
========================================
组别: 小样本组(3-6个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub11测试标签: [1, 1, 1, 1]
创建受试者目录: sub11
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub11/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.6672

开始验证...
验证集准确率: 0.5000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.5000
当前主体训练完成
最佳预测结果: [2, 0, 1, 1]
真实标签: [1, 1, 1, 1]
当前评估结果:
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 4
TP: 2, FP: 0, FN: 2, TN: 0
F1分数: 0.6667, 召回率: 0.5000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.6667
UAR: 0.5000
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 4
TP: 2, FP: 0, FN: 2, TN: 0
F1分数: 0.6667, 召回率: 0.5000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.6667
UAR: 0.5000
UF1: 0.6658 | UAR: 0.7522
最佳 UF1: 0.6658 | 最佳 UAR: 0.7522

【数据增强统计】
标签 0: 增强了 2128 个样本
标签 1: 增强了 1460 个样本
标签 2: 增强了 1863 个样本

========================================
【当前处理受试者】: sub01
========================================
组别: 小样本组(3-6个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub01测试标签: [0, 1, 1]
创建受试者目录: sub01
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub01/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.6484

开始验证...
验证集准确率: 0.3333 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.3333
当前主体训练完成
最佳预测结果: [0, 2, 2]
真实标签: [0, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 2
F1分数: 1.0000, 召回率: 1.0000

负性(negative)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 1
F1分数: 0.0000, 召回率: 0.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.5000
UAR: 0.5000

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 2
F1分数: 1.0000, 召回率: 1.0000

负性(negative)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 1
F1分数: 0.0000, 召回率: 0.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.5000
UAR: 0.5000
UF1: 0.6533 | UAR: 0.7505
最佳 UF1: 0.6533 | 最佳 UAR: 0.7505

【数据增强统计】
标签 0: 增强了 2376 个样本
标签 1: 增强了 1632 个样本
标签 2: 增强了 2088 个样本

========================================
【当前处理受试者】: sub07
========================================
组别: 小样本组(3-6个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub07测试标签: [1, 1, 1, 1, 1]
创建受试者目录: sub07
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub07/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.6328

开始验证...
验证集准确率: 0.6000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.6000
当前主体训练完成
最佳预测结果: [1, 1, 1, 2, 2]
真实标签: [1, 1, 1, 1, 1]
当前评估结果:
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 5
TP: 3, FP: 0, FN: 2, TN: 0
F1分数: 0.7500, 召回率: 0.6000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.7500
UAR: 0.6000
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 5
TP: 3, FP: 0, FN: 2, TN: 0
F1分数: 0.7500, 召回率: 0.6000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.7500
UAR: 0.6000
UF1: 0.6449 | UAR: 0.7536
最佳 UF1: 0.6449 | 最佳 UAR: 0.7536

【数据增强统计】
标签 0: 增强了 2632 个样本
标签 1: 增强了 1798 个样本
标签 2: 增强了 2313 个样本

========================================
【当前处理受试者】: sub24
========================================
组别: 小样本组(3-6个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub24测试标签: [2, 1, 1]
创建受试者目录: sub24
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub24/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.6672

开始验证...
验证集准确率: 0.3333 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.3333
当前主体训练完成
最佳预测结果: [2, 0, 0]
真实标签: [2, 1, 1]
当前评估结果:
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 1
F1分数: 0.0000, 召回率: 0.0000

惊讶(surprise)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 2
F1分数: 1.0000, 召回率: 1.0000

总体评估结果:
UF1: 0.5000
UAR: 0.5000
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 1
F1分数: 0.0000, 召回率: 0.0000

惊讶(surprise)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 2
F1分数: 1.0000, 召回率: 1.0000

总体评估结果:
UF1: 0.5000
UAR: 0.5000
UF1: 0.639 | UAR: 0.7491
最佳 UF1: 0.639 | 最佳 UAR: 0.7491

【数据增强统计】
标签 0: 增强了 2888 个样本
标签 1: 增强了 1970 个样本
标签 2: 增强了 2529 个样本

========================================
【当前处理受试者】: sub25
========================================
组别: 小样本组(3-6个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub25测试标签: [2, 2, 1, 1, 1]
创建受试者目录: sub25
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub25/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.7250

开始验证...
验证集准确率: 0.2000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.2000
当前主体训练完成
最佳预测结果: [2, 0, 0, 0, 2]
真实标签: [2, 2, 1, 1, 1]
当前评估结果:
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 3
TP: 0, FP: 0, FN: 3, TN: 2
F1分数: 0.0000, 召回率: 0.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 1, FP: 1, FN: 1, TN: 2
F1分数: 0.5000, 召回率: 0.5000

总体评估结果:
UF1: 0.2500
UAR: 0.2500
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 3
TP: 0, FP: 0, FN: 3, TN: 2
F1分数: 0.0000, 召回率: 0.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 1, FP: 1, FN: 1, TN: 2
F1分数: 0.5000, 召回率: 0.5000

总体评估结果:
UF1: 0.2500
UAR: 0.2500
UF1: 0.619 | UAR: 0.727
最佳 UF1: 0.619 | 最佳 UAR: 0.727

【数据增强统计】
标签 0: 增强了 3144 个样本
标签 1: 增强了 2140 个样本
标签 2: 增强了 2736 个样本

========================================
【当前处理受试者】: sub03
========================================
组别: 小样本组(3-6个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub03测试标签: [2, 1, 1, 1, 1]
创建受试者目录: sub03
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub03/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.6984

开始验证...
验证集准确率: 0.8000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.8000
当前主体训练完成
最佳预测结果: [2, 1, 0, 1, 1]
真实标签: [2, 1, 1, 1, 1]
当前评估结果:
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 4
TP: 3, FP: 0, FN: 1, TN: 1
F1分数: 0.8571, 召回率: 0.7500

惊讶(surprise)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 4
F1分数: 1.0000, 召回率: 1.0000

总体评估结果:
UF1: 0.9286
UAR: 0.8750
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 4
TP: 3, FP: 0, FN: 1, TN: 1
F1分数: 0.8571, 召回率: 0.7500

惊讶(surprise)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 4
F1分数: 1.0000, 召回率: 1.0000

总体评估结果:
UF1: 0.9286
UAR: 0.8750
UF1: 0.6248 | UAR: 0.7329
最佳 UF1: 0.6248 | 最佳 UAR: 0.7329

【数据增强统计】
标签 0: 增强了 3400 个样本
标签 1: 增强了 2308 个样本
标签 2: 增强了 2952 个样本

========================================
【当前处理受试者】: sub06
========================================
组别: 小样本组(3-6个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub06测试标签: [2, 2, 0, 1]
创建受试者目录: sub06
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub06/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.6937

开始验证...
验证集准确率: 0.7500 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.7500
当前主体训练完成
最佳预测结果: [2, 2, 0, 0]
真实标签: [2, 2, 0, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 1, FN: 0, TN: 2
F1分数: 0.6667, 召回率: 1.0000

负性(negative)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 3
F1分数: 0.0000, 召回率: 0.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 2
F1分数: 1.0000, 召回率: 1.0000

总体评估结果:
UF1: 0.5556
UAR: 0.6667

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 1, FN: 0, TN: 2
F1分数: 0.6667, 召回率: 1.0000

负性(negative)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 3
F1分数: 0.0000, 召回率: 0.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 2
F1分数: 1.0000, 召回率: 1.0000

总体评估结果:
UF1: 0.5556
UAR: 0.6667
UF1: 0.6306 | UAR: 0.7352
最佳 UF1: 0.6306 | 最佳 UAR: 0.7352

【数据增强统计】
标签 0: 增强了 3648 个样本
标签 1: 增强了 2482 个样本
标签 2: 增强了 3159 个样本

========================================
【当前处理受试者】: sub15
========================================
组别: 小样本组(3-6个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...

sub15测试标签: [2, 0, 1]
创建受试者目录: sub15
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub15/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.6766

开始验证...
验证集准确率: 1.0000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 1.0000
达到完美准确率,提前结束训练
当前主体训练完成
最佳预测结果: [2, 0, 1]
真实标签: [2, 0, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 2
F1分数: 1.0000, 召回率: 1.0000

负性(negative)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 2
F1分数: 1.0000, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 2
F1分数: 1.0000, 召回率: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 2
F1分数: 1.0000, 召回率: 1.0000

负性(negative)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 2
F1分数: 1.0000, 召回率: 1.0000

惊讶(surprise)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 2
F1分数: 1.0000, 召回率: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 0.6393 | UAR: 0.7408
最佳 UF1: 0.6393 | 最佳 UAR: 0.7408

【数据增强统计】
标签 0: 增强了 3896 个样本
标签 1: 增强了 2656 个样本
标签 2: 增强了 3375 个样本

========================================
【当前处理受试者】: sub20
========================================
组别: 极小样本组(<=2个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub20测试标签: [1, 1]
创建受试者目录: sub20
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub20/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.7188

开始验证...
验证集准确率: 0.5000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.5000
当前主体训练完成
最佳预测结果: [2, 1]
真实标签: [1, 1]
当前评估结果:
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 2
TP: 1, FP: 0, FN: 1, TN: 0
F1分数: 0.6667, 召回率: 0.5000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.6667
UAR: 0.5000
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 2
TP: 1, FP: 0, FN: 1, TN: 0
F1分数: 0.6667, 召回率: 0.5000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.6667
UAR: 0.5000
UF1: 0.6354 | UAR: 0.7411
最佳 UF1: 0.6354 | 最佳 UAR: 0.7411

【数据增强统计】
标签 0: 增强了 4152 个样本
标签 1: 增强了 2828 个样本
标签 2: 增强了 3600 个样本

========================================
【当前处理受试者】: sub04
========================================
组别: 极小样本组(<=2个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub04测试标签: [1, 1]
创建受试者目录: sub04
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub04/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.6719

开始验证...
验证集准确率: 0.5000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.5000
当前主体训练完成
最佳预测结果: [1, 2]
真实标签: [1, 1]
当前评估结果:
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 2
TP: 1, FP: 0, FN: 1, TN: 0
F1分数: 0.6667, 召回率: 0.5000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.6667
UAR: 0.5000
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 2
TP: 1, FP: 0, FN: 1, TN: 0
F1分数: 0.6667, 召回率: 0.5000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.6667
UAR: 0.5000
UF1: 0.6316 | UAR: 0.7414
最佳 UF1: 0.6316 | 最佳 UAR: 0.7414

【数据增强统计】
标签 0: 增强了 4408 个样本
标签 1: 增强了 3000 个样本
标签 2: 增强了 3825 个样本

========================================
【当前处理受试者】: sub13
========================================
组别: 极小样本组(<=2个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub13测试标签: [0, 0]
创建受试者目录: sub13
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub13/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.6453

开始验证...
验证集准确率: 1.0000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 1.0000
达到完美准确率,提前结束训练
当前主体训练完成
最佳预测结果: [0, 0]
真实标签: [0, 0]
当前评估结果:

正性(positive)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 0
F1分数: 1.0000, 召回率: 1.0000
负性(negative): 没有样本
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 0
F1分数: 1.0000, 召回率: 1.0000
负性(negative): 没有样本
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 0.6395 | UAR: 0.7464
最佳 UF1: 0.6395 | 最佳 UAR: 0.7464

【数据增强统计】
标签 0: 增强了 4648 个样本
标签 1: 增强了 3176 个样本
标签 2: 增强了 4050 个样本

========================================
【当前处理受试者】: sub08
========================================
组别: 极小样本组(<=2个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub08测试标签: [1]
创建受试者目录: sub08
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub08/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.7031

开始验证...
验证集准确率: 0.0000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.0000
当前主体训练完成
最佳预测结果: [0]
真实标签: [1]
当前评估结果:
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 0
F1分数: 0.0000, 召回率: 0.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.0000
UAR: 0.0000
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 0
F1分数: 0.0000, 召回率: 0.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.0000
UAR: 0.0000
UF1: 0.6358 | UAR: 0.7445
最佳 UF1: 0.6358 | 最佳 UAR: 0.7445

【数据增强统计】
标签 0: 增强了 4904 个样本
标签 1: 增强了 3350 个样本
标签 2: 增强了 4275 个样本

========================================
【当前处理受试者】: sub16
========================================
组别: 极小样本组(<=2个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub16测试标签: [0, 0]
创建受试者目录: sub16
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub16/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.7141

开始验证...
验证集准确率: 0.0000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.0000
当前主体训练完成
最佳预测结果: [2, 2]
真实标签: [0, 0]
当前评估结果:

正性(positive)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 0
F1分数: 0.0000, 召回率: 0.0000
负性(negative): 没有样本
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.0000
UAR: 0.0000

正性(positive)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 0
F1分数: 0.0000, 召回率: 0.0000
负性(negative): 没有样本
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.0000
UAR: 0.0000
UF1: 0.6237 | UAR: 0.7258
最佳 UF1: 0.6237 | 最佳 UAR: 0.7258

【数据增强统计】
标签 0: 增强了 5144 个样本
标签 1: 增强了 3526 个样本
标签 2: 增强了 4500 个样本

========================================
【当前处理受试者】: sub21
========================================
组别: 极小样本组(<=2个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub21测试标签: [1]
创建受试者目录: sub21
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub21/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.7078

开始验证...
验证集准确率: 0.0000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.0000
当前主体训练完成
最佳预测结果: [0]
真实标签: [1]
当前评估结果:
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 0
F1分数: 0.0000, 召回率: 0.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.0000
UAR: 0.0000
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 1
TP: 0, FP: 0, FN: 1, TN: 0
F1分数: 0.0000, 召回率: 0.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.0000
UAR: 0.0000
UF1: 0.6201 | UAR: 0.724
最佳 UF1: 0.6201 | 最佳 UAR: 0.724

【数据增强统计】
标签 0: 增强了 5400 个样本
标签 1: 增强了 3700 个样本
标签 2: 增强了 4725 个样本

========================================
【当前处理受试者】: sub22
========================================
组别: 极小样本组(<=2个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...

sub22测试标签: [1, 1]
创建受试者目录: sub22
创建日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub22/logs
初始化模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
加载预训练模型...
开始训练,总共1个epoch...

Epoch 1/1

训练集准确率: 0.6375

开始验证...
验证集准确率: 0.0000 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.0000
当前主体训练完成
最佳预测结果: [0, 0]
真实标签: [1, 1]
当前评估结果:
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 0
F1分数: 0.0000, 召回率: 0.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.0000
UAR: 0.0000
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 2
TP: 0, FP: 0, FN: 2, TN: 0
F1分数: 0.0000, 召回率: 0.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 0.0000
UAR: 0.0000
UF1: 0.6133 | UAR: 0.7206
最佳 UF1: 0.6133 | 最佳 UAR: 0.7206

【数据增强统计】
标签 0: 增强了 5656 个样本
标签 1: 增强了 3872 个样本
标签 2: 增强了 4950 个样本
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

【损失函数配置】
使用加权Focal Loss (3分类)
类别权重: ['0.3794', '0.1349', '0.4857']
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub17测试标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
TensorBoard日志目录已存在: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/logs
初始化WTConv小波变换卷积模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
✅ 启用WTConv小波变换卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用WTConv
  - 在中间卷积层(conv2_L, conv2_S)中使用WTConv
  - 通过小波变换实现多频响应特征提取
  - 同时捕获微表情的全局结构和局部细节
  - 实现非常大的感受野而不会过度参数化
  - 特别适合微表情的频域特征分析

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...
开始训练,总共953个epoch...

Epoch 1/953

训练集准确率: 0.6500

开始验证...
验证集准确率: 0.1935 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.1935
模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth

Epoch 2/953

训练集准确率: 0.8879

开始验证...
验证集准确率: 0.2258 | 最佳准确率: 0.1935
保存最佳模型,准确率: 0.2258
模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth

Epoch 3/953

训练集准确率: 0.9485

开始验证...
验证集准确率: 0.5484 | 最佳准确率: 0.2258
保存最佳模型,准确率: 0.5484
模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth

Epoch 4/953

训练集准确率: 0.9894

开始验证...
验证集准确率: 0.6452 | 最佳准确率: 0.5484
保存最佳模型,准确率: 0.6452
模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth

Epoch 5/953

训练集准确率: 0.9970

开始验证...
验证集准确率: 0.8387 | 最佳准确率: 0.6452
保存最佳模型,准确率: 0.8387
模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth

Epoch 6/953

训练集准确率: 0.9955

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.8387

Epoch 7/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.8387

Epoch 8/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.8387

Epoch 9/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.8387

Epoch 10/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.8387

Epoch 11/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.8387 | 最佳准确率: 0.8387
保存最佳模型,准确率: 0.8387
模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth

Epoch 12/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.8387

Epoch 13/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.8065 | 最佳准确率: 0.8387

Epoch 14/953

训练集准确率: 0.9939

开始验证...
验证集准确率: 0.8387 | 最佳准确率: 0.8387
保存最佳模型,准确率: 0.8387
模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth

Epoch 15/953

训练集准确率: 0.9955

开始验证...
验证集准确率: 0.8710 | 最佳准确率: 0.8387
保存最佳模型,准确率: 0.8710
模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth

Epoch 16/953
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

【损失函数配置】
使用加权Focal Loss (3分类)
类别权重: ['0.3794', '0.1349', '0.4857']
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub17测试标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
TensorBoard日志目录已存在: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/logs
初始化WTConv小波变换卷积模型...
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
✅ 启用WTConv小波变换卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用WTConv
  - 在中间卷积层(conv2_L, conv2_S)中使用WTConv
  - 通过小波变换实现多频响应特征提取
  - 同时捕获微表情的全局结构和局部细节
  - 实现非常大的感受野而不会过度参数化
  - 特别适合微表情的频域特征分析

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...
开始训练,总共953个epoch...

Epoch 1/953

训练集准确率: 0.6470

开始验证...
验证集准确率: 0.1935 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.1935
模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth

Epoch 2/953

训练集准确率: 0.8909

开始验证...
验证集准确率: 0.2581 | 最佳准确率: 0.1935
保存最佳模型,准确率: 0.2581
模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth

Epoch 3/953

训练集准确率: 0.9530

开始验证...
验证集准确率: 0.4839 | 最佳准确率: 0.2581
保存最佳模型,准确率: 0.4839
模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth

Epoch 4/953

训练集准确率: 0.9894

开始验证...
验证集准确率: 0.6452 | 最佳准确率: 0.4839
保存最佳模型,准确率: 0.6452
模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth

Epoch 5/953

训练集准确率: 0.9909

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.6452
保存最佳模型,准确率: 0.7097
模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth

Epoch 6/953

训练集准确率: 0.9955

开始验证...
验证集准确率: 0.8065 | 最佳准确率: 0.7097
保存最佳模型,准确率: 0.8065
模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full/sub17/sub17.pth

Epoch 7/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.8065

Epoch 8/953

训练集准确率: 0.9985

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.8065

Epoch 9/953

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.8065

Epoch 10/953

训练集准确率: 0.9924

开始验证...
验证集准确率: 0.7419 | 最佳准确率: 0.8065

Epoch 11/953

训练集准确率: 0.9955

开始验证...
验证集准确率: 0.7742 | 最佳准确率: 0.8065

Epoch 12/953

训练集准确率: 0.9924

开始验证...
验证集准确率: 0.7097 | 最佳准确率: 0.8065

Epoch 13/953

训练集准确率: 0.9970

开始验证...
验证集准确率: 0.6774 | 最佳准确率: 0.8065

Epoch 14/953
