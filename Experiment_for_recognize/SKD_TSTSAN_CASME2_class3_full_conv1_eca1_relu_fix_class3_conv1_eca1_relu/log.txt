日志系统初始化成功

================================================================================
【训练开始 - GPU预加载 + 并行训练模式】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (3分类)[0m
[1;96m🎯 类别权重: ['0.3794', '0.1349', '0.4857'][0m
开始训练...

[1m[1;94m⚙️ 训练模式配置[0m
[1;94m----------[0m
[1;96mℹ️ GPU数据预加载: ✅ 启用[0m
[1;96mℹ️ 并行训练: ✅ 启用[0m
[1;96mℹ️ 并行工作线程数: 4[0m

[1m[1;96m🚀 GPU数据预加载器初始化[0m
[1;96m----------------[0m
[1;96mℹ️ 数据集路径: ./CASME2_LOSO_full[0m
[1;96mℹ️ 分类数量: 3[0m
[1;96mℹ️ 目标设备: cuda[0m

[1;92m========================[0m
[1m[1;92m🚀 开始预加载CASME2数据集到GPU显存 🚀[0m
[1;92m========================[0m

[1;96m📊 正在估算数据集显存需求...[0m
[1;92m📈 估算结果: 6344 个样本[0m
[1;92m💾 预计显存需求: 2.07 GB[0m
[1;96mℹ️ GPU总显存: 79.25 GB[0m
[1;96mℹ️ 可用显存: 63.40 GB[0m
[1;96mℹ️ 发现 26 个受试者: ['sub01', 'sub02', 'sub03', 'sub04', 'sub05', 'sub06', 'sub07', 'sub08', 'sub09', 'sub10', 'sub11', 'sub12', 'sub13', 'sub14', 'sub15', 'sub16', 'sub17', 'sub18', 'sub19', 'sub20', 'sub21', 'sub22', 'sub23', 'sub24', 'sub25', 'sub26'][0m
[1;96m📊 预加载进度: 1/26 - sub01[0m
[1;96m📥 正在预加载受试者 sub01 的数据...[0m
[1;92m✅ 受试者 sub01 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 0.05 GB[0m
[1;96m📊 预加载进度: 2/26 - sub02[0m
[1;96m📥 正在预加载受试者 sub02 的数据...[0m
[1;92m✅ 受试者 sub02 数据预加载完成[0m
[1;96mℹ️ 训练样本: 136, 测试样本: 9[0m
[1;96m💾 当前显存使用: 0.10 GB[0m
[1;96m📊 预加载进度: 3/26 - sub03[0m
[1;96m📥 正在预加载受试者 sub03 的数据...[0m
[1;92m✅ 受试者 sub03 数据预加载完成[0m
[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96m💾 当前显存使用: 0.14 GB[0m
[1;96m📊 预加载进度: 4/26 - sub04[0m
[1;96m📥 正在预加载受试者 sub04 的数据...[0m
[1;92m✅ 受试者 sub04 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.19 GB[0m
[1;96m📊 预加载进度: 5/26 - sub05[0m
[1;96m📥 正在预加载受试者 sub05 的数据...[0m
[1;92m✅ 受试者 sub05 数据预加载完成[0m
[1;96mℹ️ 训练样本: 138, 测试样本: 7[0m
[1;96m💾 当前显存使用: 0.24 GB[0m
[1;96m📊 预加载进度: 6/26 - sub06[0m
[1;96m📥 正在预加载受试者 sub06 的数据...[0m
[1;92m✅ 受试者 sub06 数据预加载完成[0m
[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m
[1;96m💾 当前显存使用: 0.29 GB[0m
[1;96m📊 预加载进度: 7/26 - sub07[0m
[1;96m📥 正在预加载受试者 sub07 的数据...[0m
[1;92m✅ 受试者 sub07 数据预加载完成[0m
[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96m💾 当前显存使用: 0.33 GB[0m
[1;96m📊 预加载进度: 8/26 - sub08[0m
[1;96m📥 正在预加载受试者 sub08 的数据...[0m
[1;92m✅ 受试者 sub08 数据预加载完成[0m
[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m
[1;96m💾 当前显存使用: 0.38 GB[0m
[1;96m📊 预加载进度: 9/26 - sub09[0m
[1;96m📥 正在预加载受试者 sub09 的数据...[0m
[1;92m✅ 受试者 sub09 数据预加载完成[0m
[1;96mℹ️ 训练样本: 135, 测试样本: 10[0m
[1;96m💾 当前显存使用: 0.43 GB[0m
[1;96m📊 预加载进度: 10/26 - sub10[0m
[1;96m📥 正在预加载受试者 sub10 的数据...[0m
[1;92m✅ 受试者 sub10 数据预加载完成[0m
[1;96mℹ️ 训练样本: 145, 测试样本: 0[0m
[1;96m💾 当前显存使用: 0.48 GB[0m
[1;96m📊 预加载进度: 11/26 - sub11[0m
[1;96m📥 正在预加载受试者 sub11 的数据...[0m
[1;92m✅ 受试者 sub11 数据预加载完成[0m
[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m
[1;96m💾 当前显存使用: 0.53 GB[0m
[1;96m📊 预加载进度: 12/26 - sub12[0m
[1;96m📥 正在预加载受试者 sub12 的数据...[0m
[1;92m✅ 受试者 sub12 数据预加载完成[0m
[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m
[1;96m💾 当前显存使用: 0.57 GB[0m
[1;96m📊 预加载进度: 13/26 - sub13[0m
[1;96m📥 正在预加载受试者 sub13 的数据...[0m
[1;92m✅ 受试者 sub13 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.62 GB[0m
[1;96m📊 预加载进度: 14/26 - sub14[0m
[1;96m📥 正在预加载受试者 sub14 的数据...[0m
[1;92m✅ 受试者 sub14 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 0.67 GB[0m
[1;96m📊 预加载进度: 15/26 - sub15[0m
[1;96m📥 正在预加载受试者 sub15 的数据...[0m
[1;92m✅ 受试者 sub15 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 0.72 GB[0m
[1;96m📊 预加载进度: 16/26 - sub16[0m
[1;96m📥 正在预加载受试者 sub16 的数据...[0m
[1;92m✅ 受试者 sub16 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.76 GB[0m
[1;96m📊 预加载进度: 17/26 - sub17[0m
[1;96m📥 正在预加载受试者 sub17 的数据...[0m
[1;92m✅ 受试者 sub17 数据预加载完成[0m
[1;96mℹ️ 训练样本: 114, 测试样本: 31[0m
[1;96m💾 当前显存使用: 0.81 GB[0m
[1;96m📊 预加载进度: 18/26 - sub18[0m
[1;96m📥 正在预加载受试者 sub18 的数据...[0m
[1;92m✅ 受试者 sub18 数据预加载完成[0m
[1;96mℹ️ 训练样本: 145, 测试样本: 0[0m
[1;96m💾 当前显存使用: 0.86 GB[0m
[1;96m📊 预加载进度: 19/26 - sub19[0m
[1;96m📥 正在预加载受试者 sub19 的数据...[0m
[1;92m✅ 受试者 sub19 数据预加载完成[0m
[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m
[1;96m💾 当前显存使用: 0.91 GB[0m
[1;96m📊 预加载进度: 20/26 - sub20[0m
[1;96m📥 正在预加载受试者 sub20 的数据...[0m
[1;92m✅ 受试者 sub20 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.95 GB[0m
[1;96m📊 预加载进度: 21/26 - sub21[0m
[1;96m📥 正在预加载受试者 sub21 的数据...[0m
[1;92m✅ 受试者 sub21 数据预加载完成[0m
[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m
[1;96m💾 当前显存使用: 1.00 GB[0m
[1;96m📊 预加载进度: 22/26 - sub22[0m
[1;96m📥 正在预加载受试者 sub22 的数据...[0m
[1;92m✅ 受试者 sub22 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 1.05 GB[0m
[1;96m📊 预加载进度: 23/26 - sub23[0m
[1;96m📥 正在预加载受试者 sub23 的数据...[0m
[1;92m✅ 受试者 sub23 数据预加载完成[0m
[1;96mℹ️ 训练样本: 137, 测试样本: 8[0m
[1;96m💾 当前显存使用: 1.10 GB[0m
[1;96m📊 预加载进度: 24/26 - sub24[0m
[1;96m📥 正在预加载受试者 sub24 的数据...[0m
[1;92m✅ 受试者 sub24 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 1.14 GB[0m
[1;96m📊 预加载进度: 25/26 - sub25[0m
[1;96m📥 正在预加载受试者 sub25 的数据...[0m
[1;92m✅ 受试者 sub25 数据预加载完成[0m
[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96m💾 当前显存使用: 1.19 GB[0m
[1;96m📊 预加载进度: 26/26 - sub26[0m
[1;96m📥 正在预加载受试者 sub26 的数据...[0m
[1;92m✅ 受试者 sub26 数据预加载完成[0m
[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m
[1;96m💾 当前显存使用: 1.24 GB[0m

[1;92m===========[0m
[1m[1;92m✅ 数据预加载完成 ✅[0m
[1;92m===========[0m

[1;92m✅ 成功预加载: 26 个受试者[0m
[1m[1;95m💾 最终显存使用: 1.24 GB[0m
根据配置,不使用Visdom可视化

[1;95m=====================[0m
[1m[1;95m🚀 使用GPU预加载 + 并行训练模式 🚀[0m
[1;95m=====================[0m

[1;96mℹ️ 可用受试者: 23 个[0m
[1;96mℹ️ 受试者列表: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub23', 'sub12', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub06', 'sub15', 'sub20', 'sub04', 'sub13', 'sub08', 'sub16', 'sub21', 'sub22'][0m

[1;95m==================[0m
[1m[1;95m⚡ 开始并行训练 23 个受试者 ⚡[0m
[1;95m==================[0m

[1;96mℹ️ 并行工作线程数: 4[0m
[1;96m🎯 开始训练受试者: sub17[0m
[1;96m🎯 开始训练受试者: sub05[0m[1;96mℹ️ 训练样本: 114, 测试样本: 31[0m
[1;96m🎯 开始训练受试者: sub26[0m
[1;96mℹ️ 训练样本: 138, 测试样本: 7[0m
[1;96m🎯 开始训练受试者: sub19[0m[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m[1;96mℹ️ 训练数据形状: torch.Size([114, 38, 48, 48]), 测试数据形状: torch.Size([31, 38, 48, 48])[0m



[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m[1;96mℹ️ 训练数据形状: torch.Size([138, 38, 48, 48]), 测试数据形状: torch.Size([7, 38, 48, 48])[0m[1;96mℹ️ 训练数据形状: torch.Size([134, 38, 48, 48]), 测试数据形状: torch.Size([11, 38, 48, 48])[0m


[1;96mℹ️ 训练数据形状: torch.Size([134, 38, 48, 48]), 测试数据形状: torch.Size([11, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
🔧 激活函数配置: 标准ReLU
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8=> 使用折叠除法: 8


=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8📝 使用传统特征拼接方式

📝 使用传统特征拼接方式=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8📝 使用传统特征拼接方式

📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;91m❌ 训练受试者 sub17 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub19 时出错: 'tuple' object has no attribute 'log_softmax'[0m[1;91m❌ 训练受试者 sub05 时出错: 'tuple' object has no attribute 'log_softmax'[0m

[1;91m❌ 训练受试者 sub26 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;96m🎯 开始训练受试者: sub09[0m[1;91m❌ ❌ sub19 训练失败[0m

[1;96mℹ️ 训练样本: 135, 测试样本: 10[0m
[1;96mℹ️ 训练数据形状: torch.Size([135, 38, 48, 48]), 测试数据形状: torch.Size([10, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub02[0m
[1;91m❌ ❌ sub26 训练失败[0m[1;96mℹ️ 训练样本: 136, 测试样本: 9[0m

[1;96mℹ️ 训练数据形状: torch.Size([136, 38, 48, 48]), 测试数据形状: torch.Size([9, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub23[0m
[1;91m❌ ❌ sub05 训练失败[0m[1;96mℹ️ 训练样本: 137, 测试样本: 8[0m

[1;96mℹ️ 训练数据形状: torch.Size([137, 38, 48, 48]), 测试数据形状: torch.Size([8, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub12[0m[1;91m❌ ❌ sub17 训练失败[0m

[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m
[1;96mℹ️ 训练数据形状: torch.Size([134, 38, 48, 48]), 测试数据形状: torch.Size([11, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
📝 使用传统特征拼接方式
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

📝 使用传统特征拼接方式=> 使用折叠除法: 8📝 使用传统特征拼接方式


📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m[1;96mℹ️ 加载预训练模型...[0m

[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;91m❌ 训练受试者 sub02 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub23 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub09 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub12 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;96m🎯 开始训练受试者: sub11[0m[1;91m❌ ❌ sub02 训练失败[0m

[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m
[1;96mℹ️ 训练数据形状: torch.Size([141, 38, 48, 48]), 测试数据形状: torch.Size([4, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub01[0m[1;91m❌ ❌ sub23 训练失败[0m

[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96mℹ️ 训练数据形状: torch.Size([142, 38, 48, 48]), 测试数据形状: torch.Size([3, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub07[0m[1;91m❌ ❌ sub09 训练失败[0m

[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96mℹ️ 训练数据形状: torch.Size([140, 38, 48, 48]), 测试数据形状: torch.Size([5, 38, 48, 48])[0m
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub24[0m
[1;91m❌ ❌ sub12 训练失败[0m[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m

[1;96mℹ️ 训练数据形状: torch.Size([142, 38, 48, 48]), 测试数据形状: torch.Size([3, 38, 48, 48])[0m
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
🔧 激活函数配置: 标准ReLU
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8🔧 激活函数配置: 标准ReLU

=> 使用折叠除法: 8=> 使用折叠除法: 8
=> 使用折叠除法: 8

=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8
=> 使用折叠除法: 8

📝 使用传统特征拼接方式📝 使用传统特征拼接方式

=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;91m❌ 训练受试者 sub11 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub07 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub24 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub01 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;96m🎯 开始训练受试者: sub25[0m[1;91m❌ ❌ sub07 训练失败[0m

[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96mℹ️ 训练数据形状: torch.Size([140, 38, 48, 48]), 测试数据形状: torch.Size([5, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub03[0m[1;91m❌ ❌ sub11 训练失败[0m

[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96mℹ️ 训练数据形状: torch.Size([140, 38, 48, 48]), 测试数据形状: torch.Size([5, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub06[0m
[1;91m❌ ❌ sub24 训练失败[0m[1;96m🎯 开始训练受试者: sub15[0m🔧 激活函数配置: 标准ReLU
[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m

🔧 激活函数配置: 标准ReLU[1;91m❌ ❌ sub01 训练失败[0m
[1;96mℹ️ 训练数据形状: torch.Size([141, 38, 48, 48]), 测试数据形状: torch.Size([4, 38, 48, 48])[0m[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m



[1;96mℹ️ 训练数据形状: torch.Size([142, 38, 48, 48]), 测试数据形状: torch.Size([3, 38, 48, 48])[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8=> 使用折叠除法: 8

🔧 激活函数配置: 标准ReLU=> 使用折叠除法: 8

🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

📝 使用传统特征拼接方式=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

📝 使用传统特征拼接方式
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

📝 使用传统特征拼接方式
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;91m❌ 训练受试者 sub25 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub03 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub15 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub06 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;96m🎯 开始训练受试者: sub20[0m[1;91m❌ ❌ sub25 训练失败[0m

[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub04[0m=> 使用折叠除法: 8

[1;91m❌ ❌ sub03 训练失败[0m[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m

[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub13[0m=> 使用折叠除法: 8
[1;91m❌ ❌ sub06 训练失败[0m[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m


[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub08[0m[1;91m❌ ❌ sub15 训练失败[0m

[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m
[1;96mℹ️ 训练数据形状: torch.Size([144, 38, 48, 48]), 测试数据形状: torch.Size([1, 38, 48, 48])[0m
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式🔧 激活函数配置: 标准ReLU

🔧 激活函数配置: 标准ReLU=> 使用折叠除法: 8

=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8=> 使用折叠除法: 8


=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

📝 使用传统特征拼接方式=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8📝 使用传统特征拼接方式

📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m

[1;96mℹ️ 加载了 356/362 个层[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;91m❌ 训练受试者 sub20 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;96m🎯 开始训练受试者: sub16[0m[1;91m❌ ❌ sub20 训练失败[0m

[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;91m❌ 训练受试者 sub04 时出错: 'tuple' object has no attribute 'log_softmax'[0m
=> 使用折叠除法: 8
[1;91m❌ 训练受试者 sub13 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub08 时出错: 'tuple' object has no attribute 'log_softmax'[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96m🎯 开始训练受试者: sub21[0m[1;91m❌ ❌ sub08 训练失败[0m

[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m
[1;96mℹ️ 训练数据形状: torch.Size([144, 38, 48, 48]), 测试数据形状: torch.Size([1, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub22[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m[1;91m❌ ❌ sub04 训练失败[0m

[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
[1;91m❌ ❌ sub13 训练失败[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8🔧 激活函数配置: 标准ReLU

=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8📝 使用传统特征拼接方式

📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;91m❌ 训练受试者 sub16 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub21 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub22 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ ❌ sub16 训练失败[0m
[1;91m❌ ❌ sub21 训练失败[0m
[1;91m❌ ❌ sub22 训练失败[0m

[1;92m==========[0m
[1m[1;92m🎉 并行训练完成 🎉[0m
[1;92m==========[0m

[1;92m✅ 成功训练: 0 个受试者[0m

[1;92m===================[0m
[1m[1;92m📊 并行训练完成，正在计算总体指标 📊[0m
[1;92m===================[0m


================================================================================
【实验结果报告】
================================================================================

【基本信息】
实验名称: SKD_TSTSAN_CASME2_class3_full_conv1_eca1_relu_fix_class3_conv1_eca1_relu
时间: 2025-07-28 23:01:45
总训练时间: 00:00:37
数据集: CASME2_LOSO_full

【系统环境】
操作系统: 💻 Linux 6.8.0-1030-nvidia (x86_64)
处理器: ⚡ 32核心/32线程 @ 2195MHz
内存: 🧠 62.7GB RAM (已用: 25.6GB, 40.8%)
GPU: 🚀 NVIDIA A800 80GB PCIe (79.3GB)

【模型配置】
模型架构: SKD_TSTSAN
是否使用预训练: 是
预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
批次大小: 32
最大迭代次数: 20
损失函数: FocalLoss_weighted
随机种子: 2577

【GPU性能配置】
CUDNN Benchmark: 禁用
CUDNN Deterministic: 禁用
混合精度训练: 禁用
TF32加速: 启用
BatchNorm修复: 禁用
卷积类型: 标准卷积

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: N/A
- UAR分数: N/A

【各表情类别准确率】
--------------------------------------------------

【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
    平均        nan        nan        nan    


【各受试者详细预测结果】
==================================================

================================================================================

【错误】邮件处理过程出错: '<' not supported between instances of 'NoneType' and 'float'
详细错误信息: Traceback (most recent call last):
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSANCASME2.py", line 761, in send_training_results
    performance_recommendations = generate_performance_recommendations(results_dict, config, total_time)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSANCASME2.py", line 527, in generate_performance_recommendations
    if overall_uf1 < 0.7 or overall_uar < 0.7:
       ^^^^^^^^^^^^^^^^^
TypeError: '<' not supported between instances of 'NoneType' and 'float'


训练完成!

正在关闭日志系统...
日志系统初始化成功

================================================================================
【训练开始 - GPU预加载 + 并行训练模式】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (3分类)[0m
[1;96m🎯 类别权重: ['0.3794', '0.1349', '0.4857'][0m
开始训练...

[1m[1;94m⚙️ 训练模式配置[0m
[1;94m----------[0m
[1;96mℹ️ GPU数据预加载: ✅ 启用[0m
[1;96mℹ️ 并行训练: ✅ 启用[0m
[1;96mℹ️ 并行工作线程数: 4[0m

[1m[1;96m🚀 GPU数据预加载器初始化[0m
[1;96m----------------[0m
[1;96mℹ️ 数据集路径: ./CASME2_LOSO_full[0m
[1;96mℹ️ 分类数量: 3[0m
[1;96mℹ️ 目标设备: cuda[0m

[1;92m========================[0m
[1m[1;92m🚀 开始预加载CASME2数据集到GPU显存 🚀[0m
[1;92m========================[0m

[1;96m📊 正在估算数据集显存需求...[0m
[1;92m📈 估算结果: 6344 个样本[0m
[1;92m💾 预计显存需求: 2.07 GB[0m
[1;96mℹ️ GPU总显存: 79.25 GB[0m
[1;96mℹ️ 可用显存: 63.40 GB[0m
[1;96mℹ️ 发现 26 个受试者: ['sub01', 'sub02', 'sub03', 'sub04', 'sub05', 'sub06', 'sub07', 'sub08', 'sub09', 'sub10', 'sub11', 'sub12', 'sub13', 'sub14', 'sub15', 'sub16', 'sub17', 'sub18', 'sub19', 'sub20', 'sub21', 'sub22', 'sub23', 'sub24', 'sub25', 'sub26'][0m
[1;96m📊 预加载进度: 1/26 - sub01[0m
[1;96m📥 正在预加载受试者 sub01 的数据...[0m
[1;92m✅ 受试者 sub01 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 0.05 GB[0m
[1;96m📊 预加载进度: 2/26 - sub02[0m
[1;96m📥 正在预加载受试者 sub02 的数据...[0m
[1;92m✅ 受试者 sub02 数据预加载完成[0m
[1;96mℹ️ 训练样本: 136, 测试样本: 9[0m
[1;96m💾 当前显存使用: 0.10 GB[0m
[1;96m📊 预加载进度: 3/26 - sub03[0m
[1;96m📥 正在预加载受试者 sub03 的数据...[0m
[1;92m✅ 受试者 sub03 数据预加载完成[0m
[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96m💾 当前显存使用: 0.14 GB[0m
[1;96m📊 预加载进度: 4/26 - sub04[0m
[1;96m📥 正在预加载受试者 sub04 的数据...[0m
[1;92m✅ 受试者 sub04 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.19 GB[0m
[1;96m📊 预加载进度: 5/26 - sub05[0m
[1;96m📥 正在预加载受试者 sub05 的数据...[0m
[1;92m✅ 受试者 sub05 数据预加载完成[0m
[1;96mℹ️ 训练样本: 138, 测试样本: 7[0m
[1;96m💾 当前显存使用: 0.24 GB[0m
[1;96m📊 预加载进度: 6/26 - sub06[0m
[1;96m📥 正在预加载受试者 sub06 的数据...[0m
[1;92m✅ 受试者 sub06 数据预加载完成[0m
[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m
[1;96m💾 当前显存使用: 0.29 GB[0m
[1;96m📊 预加载进度: 7/26 - sub07[0m
[1;96m📥 正在预加载受试者 sub07 的数据...[0m
[1;92m✅ 受试者 sub07 数据预加载完成[0m
[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96m💾 当前显存使用: 0.33 GB[0m
[1;96m📊 预加载进度: 8/26 - sub08[0m
[1;96m📥 正在预加载受试者 sub08 的数据...[0m
[1;92m✅ 受试者 sub08 数据预加载完成[0m
[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m
[1;96m💾 当前显存使用: 0.38 GB[0m
[1;96m📊 预加载进度: 9/26 - sub09[0m
[1;96m📥 正在预加载受试者 sub09 的数据...[0m
[1;92m✅ 受试者 sub09 数据预加载完成[0m
[1;96mℹ️ 训练样本: 135, 测试样本: 10[0m
[1;96m💾 当前显存使用: 0.43 GB[0m
[1;96m📊 预加载进度: 10/26 - sub10[0m
[1;96m📥 正在预加载受试者 sub10 的数据...[0m
[1;92m✅ 受试者 sub10 数据预加载完成[0m
[1;96mℹ️ 训练样本: 145, 测试样本: 0[0m
[1;96m💾 当前显存使用: 0.48 GB[0m
[1;96m📊 预加载进度: 11/26 - sub11[0m
[1;96m📥 正在预加载受试者 sub11 的数据...[0m
[1;92m✅ 受试者 sub11 数据预加载完成[0m
[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m
[1;96m💾 当前显存使用: 0.53 GB[0m
[1;96m📊 预加载进度: 12/26 - sub12[0m
[1;96m📥 正在预加载受试者 sub12 的数据...[0m
[1;92m✅ 受试者 sub12 数据预加载完成[0m
[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m
[1;96m💾 当前显存使用: 0.57 GB[0m
[1;96m📊 预加载进度: 13/26 - sub13[0m
[1;96m📥 正在预加载受试者 sub13 的数据...[0m
[1;92m✅ 受试者 sub13 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.62 GB[0m
[1;96m📊 预加载进度: 14/26 - sub14[0m
[1;96m📥 正在预加载受试者 sub14 的数据...[0m
[1;92m✅ 受试者 sub14 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 0.67 GB[0m
[1;96m📊 预加载进度: 15/26 - sub15[0m
[1;96m📥 正在预加载受试者 sub15 的数据...[0m
[1;92m✅ 受试者 sub15 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 0.72 GB[0m
[1;96m📊 预加载进度: 16/26 - sub16[0m
[1;96m📥 正在预加载受试者 sub16 的数据...[0m
[1;92m✅ 受试者 sub16 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.76 GB[0m
[1;96m📊 预加载进度: 17/26 - sub17[0m
[1;96m📥 正在预加载受试者 sub17 的数据...[0m
[1;92m✅ 受试者 sub17 数据预加载完成[0m
[1;96mℹ️ 训练样本: 114, 测试样本: 31[0m
[1;96m💾 当前显存使用: 0.81 GB[0m
[1;96m📊 预加载进度: 18/26 - sub18[0m
[1;96m📥 正在预加载受试者 sub18 的数据...[0m
[1;92m✅ 受试者 sub18 数据预加载完成[0m
[1;96mℹ️ 训练样本: 145, 测试样本: 0[0m
[1;96m💾 当前显存使用: 0.86 GB[0m
[1;96m📊 预加载进度: 19/26 - sub19[0m
[1;96m📥 正在预加载受试者 sub19 的数据...[0m
[1;92m✅ 受试者 sub19 数据预加载完成[0m
[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m
[1;96m💾 当前显存使用: 0.91 GB[0m
[1;96m📊 预加载进度: 20/26 - sub20[0m
[1;96m📥 正在预加载受试者 sub20 的数据...[0m
[1;92m✅ 受试者 sub20 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.95 GB[0m
[1;96m📊 预加载进度: 21/26 - sub21[0m
[1;96m📥 正在预加载受试者 sub21 的数据...[0m
[1;92m✅ 受试者 sub21 数据预加载完成[0m
[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m
[1;96m💾 当前显存使用: 1.00 GB[0m
[1;96m📊 预加载进度: 22/26 - sub22[0m
[1;96m📥 正在预加载受试者 sub22 的数据...[0m
[1;92m✅ 受试者 sub22 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 1.05 GB[0m
[1;96m📊 预加载进度: 23/26 - sub23[0m
[1;96m📥 正在预加载受试者 sub23 的数据...[0m
[1;92m✅ 受试者 sub23 数据预加载完成[0m
[1;96mℹ️ 训练样本: 137, 测试样本: 8[0m
[1;96m💾 当前显存使用: 1.10 GB[0m
[1;96m📊 预加载进度: 24/26 - sub24[0m
[1;96m📥 正在预加载受试者 sub24 的数据...[0m
[1;92m✅ 受试者 sub24 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 1.14 GB[0m
[1;96m📊 预加载进度: 25/26 - sub25[0m
[1;96m📥 正在预加载受试者 sub25 的数据...[0m
[1;92m✅ 受试者 sub25 数据预加载完成[0m
[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96m💾 当前显存使用: 1.19 GB[0m
[1;96m📊 预加载进度: 26/26 - sub26[0m
[1;96m📥 正在预加载受试者 sub26 的数据...[0m
[1;92m✅ 受试者 sub26 数据预加载完成[0m
[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m
[1;96m💾 当前显存使用: 1.24 GB[0m

[1;92m===========[0m
[1m[1;92m✅ 数据预加载完成 ✅[0m
[1;92m===========[0m

[1;92m✅ 成功预加载: 26 个受试者[0m
[1m[1;95m💾 最终显存使用: 1.24 GB[0m
根据配置,不使用Visdom可视化

[1;95m=====================[0m
[1m[1;95m🚀 使用GPU预加载 + 并行训练模式 🚀[0m
[1;95m=====================[0m

[1;96mℹ️ 可用受试者: 23 个[0m
[1;96mℹ️ 受试者列表: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub23', 'sub12', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub06', 'sub15', 'sub20', 'sub04', 'sub13', 'sub08', 'sub16', 'sub21', 'sub22'][0m

[1;95m==================[0m
[1m[1;95m⚡ 开始并行训练 23 个受试者 ⚡[0m
[1;95m==================[0m

[1;96mℹ️ 并行工作线程数: 4[0m
[1;96m🎯 开始训练受试者: sub17[0m
[1;96mℹ️ 训练样本: 114, 测试样本: 31[0m
[1;96m🎯 开始训练受试者: sub05[0m[1;96m🎯 开始训练受试者: sub26[0m[1;96m🎯 开始训练受试者: sub19[0m


[1;96mℹ️ 训练样本: 138, 测试样本: 7[0m[1;96mℹ️ 训练数据形状: torch.Size([114, 38, 48, 48]), 测试数据形状: torch.Size([31, 38, 48, 48])[0m[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m
[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m


[1;96mℹ️ 训练数据形状: torch.Size([138, 38, 48, 48]), 测试数据形状: torch.Size([7, 38, 48, 48])[0m[1;96mℹ️ 训练数据形状: torch.Size([134, 38, 48, 48]), 测试数据形状: torch.Size([11, 38, 48, 48])[0m
[1;96mℹ️ 训练数据形状: torch.Size([134, 38, 48, 48]), 测试数据形状: torch.Size([11, 38, 48, 48])[0m

🔧 激活函数配置: 标准ReLU
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8=> 使用折叠除法: 8
=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8=> 使用折叠除法: 8
📝 使用传统特征拼接方式


📝 使用传统特征拼接方式
=> 使用折叠除法: 8📝 使用传统特征拼接方式

=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m[1;96mℹ️ 加载预训练模型...[0m

[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;91m❌ 训练受试者 sub26 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub05 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub17 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub19 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;96m🎯 开始训练受试者: sub09[0m[1;91m❌ ❌ sub05 训练失败[0m

[1;96mℹ️ 训练样本: 135, 测试样本: 10[0m
[1;96mℹ️ 训练数据形状: torch.Size([135, 38, 48, 48]), 测试数据形状: torch.Size([10, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub02[0m[1;91m❌ ❌ sub26 训练失败[0m

[1;96mℹ️ 训练样本: 136, 测试样本: 9[0m
[1;96mℹ️ 训练数据形状: torch.Size([136, 38, 48, 48]), 测试数据形状: torch.Size([9, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub23[0m[1;96m🎯 开始训练受试者: sub12[0m[1;91m❌ ❌ sub19 训练失败[0m

[1;96mℹ️ 训练样本: 137, 测试样本: 8[0m[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m


[1;96mℹ️ 训练数据形状: torch.Size([137, 38, 48, 48]), 测试数据形状: torch.Size([8, 38, 48, 48])[0m[1;91m❌ ❌ sub17 训练失败[0m

[1;96mℹ️ 训练数据形状: torch.Size([134, 38, 48, 48]), 测试数据形状: torch.Size([11, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8🔧 激活函数配置: 标准ReLU

🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8=> 使用折叠除法: 8


=> 使用折叠除法: 8
📝 使用传统特征拼接方式
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
📝 使用传统特征拼接方式
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8📝 使用传统特征拼接方式

📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m[1;96mℹ️ 加载预训练模型...[0m

[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;91m❌ 训练受试者 sub09 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub02 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub12 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub23 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;96m🎯 开始训练受试者: sub11[0m[1;91m❌ ❌ sub09 训练失败[0m

[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m
[1;96mℹ️ 训练数据形状: torch.Size([141, 38, 48, 48]), 测试数据形状: torch.Size([4, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub01[0m
[1;91m❌ ❌ sub12 训练失败[0m[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m

[1;96mℹ️ 训练数据形状: torch.Size([142, 38, 48, 48]), 测试数据形状: torch.Size([3, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub07[0m[1;91m❌ ❌ sub02 训练失败[0m

[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96mℹ️ 训练数据形状: torch.Size([140, 38, 48, 48]), 测试数据形状: torch.Size([5, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub24[0m[1;91m❌ ❌ sub23 训练失败[0m

[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96mℹ️ 训练数据形状: torch.Size([142, 38, 48, 48]), 测试数据形状: torch.Size([3, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU🔧 激活函数配置: 标准ReLU

=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8🔧 激活函数配置: 标准ReLU

=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8=> 使用折叠除法: 8


=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8=> 使用折叠除法: 8
=> 使用折叠除法: 8


=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式=> 使用折叠除法: 8=> 使用折叠除法: 8


=> 使用折叠除法: 8📝 使用传统特征拼接方式

📝 使用传统特征拼接方式
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m[1;96mℹ️ 加载了 356/362 个层[0m

[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;91m❌ 训练受试者 sub01 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub24 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub11 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub07 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;96m🎯 开始训练受试者: sub25[0m[1;91m❌ ❌ sub01 训练失败[0m

[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96mℹ️ 训练数据形状: torch.Size([140, 38, 48, 48]), 测试数据形状: torch.Size([5, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub03[0m[1;91m❌ ❌ sub24 训练失败[0m

[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96mℹ️ 训练数据形状: torch.Size([140, 38, 48, 48]), 测试数据形状: torch.Size([5, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub06[0m[1;91m❌ ❌ sub11 训练失败[0m

[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m
=> 使用折叠除法: 8[1;96mℹ️ 训练数据形状: torch.Size([141, 38, 48, 48]), 测试数据形状: torch.Size([4, 38, 48, 48])[0m

[1;96m🎯 开始训练受试者: sub15[0m[1;91m❌ ❌ sub07 训练失败[0m

[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96mℹ️ 训练数据形状: torch.Size([142, 38, 48, 48]), 测试数据形状: torch.Size([3, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8=> 使用折叠除法: 8


📝 使用传统特征拼接方式
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

📝 使用传统特征拼接方式
=> 使用折叠除法: 8📝 使用传统特征拼接方式

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;91m❌ 训练受试者 sub03 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub25 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;96m🎯 开始训练受试者: sub20[0m[1;91m❌ ❌ sub03 训练失败[0m

[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
[1;91m❌ 训练受试者 sub06 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub15 时出错: 'tuple' object has no attribute 'log_softmax'[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub04[0m[1;91m❌ ❌ sub25 训练失败[0m

[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub13[0m[1;91m❌ ❌ sub06 训练失败[0m

[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub08[0m[1;91m❌ ❌ sub15 训练失败[0m
[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m

[1;96mℹ️ 训练数据形状: torch.Size([144, 38, 48, 48]), 测试数据形状: torch.Size([1, 38, 48, 48])[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8🔧 激活函数配置: 标准ReLU

🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

📝 使用传统特征拼接方式
=> 使用折叠除法: 8
=> 使用折叠除法: 8📝 使用传统特征拼接方式

📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m

[1;96mℹ️ 加载了 356/362 个层[0m[1;96mℹ️ 加载了 356/362 个层[0m

[1;91m❌ 训练受试者 sub20 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub08 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub04 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub13 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;96m🎯 开始训练受试者: sub16[0m
[1;91m❌ ❌ sub20 训练失败[0m[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m

[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub21[0m[1;91m❌ ❌ sub08 训练失败[0m=> 使用折叠除法: 8

[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m

[1;96mℹ️ 训练数据形状: torch.Size([144, 38, 48, 48]), 测试数据形状: torch.Size([1, 38, 48, 48])[0m
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub22[0m[1;91m❌ ❌ sub04 训练失败[0m

[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m=> 使用折叠除法: 8

[1;91m❌ ❌ sub13 训练失败[0m[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU📝 使用传统特征拼接方式

=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8=> 使用折叠除法: 8

📝 使用传统特征拼接方式
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;91m❌ 训练受试者 sub16 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ ❌ sub16 训练失败[0m
[1;91m❌ 训练受试者 sub22 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub21 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ ❌ sub22 训练失败[0m
[1;91m❌ ❌ sub21 训练失败[0m

[1;92m==========[0m
[1m[1;92m🎉 并行训练完成 🎉[0m
[1;92m==========[0m

[1;92m✅ 成功训练: 0 个受试者[0m

[1;92m===================[0m
[1m[1;92m📊 并行训练完成，正在计算总体指标 📊[0m
[1;92m===================[0m


================================================================================
【实验结果报告】
================================================================================

【基本信息】
实验名称: SKD_TSTSAN_CASME2_class3_full_conv1_eca1_relu_fix_class3_conv1_eca1_relu
时间: 2025-07-28 23:02:36
总训练时间: 00:00:32
数据集: CASME2_LOSO_full

【系统环境】
操作系统: 💻 Linux 6.8.0-1030-nvidia (x86_64)
处理器: ⚡ 32核心/32线程 @ 2195MHz
内存: 🧠 62.7GB RAM (已用: 25.1GB, 40.0%)
GPU: 🚀 NVIDIA A800 80GB PCIe (79.3GB)

【模型配置】
模型架构: SKD_TSTSAN
是否使用预训练: 是
预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
批次大小: 32
最大迭代次数: 20
损失函数: FocalLoss_weighted
随机种子: 2577

【GPU性能配置】
CUDNN Benchmark: 禁用
CUDNN Deterministic: 禁用
混合精度训练: 禁用
TF32加速: 启用
BatchNorm修复: 禁用
卷积类型: 标准卷积

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: N/A
- UAR分数: N/A

【各表情类别准确率】
--------------------------------------------------

【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
    平均        nan        nan        nan    


【各受试者详细预测结果】
==================================================

================================================================================

【错误】邮件处理过程出错: '<' not supported between instances of 'NoneType' and 'float'
详细错误信息: Traceback (most recent call last):
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSANCASME2.py", line 761, in send_training_results
    performance_recommendations = generate_performance_recommendations(results_dict, config, total_time)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSANCASME2.py", line 527, in generate_performance_recommendations
    if overall_uf1 < 0.7 or overall_uar < 0.7:
       ^^^^^^^^^^^^^^^^^
TypeError: '<' not supported between instances of 'NoneType' and 'float'


训练完成!

正在关闭日志系统...
日志系统初始化成功

================================================================================
【训练开始 - GPU预加载 + 并行训练模式】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (3分类)[0m
[1;96m🎯 类别权重: ['0.3794', '0.1349', '0.4857'][0m
开始训练...

[1m[1;94m⚙️ 训练模式配置[0m
[1;94m----------[0m
[1;96mℹ️ GPU数据预加载: ✅ 启用[0m
[1;96mℹ️ 并行训练: ✅ 启用[0m
[1;96mℹ️ 并行工作线程数: 4[0m

[1m[1;96m🚀 GPU数据预加载器初始化[0m
[1;96m----------------[0m
[1;96mℹ️ 数据集路径: ./CASME2_LOSO_full[0m
[1;96mℹ️ 分类数量: 3[0m
[1;96mℹ️ 目标设备: cuda[0m

[1;92m========================[0m
[1m[1;92m🚀 开始预加载CASME2数据集到GPU显存 🚀[0m
[1;92m========================[0m

[1;96m📊 正在估算数据集显存需求...[0m
[1;92m📈 估算结果: 6344 个样本[0m
[1;92m💾 预计显存需求: 2.07 GB[0m
[1;96mℹ️ GPU总显存: 79.25 GB[0m
[1;96mℹ️ 可用显存: 63.40 GB[0m
[1;96mℹ️ 发现 26 个受试者: ['sub01', 'sub02', 'sub03', 'sub04', 'sub05', 'sub06', 'sub07', 'sub08', 'sub09', 'sub10', 'sub11', 'sub12', 'sub13', 'sub14', 'sub15', 'sub16', 'sub17', 'sub18', 'sub19', 'sub20', 'sub21', 'sub22', 'sub23', 'sub24', 'sub25', 'sub26'][0m
[1;96m📊 预加载进度: 1/26 - sub01[0m
[1;96m📥 正在预加载受试者 sub01 的数据...[0m
[1;92m✅ 受试者 sub01 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 0.05 GB[0m
[1;96m📊 预加载进度: 2/26 - sub02[0m
[1;96m📥 正在预加载受试者 sub02 的数据...[0m
[1;92m✅ 受试者 sub02 数据预加载完成[0m
[1;96mℹ️ 训练样本: 136, 测试样本: 9[0m
[1;96m💾 当前显存使用: 0.10 GB[0m
[1;96m📊 预加载进度: 3/26 - sub03[0m
[1;96m📥 正在预加载受试者 sub03 的数据...[0m
[1;92m✅ 受试者 sub03 数据预加载完成[0m
[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96m💾 当前显存使用: 0.14 GB[0m
[1;96m📊 预加载进度: 4/26 - sub04[0m
[1;96m📥 正在预加载受试者 sub04 的数据...[0m
[1;92m✅ 受试者 sub04 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.19 GB[0m
[1;96m📊 预加载进度: 5/26 - sub05[0m
[1;96m📥 正在预加载受试者 sub05 的数据...[0m
[1;92m✅ 受试者 sub05 数据预加载完成[0m
[1;96mℹ️ 训练样本: 138, 测试样本: 7[0m
[1;96m💾 当前显存使用: 0.24 GB[0m
[1;96m📊 预加载进度: 6/26 - sub06[0m
[1;96m📥 正在预加载受试者 sub06 的数据...[0m
[1;92m✅ 受试者 sub06 数据预加载完成[0m
[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m
[1;96m💾 当前显存使用: 0.29 GB[0m
[1;96m📊 预加载进度: 7/26 - sub07[0m
[1;96m📥 正在预加载受试者 sub07 的数据...[0m
[1;92m✅ 受试者 sub07 数据预加载完成[0m
[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96m💾 当前显存使用: 0.33 GB[0m
[1;96m📊 预加载进度: 8/26 - sub08[0m
[1;96m📥 正在预加载受试者 sub08 的数据...[0m
[1;92m✅ 受试者 sub08 数据预加载完成[0m
[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m
[1;96m💾 当前显存使用: 0.38 GB[0m
[1;96m📊 预加载进度: 9/26 - sub09[0m
[1;96m📥 正在预加载受试者 sub09 的数据...[0m
[1;92m✅ 受试者 sub09 数据预加载完成[0m
[1;96mℹ️ 训练样本: 135, 测试样本: 10[0m
[1;96m💾 当前显存使用: 0.43 GB[0m
[1;96m📊 预加载进度: 10/26 - sub10[0m
[1;96m📥 正在预加载受试者 sub10 的数据...[0m
[1;92m✅ 受试者 sub10 数据预加载完成[0m
[1;96mℹ️ 训练样本: 145, 测试样本: 0[0m
[1;96m💾 当前显存使用: 0.48 GB[0m
[1;96m📊 预加载进度: 11/26 - sub11[0m
[1;96m📥 正在预加载受试者 sub11 的数据...[0m
[1;92m✅ 受试者 sub11 数据预加载完成[0m
[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m
[1;96m💾 当前显存使用: 0.53 GB[0m
[1;96m📊 预加载进度: 12/26 - sub12[0m
[1;96m📥 正在预加载受试者 sub12 的数据...[0m
[1;92m✅ 受试者 sub12 数据预加载完成[0m
[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m
[1;96m💾 当前显存使用: 0.57 GB[0m
[1;96m📊 预加载进度: 13/26 - sub13[0m
[1;96m📥 正在预加载受试者 sub13 的数据...[0m
[1;92m✅ 受试者 sub13 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.62 GB[0m
[1;96m📊 预加载进度: 14/26 - sub14[0m
[1;96m📥 正在预加载受试者 sub14 的数据...[0m
[1;92m✅ 受试者 sub14 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 0.67 GB[0m
[1;96m📊 预加载进度: 15/26 - sub15[0m
[1;96m📥 正在预加载受试者 sub15 的数据...[0m
[1;92m✅ 受试者 sub15 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 0.72 GB[0m
[1;96m📊 预加载进度: 16/26 - sub16[0m
[1;96m📥 正在预加载受试者 sub16 的数据...[0m
[1;92m✅ 受试者 sub16 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.76 GB[0m
[1;96m📊 预加载进度: 17/26 - sub17[0m
[1;96m📥 正在预加载受试者 sub17 的数据...[0m
[1;92m✅ 受试者 sub17 数据预加载完成[0m
[1;96mℹ️ 训练样本: 114, 测试样本: 31[0m
[1;96m💾 当前显存使用: 0.81 GB[0m
[1;96m📊 预加载进度: 18/26 - sub18[0m
[1;96m📥 正在预加载受试者 sub18 的数据...[0m
[1;92m✅ 受试者 sub18 数据预加载完成[0m
[1;96mℹ️ 训练样本: 145, 测试样本: 0[0m
[1;96m💾 当前显存使用: 0.86 GB[0m
[1;96m📊 预加载进度: 19/26 - sub19[0m
[1;96m📥 正在预加载受试者 sub19 的数据...[0m
[1;92m✅ 受试者 sub19 数据预加载完成[0m
[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m
[1;96m💾 当前显存使用: 0.91 GB[0m
[1;96m📊 预加载进度: 20/26 - sub20[0m
[1;96m📥 正在预加载受试者 sub20 的数据...[0m
[1;92m✅ 受试者 sub20 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.95 GB[0m
[1;96m📊 预加载进度: 21/26 - sub21[0m
[1;96m📥 正在预加载受试者 sub21 的数据...[0m
[1;92m✅ 受试者 sub21 数据预加载完成[0m
[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m
[1;96m💾 当前显存使用: 1.00 GB[0m
[1;96m📊 预加载进度: 22/26 - sub22[0m
[1;96m📥 正在预加载受试者 sub22 的数据...[0m
[1;92m✅ 受试者 sub22 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 1.05 GB[0m
[1;96m📊 预加载进度: 23/26 - sub23[0m
[1;96m📥 正在预加载受试者 sub23 的数据...[0m
[1;92m✅ 受试者 sub23 数据预加载完成[0m
[1;96mℹ️ 训练样本: 137, 测试样本: 8[0m
[1;96m💾 当前显存使用: 1.10 GB[0m
[1;96m📊 预加载进度: 24/26 - sub24[0m
[1;96m📥 正在预加载受试者 sub24 的数据...[0m
[1;92m✅ 受试者 sub24 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 1.14 GB[0m
[1;96m📊 预加载进度: 25/26 - sub25[0m
[1;96m📥 正在预加载受试者 sub25 的数据...[0m
[1;92m✅ 受试者 sub25 数据预加载完成[0m
[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96m💾 当前显存使用: 1.19 GB[0m
[1;96m📊 预加载进度: 26/26 - sub26[0m
[1;96m📥 正在预加载受试者 sub26 的数据...[0m
[1;92m✅ 受试者 sub26 数据预加载完成[0m
[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m
[1;96m💾 当前显存使用: 1.24 GB[0m

[1;92m===========[0m
[1m[1;92m✅ 数据预加载完成 ✅[0m
[1;92m===========[0m

[1;92m✅ 成功预加载: 26 个受试者[0m
[1m[1;95m💾 最终显存使用: 1.24 GB[0m
根据配置,不使用Visdom可视化

[1;95m=====================[0m
[1m[1;95m🚀 使用GPU预加载 + 并行训练模式 🚀[0m
[1;95m=====================[0m

[1;96mℹ️ 可用受试者: 23 个[0m
[1;96mℹ️ 受试者列表: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub23', 'sub12', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub06', 'sub15', 'sub20', 'sub04', 'sub13', 'sub08', 'sub16', 'sub21', 'sub22'][0m

[1;95m==================[0m
[1m[1;95m⚡ 开始并行训练 23 个受试者 ⚡[0m
[1;95m==================[0m

[1;96mℹ️ 并行工作线程数: 4[0m
[1;96m🎯 开始训练受试者: sub17[0m
[1;96mℹ️ 训练样本: 114, 测试样本: 31[0m[1;96m🎯 开始训练受试者: sub05[0m

[1;96m🎯 开始训练受试者: sub26[0m[1;96mℹ️ 训练样本: 138, 测试样本: 7[0m[1;96m🎯 开始训练受试者: sub19[0m

[1;96mℹ️ 训练数据形状: torch.Size([114, 38, 48, 48]), 测试数据形状: torch.Size([31, 38, 48, 48])[0m
[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m[1;96mℹ️ 训练数据形状: torch.Size([138, 38, 48, 48]), 测试数据形状: torch.Size([7, 38, 48, 48])[0m



[1;96mℹ️ 训练数据形状: torch.Size([134, 38, 48, 48]), 测试数据形状: torch.Size([11, 38, 48, 48])[0m[1;96mℹ️ 训练数据形状: torch.Size([134, 38, 48, 48]), 测试数据形状: torch.Size([11, 38, 48, 48])[0m

🔧 激活函数配置: 标准ReLU
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8=> 使用折叠除法: 8


=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8=> 使用折叠除法: 8


=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8📝 使用传统特征拼接方式=> 使用折叠除法: 8



📝 使用传统特征拼接方式
=> 使用折叠除法: 8📝 使用传统特征拼接方式

=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m

[1;96mℹ️ 加载了 356/362 个层[0m
[1;91m❌ 训练受试者 sub05 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub17 时出错: 'tuple' object has no attribute 'log_softmax'[0m[1;91m❌ 训练受试者 sub19 时出错: 'tuple' object has no attribute 'log_softmax'[0m

[1;91m❌ 训练受试者 sub26 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;96m🎯 开始训练受试者: sub09[0m[1;91m❌ ❌ sub19 训练失败[0m

[1;96mℹ️ 训练样本: 135, 测试样本: 10[0m
[1;96mℹ️ 训练数据形状: torch.Size([135, 38, 48, 48]), 测试数据形状: torch.Size([10, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub02[0m[1;91m❌ ❌ sub26 训练失败[0m

[1;96mℹ️ 训练样本: 136, 测试样本: 9[0m
[1;96mℹ️ 训练数据形状: torch.Size([136, 38, 48, 48]), 测试数据形状: torch.Size([9, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub23[0m[1;91m❌ ❌ sub05 训练失败[0m

[1;96mℹ️ 训练样本: 137, 测试样本: 8[0m
[1;96mℹ️ 训练数据形状: torch.Size([137, 38, 48, 48]), 测试数据形状: torch.Size([8, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub12[0m
[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m[1;91m❌ ❌ sub17 训练失败[0m

[1;96mℹ️ 训练数据形状: torch.Size([134, 38, 48, 48]), 测试数据形状: torch.Size([11, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
📝 使用传统特征拼接方式
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

📝 使用传统特征拼接方式
=> 使用折叠除法: 8
📝 使用传统特征拼接方式=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;91m❌ 训练受试者 sub23 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub09 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub02 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub12 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;96m🎯 开始训练受试者: sub11[0m[1;91m❌ ❌ sub23 训练失败[0m

[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m
[1;96mℹ️ 训练数据形状: torch.Size([141, 38, 48, 48]), 测试数据形状: torch.Size([4, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub01[0m[1;91m❌ ❌ sub09 训练失败[0m

[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96mℹ️ 训练数据形状: torch.Size([142, 38, 48, 48]), 测试数据形状: torch.Size([3, 38, 48, 48])[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub07[0m[1;91m❌ ❌ sub12 训练失败[0m

[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96mℹ️ 训练数据形状: torch.Size([140, 38, 48, 48]), 测试数据形状: torch.Size([5, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub24[0m[1;91m❌ ❌ sub02 训练失败[0m

[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96mℹ️ 训练数据形状: torch.Size([142, 38, 48, 48]), 测试数据形状: torch.Size([3, 38, 48, 48])[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8🔧 激活函数配置: 标准ReLU

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

📝 使用传统特征拼接方式
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式=> 使用折叠除法: 8

📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;91m❌ 训练受试者 sub11 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub24 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub07 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub01 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;96m🎯 开始训练受试者: sub25[0m[1;91m❌ ❌ sub11 训练失败[0m

[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96mℹ️ 训练数据形状: torch.Size([140, 38, 48, 48]), 测试数据形状: torch.Size([5, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub03[0m[1;91m❌ ❌ sub24 训练失败[0m

[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96mℹ️ 训练数据形状: torch.Size([140, 38, 48, 48]), 测试数据形状: torch.Size([5, 38, 48, 48])[0m
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub06[0m[1;91m❌ ❌ sub01 训练失败[0m

[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m
[1;96mℹ️ 训练数据形状: torch.Size([141, 38, 48, 48]), 测试数据形状: torch.Size([4, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub15[0m[1;91m❌ ❌ sub07 训练失败[0m

[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96mℹ️ 训练数据形状: torch.Size([142, 38, 48, 48]), 测试数据形状: torch.Size([3, 38, 48, 48])[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8📝 使用传统特征拼接方式

=> 使用折叠除法: 8=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;91m❌ 训练受试者 sub25 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub15 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub06 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub03 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;96m🎯 开始训练受试者: sub20[0m[1;91m❌ ❌ sub25 训练失败[0m

[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub04[0m[1;91m❌ ❌ sub15 训练失败[0m

[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
[1;96m🎯 开始训练受试者: sub13[0m[1;91m❌ ❌ sub06 训练失败[0m

[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub08[0m[1;91m❌ ❌ sub03 训练失败[0m

[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m
[1;96mℹ️ 训练数据形状: torch.Size([144, 38, 48, 48]), 测试数据形状: torch.Size([1, 38, 48, 48])[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8🔧 激活函数配置: 标准ReLU

=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8=> 使用折叠除法: 8


=> 使用折叠除法: 8
📝 使用传统特征拼接方式
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

📝 使用传统特征拼接方式=> 使用折叠除法: 8📝 使用传统特征拼接方式


📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;91m❌ 训练受试者 sub20 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub13 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub04 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub08 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;96m🎯 开始训练受试者: sub16[0m[1;91m❌ ❌ sub20 训练失败[0m

[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub21[0m[1;91m❌ ❌ sub13 训练失败[0m

[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m
[1;96mℹ️ 训练数据形状: torch.Size([144, 38, 48, 48]), 测试数据形状: torch.Size([1, 38, 48, 48])[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;96m🎯 开始训练受试者: sub22[0m[1;91m❌ ❌ sub08 训练失败[0m

[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
=> 使用折叠除法: 8
[1;91m❌ ❌ sub04 训练失败[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

📝 使用传统特征拼接方式
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;91m❌ 训练受试者 sub16 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ ❌ sub16 训练失败[0m
[1;91m❌ 训练受试者 sub21 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ 训练受试者 sub22 时出错: 'tuple' object has no attribute 'log_softmax'[0m
[1;91m❌ ❌ sub21 训练失败[0m
[1;91m❌ ❌ sub22 训练失败[0m

[1;92m==========[0m
[1m[1;92m🎉 并行训练完成 🎉[0m
[1;92m==========[0m

[1;92m✅ 成功训练: 0 个受试者[0m

[1;92m===================[0m
[1m[1;92m📊 并行训练完成，正在计算总体指标 📊[0m
[1;92m===================[0m


================================================================================
【实验结果报告】
================================================================================

【基本信息】
实验名称: SKD_TSTSAN_CASME2_class3_full_conv1_eca1_relu_fix_class3_conv1_eca1_relu
时间: 2025-07-28 23:04:13
总训练时间: 00:00:46
数据集: CASME2_LOSO_full

【系统环境】
操作系统: 💻 Linux 6.8.0-1030-nvidia (x86_64)
处理器: ⚡ 32核心/32线程 @ 2195MHz
内存: 🧠 62.7GB RAM (已用: 26.4GB, 42.0%)
GPU: 🚀 NVIDIA A800 80GB PCIe (79.3GB)

【模型配置】
模型架构: SKD_TSTSAN
是否使用预训练: 是
预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
批次大小: 32
最大迭代次数: 20
损失函数: FocalLoss_weighted
随机种子: 2577

【GPU性能配置】
CUDNN Benchmark: 禁用
CUDNN Deterministic: 禁用
混合精度训练: 禁用
TF32加速: 启用
BatchNorm修复: 禁用
卷积类型: 标准卷积

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: N/A
- UAR分数: N/A

【各表情类别准确率】
--------------------------------------------------

【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
    平均        nan        nan        nan    


【各受试者详细预测结果】
==================================================

================================================================================

【实验结果报告】
================================================================================

================================================================================
【🎯 深度学习微表情识别实验报告 - 3分类 (正性、负性、惊讶)】
================================================================================

【📋 基本信息】
实验名称: SKD_TSTSAN_CASME2_class3_full_conv1_eca1_relu_fix_class3_conv1_eca1_relu
分类方案: 3分类 (正性、负性、惊讶)
完成时间: 2025-07-28 23:04:13
总训练时间: 00:00:46
数据集路径: ./CASME2_LOSO_full
数据集名称: CASME2_LOSO_full


【💻 详细系统环境】
操作系统: 💻 Linux 6.8.0-1030-nvidia (x86_64)
处理器: ⚡ 32核心/32线程 @ 2195MHz
内存状态: 🧠 62.7GB RAM (已用: 26.4GB, 42.0%)
GPU信息: 🚀 NVIDIA A800 80GB PCIe (79.3GB)
GPU利用率: 96%
磁盘状态: 💾 97.4GB 总计 (可用: 0.0GB, 已用: 94.9%)
Python版本: 🐍 Python 3.12.7
PyTorch版本: 🔥 PyTorch 2.7.1+cu126
CUDA版本: ⚡ CUDA 12.6
cuDNN版本: 🧠 cuDNN 90501

【📊 训练统计信息】
受试者数量: 0
平均每受试者训练时间: N/A
总迭代次数: 20
理论样本处理量: N/A
GPU加速: ✅ 启用
多GPU训练: ❌ 单GPU
混合精度: ❌ 禁用

【🤖 模型架构配置】
模型名称: SKD_TSTSAN
分类类别数: 3
预训练模型: ✅ ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth
COCO预训练: ✅ 启用
模型保存: ✅ 启用
卷积类型: 标准卷积

【⚙️ 训练超参数】
学习率: 0.001
基础批次大小: 32
最大迭代次数: 20
损失函数: FocalLoss_weighted
随机种子: 2577
温度参数: 3
蒸馏α值: 0.1
蒸馏β值: 1e-06
数据增强系数: 2

【🚀 GPU性能优化配置】
CUDNN Benchmark: ❌ 禁用
CUDNN Deterministic: ❌ 禁用
混合精度训练: ❌ 禁用
TF32加速: ✅ 启用
BatchNorm修复: ❌ 禁用

【🔄 数据增强策略】
训练数据增强: ✅ 启用
测试数据增强: ✅ 启用
旋转角度范围: 3,8
训练增强倍数: 9,3,10
测试增强倍数: 9,3,10
测试镜像训练: ✅ 启用
镜像训练受试者: sub02,sub05,sub26

【🎯 核心性能指标】
总体性能:
- 🏆 UF1分数: N/A
- 📈 UAR分数: N/A

【📊 各表情类别准确率】
--------------------------------------------------
- 暂无各类别准确率数据


【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
    平均        nan        nan        nan    

❌ 无法生成混淆矩阵：缺少预测或真实标签数据

【性能分析与改进建议】
============================================================
📊 总体性能评估:
   ❌ 较差: 模型性能需要显著改进

⏱️ 训练效率分析:

💡 改进建议:
   🔧 模型性能优化:
      - 尝试调整学习率 (当前: 0.0010)
      - 增加训练迭代次数 (当前: 20)
      - 考虑使用数据增强技术
      - 尝试不同的预训练模型
   ⚡ 性能优化:
      - 启用混合精度训练以提高速度

============================================================

【📝 实验总结】
============================================================
本次实验采用3分类 (正性、负性、惊讶)方案，在CASME2_LOSO_full数据集上进行训练。
总体UF1得分: N/A, UAR得分: N/A
训练耗时: 00:00:46
受试者数量: 0

实验配置摘要:
- 模型: SKD_TSTSAN
- 学习率: 0.001
- 批次大小: 32
- 迭代次数: 20
- 预训练: 是

================================================================================

================================================================================

【邮件通知】正在发送训练结果...

【邮件通知】邮件发送成功!
- 发件人: <EMAIL>
- 收件人: <EMAIL>
- 主题: [3分类 (正性、负性、惊讶)实验总结] SKD_TSTSAN_CASME2_class3_full_conv1_eca1_relu_fix_class3_conv1_eca1_relu - UF1=N/A, UAR=N/A

训练完成!

正在关闭日志系统...
日志系统初始化成功

================================================================================
【训练开始 - GPU预加载 + 并行训练模式】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (3分类)[0m
[1;96m🎯 类别权重: ['0.3794', '0.1349', '0.4857'][0m
开始训练...

[1m[1;94m⚙️ 训练模式配置[0m
[1;94m----------[0m
[1;96mℹ️ GPU数据预加载: ✅ 启用[0m
[1;96mℹ️ 并行训练: ✅ 启用[0m
[1;96mℹ️ 并行工作线程数: 4[0m

[1m[1;96m🚀 GPU数据预加载器初始化[0m
[1;96m----------------[0m
[1;96mℹ️ 数据集路径: ./CASME2_LOSO_full[0m
[1;96mℹ️ 分类数量: 3[0m
[1;96mℹ️ 目标设备: cuda[0m

[1;92m========================[0m
[1m[1;92m🚀 开始预加载CASME2数据集到GPU显存 🚀[0m
[1;92m========================[0m

[1;96m📊 正在估算数据集显存需求...[0m
[1;92m📈 估算结果: 6344 个样本[0m
[1;92m💾 预计显存需求: 2.07 GB[0m
[1;96mℹ️ GPU总显存: 79.25 GB[0m
[1;96mℹ️ 可用显存: 63.40 GB[0m
[1;96mℹ️ 发现 26 个受试者: ['sub01', 'sub02', 'sub03', 'sub04', 'sub05', 'sub06', 'sub07', 'sub08', 'sub09', 'sub10', 'sub11', 'sub12', 'sub13', 'sub14', 'sub15', 'sub16', 'sub17', 'sub18', 'sub19', 'sub20', 'sub21', 'sub22', 'sub23', 'sub24', 'sub25', 'sub26'][0m
[1;96m📊 预加载进度: 1/26 - sub01[0m
[1;96m📥 正在预加载受试者 sub01 的数据...[0m
[1;92m✅ 受试者 sub01 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 0.05 GB[0m
[1;96m📊 预加载进度: 2/26 - sub02[0m
[1;96m📥 正在预加载受试者 sub02 的数据...[0m
[1;92m✅ 受试者 sub02 数据预加载完成[0m
[1;96mℹ️ 训练样本: 136, 测试样本: 9[0m
[1;96m💾 当前显存使用: 0.10 GB[0m
[1;96m📊 预加载进度: 3/26 - sub03[0m
[1;96m📥 正在预加载受试者 sub03 的数据...[0m
[1;92m✅ 受试者 sub03 数据预加载完成[0m
[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96m💾 当前显存使用: 0.14 GB[0m
[1;96m📊 预加载进度: 4/26 - sub04[0m
[1;96m📥 正在预加载受试者 sub04 的数据...[0m
[1;92m✅ 受试者 sub04 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.19 GB[0m
[1;96m📊 预加载进度: 5/26 - sub05[0m
[1;96m📥 正在预加载受试者 sub05 的数据...[0m
[1;92m✅ 受试者 sub05 数据预加载完成[0m
[1;96mℹ️ 训练样本: 138, 测试样本: 7[0m
[1;96m💾 当前显存使用: 0.24 GB[0m
[1;96m📊 预加载进度: 6/26 - sub06[0m
[1;96m📥 正在预加载受试者 sub06 的数据...[0m
[1;92m✅ 受试者 sub06 数据预加载完成[0m
[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m
[1;96m💾 当前显存使用: 0.29 GB[0m
[1;96m📊 预加载进度: 7/26 - sub07[0m
[1;96m📥 正在预加载受试者 sub07 的数据...[0m
[1;92m✅ 受试者 sub07 数据预加载完成[0m
[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96m💾 当前显存使用: 0.33 GB[0m
[1;96m📊 预加载进度: 8/26 - sub08[0m
[1;96m📥 正在预加载受试者 sub08 的数据...[0m
[1;92m✅ 受试者 sub08 数据预加载完成[0m
[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m
[1;96m💾 当前显存使用: 0.38 GB[0m
[1;96m📊 预加载进度: 9/26 - sub09[0m
[1;96m📥 正在预加载受试者 sub09 的数据...[0m
[1;92m✅ 受试者 sub09 数据预加载完成[0m
[1;96mℹ️ 训练样本: 135, 测试样本: 10[0m
[1;96m💾 当前显存使用: 0.43 GB[0m
[1;96m📊 预加载进度: 10/26 - sub10[0m
[1;96m📥 正在预加载受试者 sub10 的数据...[0m
[1;92m✅ 受试者 sub10 数据预加载完成[0m
[1;96mℹ️ 训练样本: 145, 测试样本: 0[0m
[1;96m💾 当前显存使用: 0.48 GB[0m
[1;96m📊 预加载进度: 11/26 - sub11[0m
[1;96m📥 正在预加载受试者 sub11 的数据...[0m
[1;92m✅ 受试者 sub11 数据预加载完成[0m
[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m
[1;96m💾 当前显存使用: 0.53 GB[0m
[1;96m📊 预加载进度: 12/26 - sub12[0m
[1;96m📥 正在预加载受试者 sub12 的数据...[0m
[1;92m✅ 受试者 sub12 数据预加载完成[0m
[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m
[1;96m💾 当前显存使用: 0.57 GB[0m
[1;96m📊 预加载进度: 13/26 - sub13[0m
[1;96m📥 正在预加载受试者 sub13 的数据...[0m
[1;92m✅ 受试者 sub13 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.62 GB[0m
[1;96m📊 预加载进度: 14/26 - sub14[0m
[1;96m📥 正在预加载受试者 sub14 的数据...[0m
[1;92m✅ 受试者 sub14 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 0.67 GB[0m
[1;96m📊 预加载进度: 15/26 - sub15[0m
[1;96m📥 正在预加载受试者 sub15 的数据...[0m
[1;92m✅ 受试者 sub15 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 0.72 GB[0m
[1;96m📊 预加载进度: 16/26 - sub16[0m
[1;96m📥 正在预加载受试者 sub16 的数据...[0m
[1;92m✅ 受试者 sub16 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.76 GB[0m
[1;96m📊 预加载进度: 17/26 - sub17[0m
[1;96m📥 正在预加载受试者 sub17 的数据...[0m
[1;92m✅ 受试者 sub17 数据预加载完成[0m
[1;96mℹ️ 训练样本: 114, 测试样本: 31[0m
[1;96m💾 当前显存使用: 0.81 GB[0m
[1;96m📊 预加载进度: 18/26 - sub18[0m
[1;96m📥 正在预加载受试者 sub18 的数据...[0m
[1;92m✅ 受试者 sub18 数据预加载完成[0m
[1;96mℹ️ 训练样本: 145, 测试样本: 0[0m
[1;96m💾 当前显存使用: 0.86 GB[0m
[1;96m📊 预加载进度: 19/26 - sub19[0m
[1;96m📥 正在预加载受试者 sub19 的数据...[0m
[1;92m✅ 受试者 sub19 数据预加载完成[0m
[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m
[1;96m💾 当前显存使用: 0.91 GB[0m
[1;96m📊 预加载进度: 20/26 - sub20[0m
[1;96m📥 正在预加载受试者 sub20 的数据...[0m
[1;92m✅ 受试者 sub20 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 0.95 GB[0m
[1;96m📊 预加载进度: 21/26 - sub21[0m
[1;96m📥 正在预加载受试者 sub21 的数据...[0m
[1;92m✅ 受试者 sub21 数据预加载完成[0m
[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m
[1;96m💾 当前显存使用: 1.00 GB[0m
[1;96m📊 预加载进度: 22/26 - sub22[0m
[1;96m📥 正在预加载受试者 sub22 的数据...[0m
[1;92m✅ 受试者 sub22 数据预加载完成[0m
[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96m💾 当前显存使用: 1.05 GB[0m
[1;96m📊 预加载进度: 23/26 - sub23[0m
[1;96m📥 正在预加载受试者 sub23 的数据...[0m
[1;92m✅ 受试者 sub23 数据预加载完成[0m
[1;96mℹ️ 训练样本: 137, 测试样本: 8[0m
[1;96m💾 当前显存使用: 1.10 GB[0m
[1;96m📊 预加载进度: 24/26 - sub24[0m
[1;96m📥 正在预加载受试者 sub24 的数据...[0m
[1;92m✅ 受试者 sub24 数据预加载完成[0m
[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96m💾 当前显存使用: 1.14 GB[0m
[1;96m📊 预加载进度: 25/26 - sub25[0m
[1;96m📥 正在预加载受试者 sub25 的数据...[0m
[1;92m✅ 受试者 sub25 数据预加载完成[0m
[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96m💾 当前显存使用: 1.19 GB[0m
[1;96m📊 预加载进度: 26/26 - sub26[0m
[1;96m📥 正在预加载受试者 sub26 的数据...[0m
[1;92m✅ 受试者 sub26 数据预加载完成[0m
[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m
[1;96m💾 当前显存使用: 1.24 GB[0m

[1;92m===========[0m
[1m[1;92m✅ 数据预加载完成 ✅[0m
[1;92m===========[0m

[1;92m✅ 成功预加载: 26 个受试者[0m
[1m[1;95m💾 最终显存使用: 1.24 GB[0m
根据配置,不使用Visdom可视化

[1;95m=====================[0m
[1m[1;95m🚀 使用GPU预加载 + 并行训练模式 🚀[0m
[1;95m=====================[0m

[1;96mℹ️ 可用受试者: 23 个[0m
[1;96mℹ️ 受试者列表: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub23', 'sub12', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub06', 'sub15', 'sub20', 'sub04', 'sub13', 'sub08', 'sub16', 'sub21', 'sub22'][0m

[1;95m==================[0m
[1m[1;95m⚡ 开始并行训练 23 个受试者 ⚡[0m
[1;95m==================[0m

[1;96mℹ️ 并行工作线程数: 4[0m
[1;96m🎯 开始训练受试者: sub17[0m
[1;96mℹ️ 训练样本: 114, 测试样本: 31[0m[1;96m🎯 开始训练受试者: sub05[0m

[1;96m🎯 开始训练受试者: sub26[0m[1;96mℹ️ 训练样本: 138, 测试样本: 7[0m[1;96m🎯 开始训练受试者: sub19[0m
[1;96mℹ️ 训练数据形状: torch.Size([114, 38, 48, 48]), 测试数据形状: torch.Size([31, 38, 48, 48])[0m

[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m

[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m[1;96mℹ️ 训练数据形状: torch.Size([138, 38, 48, 48]), 测试数据形状: torch.Size([7, 38, 48, 48])[0m[1;96mℹ️ 训练数据形状: torch.Size([134, 38, 48, 48]), 测试数据形状: torch.Size([11, 38, 48, 48])[0m


[1;96mℹ️ 训练数据形状: torch.Size([134, 38, 48, 48]), 测试数据形状: torch.Size([11, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
🔧 激活函数配置: 标准ReLU
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式📝 使用传统特征拼接方式

=> 使用折叠除法: 8
📝 使用传统特征拼接方式
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub17 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;96m🎯 开始训练受试者: sub09[0m[1;92m✅ ✅ sub17 训练完成[0m

[1;96mℹ️ 训练样本: 135, 测试样本: 10[0m
[1;96mℹ️ 训练数据形状: torch.Size([135, 38, 48, 48]), 测试数据形状: torch.Size([10, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub05 训练完成: UF1=0.0000, UAR=0.0000[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub19 训练完成: UF1=0.0000, UAR=0.0000[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub26 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;96m🎯 开始训练受试者: sub02[0m[1;92m✅ ✅ sub05 训练完成[0m

[1;96mℹ️ 训练样本: 136, 测试样本: 9[0m
[1;96mℹ️ 训练数据形状: torch.Size([136, 38, 48, 48]), 测试数据形状: torch.Size([9, 38, 48, 48])[0m
[1;96m🎯 开始训练受试者: sub23[0m[1;92m✅ ✅ sub19 训练完成[0m

[1;96mℹ️ 训练样本: 137, 测试样本: 8[0m
[1;96mℹ️ 训练数据形状: torch.Size([137, 38, 48, 48]), 测试数据形状: torch.Size([8, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
[1;96m🎯 开始训练受试者: sub12[0m[1;92m✅ ✅ sub26 训练完成[0m

[1;96mℹ️ 训练样本: 134, 测试样本: 11[0m
[1;96mℹ️ 训练数据形状: torch.Size([134, 38, 48, 48]), 测试数据形状: torch.Size([11, 38, 48, 48])[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8

📝 使用传统特征拼接方式
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8📝 使用传统特征拼接方式

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;96mℹ️ 加载预训练模型...[0m
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub09 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;96m🎯 开始训练受试者: sub11[0m[1;92m✅ ✅ sub09 训练完成[0m

[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m
[1;96mℹ️ 训练数据形状: torch.Size([141, 38, 48, 48]), 测试数据形状: torch.Size([4, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub11 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;96m🎯 开始训练受试者: sub01[0m[1;92m✅ ✅ sub11 训练完成[0m

[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96mℹ️ 训练数据形状: torch.Size([142, 38, 48, 48]), 测试数据形状: torch.Size([3, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub12 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;96m🎯 开始训练受试者: sub07[0m[1;92m✅ ✅ sub12 训练完成[0m

[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96mℹ️ 训练数据形状: torch.Size([140, 38, 48, 48]), 测试数据形状: torch.Size([5, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub01 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;96m🎯 开始训练受试者: sub24[0m[1;92m✅ ✅ sub01 训练完成[0m

[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96mℹ️ 训练数据形状: torch.Size([142, 38, 48, 48]), 测试数据形状: torch.Size([3, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub24 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;96m🎯 开始训练受试者: sub25[0m[1;92m✅ ✅ sub24 训练完成[0m

[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m
[1;96mℹ️ 训练数据形状: torch.Size([140, 38, 48, 48]), 测试数据形状: torch.Size([5, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub23 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;96m🎯 开始训练受试者: sub03[0m
[1;92m✅ ✅ sub23 训练完成[0m[1;96mℹ️ 训练样本: 140, 测试样本: 5[0m

[1;96mℹ️ 训练数据形状: torch.Size([140, 38, 48, 48]), 测试数据形状: torch.Size([5, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub07 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;96m🎯 开始训练受试者: sub06[0m[1;92m✅ ✅ sub07 训练完成[0m
[1;96mℹ️ 训练样本: 141, 测试样本: 4[0m

[1;96mℹ️ 训练数据形状: torch.Size([141, 38, 48, 48]), 测试数据形状: torch.Size([4, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub25 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;96m🎯 开始训练受试者: sub15[0m[1;92m✅ ✅ sub25 训练完成[0m

[1;96mℹ️ 训练样本: 142, 测试样本: 3[0m
[1;96mℹ️ 训练数据形状: torch.Size([142, 38, 48, 48]), 测试数据形状: torch.Size([3, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub02 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;96m🎯 开始训练受试者: sub20[0m
[1;92m✅ ✅ sub02 训练完成[0m[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m

[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub15 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;96m🎯 开始训练受试者: sub04[0m[1;92m✅ ✅ sub15 训练完成[0m

[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub06 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;96m🎯 开始训练受试者: sub13[0m[1;92m✅ ✅ sub06 训练完成[0m

[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub04 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;96m🎯 开始训练受试者: sub08[0m[1;92m✅ ✅ sub04 训练完成[0m

[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m
[1;96mℹ️ 训练数据形状: torch.Size([144, 38, 48, 48]), 测试数据形状: torch.Size([1, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub03 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;96m🎯 开始训练受试者: sub16[0m[1;92m✅ ✅ sub03 训练完成[0m

[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub13 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;96m🎯 开始训练受试者: sub21[0m[1;92m✅ ✅ sub13 训练完成[0m

[1;96mℹ️ 训练样本: 144, 测试样本: 1[0m
[1;96mℹ️ 训练数据形状: torch.Size([144, 38, 48, 48]), 测试数据形状: torch.Size([1, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub08 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
[1;96m🎯 开始训练受试者: sub22[0m[1;92m✅ ✅ sub08 训练完成[0m

[1;96mℹ️ 训练样本: 143, 测试样本: 2[0m
[1;96mℹ️ 训练数据形状: torch.Size([143, 38, 48, 48]), 测试数据形状: torch.Size([2, 38, 48, 48])[0m
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth[0m
[1;96mℹ️ 加载了 356/362 个层[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub22 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ ✅ sub22 训练完成[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub21 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ ✅ sub21 训练完成[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub16 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ ✅ sub16 训练完成[0m
错误: 真实标签或预测标签为空
[1;92m✅ 受试者 sub20 训练完成: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ ✅ sub20 训练完成[0m

[1;92m==========[0m
[1m[1;92m🎉 并行训练完成 🎉[0m
[1;92m==========[0m

[1;92m✅ 成功训练: 23 个受试者[0m
[1;92m✅ 受试者 sub17: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub05: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub19: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub26: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub09: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub11: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub12: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub01: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub24: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub23: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub07: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub25: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub02: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub15: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub06: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub04: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub03: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub13: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub08: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub22: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub21: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub16: UF1=0.0000, UAR=0.0000[0m
[1;92m✅ 受试者 sub20: UF1=0.0000, UAR=0.0000[0m

[1;92m===================[0m
[1m[1;92m📊 并行训练完成，正在计算总体指标 📊[0m
[1;92m===================[0m


================================================================================
【实验结果报告】
================================================================================

【基本信息】
实验名称: SKD_TSTSAN_CASME2_class3_full_conv1_eca1_relu_fix_class3_conv1_eca1_relu
时间: 2025-07-28 23:08:15
总训练时间: 00:02:05
数据集: CASME2_LOSO_full

【系统环境】
操作系统: 💻 Linux 6.8.0-1030-nvidia (x86_64)
处理器: ⚡ 32核心/32线程 @ 2195MHz
内存: 🧠 62.7GB RAM (已用: 23.0GB, 36.6%)
GPU: 🚀 NVIDIA A800 80GB PCIe (79.3GB)

【模型配置】
模型架构: SKD_TSTSAN
是否使用预训练: 是
预训练模型: ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
批次大小: 32
最大迭代次数: 20
损失函数: FocalLoss_weighted
随机种子: 2577

【GPU性能配置】
CUDNN Benchmark: 禁用
CUDNN Deterministic: 禁用
混合精度训练: 禁用
TF32加速: 启用
BatchNorm修复: 禁用
卷积类型: 标准卷积

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: N/A
- UAR分数: N/A

【各表情类别准确率】
--------------------------------------------------

【各受试者评估结果】
--------------------------------------------------
受试者 sub01: UF1=0.0000, UAR=0.0000
受试者 sub02: UF1=0.0000, UAR=0.0000
受试者 sub03: UF1=0.0000, UAR=0.0000
受试者 sub04: UF1=0.0000, UAR=0.0000
受试者 sub05: UF1=0.0000, UAR=0.0000
受试者 sub06: UF1=0.0000, UAR=0.0000
受试者 sub07: UF1=0.0000, UAR=0.0000
受试者 sub08: UF1=0.0000, UAR=0.0000
受试者 sub09: UF1=0.0000, UAR=0.0000
受试者 sub11: UF1=0.0000, UAR=0.0000
受试者 sub12: UF1=0.0000, UAR=0.0000
受试者 sub13: UF1=0.0000, UAR=0.0000
受试者 sub15: UF1=0.0000, UAR=0.0000
受试者 sub16: UF1=0.0000, UAR=0.0000
受试者 sub17: UF1=0.0000, UAR=0.0000
受试者 sub19: UF1=0.0000, UAR=0.0000
受试者 sub20: UF1=0.0000, UAR=0.0000
受试者 sub21: UF1=0.0000, UAR=0.0000
受试者 sub22: UF1=0.0000, UAR=0.0000
受试者 sub23: UF1=0.0000, UAR=0.0000
受试者 sub24: UF1=0.0000, UAR=0.0000
受试者 sub25: UF1=0.0000, UAR=0.0000
受试者 sub26: UF1=0.0000, UAR=0.0000
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
  sub01      0.0000     0.0000     0.0000  
  sub02      0.0000     0.0000     0.0000  
  sub03      0.0000     0.0000     0.0000  
  sub04      0.0000     0.0000     0.0000  
  sub05      0.0000     0.0000     0.0000  
  sub06      0.0000     0.0000     0.0000  
  sub07      0.0000     0.0000     0.0000  
  sub08      0.0000     0.0000     0.0000  
  sub09      0.0000     0.0000     0.0000  
  sub11      0.0000     0.0000     0.0000  
  sub12      0.0000     0.0000     0.0000  
  sub13      0.0000     0.0000     0.0000  
  sub15      0.0000     0.0000     0.0000  
  sub16      0.0000     0.0000     0.0000  
  sub17      0.0000     0.0000     0.0000  
  sub19      0.0000     0.0000     0.0000  
  sub20      0.0000     0.0000     0.0000  
  sub21      0.0000     0.0000     0.0000  
  sub22      0.0000     0.0000     0.0000  
  sub23      0.0000     0.0000     0.0000  
  sub24      0.0000     0.0000     0.0000  
  sub25      0.0000     0.0000     0.0000  
  sub26      0.0000     0.0000     0.0000  
--------------------------------------------------
    平均       0.0000     0.0000     0.0000  


【各受试者详细预测结果】
==================================================

受试者 sub01:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub02:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub03:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub04:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub05:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub06:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub07:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub08:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub09:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub11:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub12:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub13:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub15:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub16:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub17:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub19:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub20:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub21:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub22:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub23:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub24:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub25:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

受试者 sub26:
性能指标: UF1=0.0000, UAR=0.0000
警告: 该受试者缺少预测或真实标签数据

================================================================================

【实验结果报告】
================================================================================

================================================================================
【🎯 深度学习微表情识别实验报告 - 3分类 (正性、负性、惊讶)】
================================================================================

【📋 基本信息】
实验名称: SKD_TSTSAN_CASME2_class3_full_conv1_eca1_relu_fix_class3_conv1_eca1_relu
分类方案: 3分类 (正性、负性、惊讶)
完成时间: 2025-07-28 23:08:15
总训练时间: 00:02:05
数据集路径: ./CASME2_LOSO_full
数据集名称: CASME2_LOSO_full


【💻 详细系统环境】
操作系统: 💻 Linux 6.8.0-1030-nvidia (x86_64)
处理器: ⚡ 32核心/32线程 @ 2195MHz
内存状态: 🧠 62.7GB RAM (已用: 23.0GB, 36.6%)
GPU信息: 🚀 NVIDIA A800 80GB PCIe (79.3GB)
GPU利用率: 47%
磁盘状态: 💾 97.4GB 总计 (可用: 0.0GB, 已用: 94.9%)
Python版本: 🐍 Python 3.12.7
PyTorch版本: 🔥 PyTorch 2.7.1+cu126
CUDA版本: ⚡ CUDA 12.6
cuDNN版本: 🧠 cuDNN 90501

【📊 训练统计信息】
受试者数量: 23
平均每受试者训练时间: 00:00:05
UF1最高: 0.0000
UF1最低: 0.0000
UF1标准差: 0.0000
UAR最高: 0.0000
UAR最低: 0.0000
UAR标准差: 0.0000
总迭代次数: 460
理论样本处理量: 14720
GPU加速: ✅ 启用
多GPU训练: ❌ 单GPU
混合精度: ❌ 禁用

【🤖 模型架构配置】
模型名称: SKD_TSTSAN
分类类别数: 3
预训练模型: ✅ ck门控融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_gate.pth
COCO预训练: ✅ 启用
模型保存: ✅ 启用
卷积类型: 标准卷积

【⚙️ 训练超参数】
学习率: 0.001
基础批次大小: 32
最大迭代次数: 20
损失函数: FocalLoss_weighted
随机种子: 2577
温度参数: 3
蒸馏α值: 0.1
蒸馏β值: 1e-06
数据增强系数: 2

【🚀 GPU性能优化配置】
CUDNN Benchmark: ❌ 禁用
CUDNN Deterministic: ❌ 禁用
混合精度训练: ❌ 禁用
TF32加速: ✅ 启用
BatchNorm修复: ❌ 禁用

【🔄 数据增强策略】
训练数据增强: ✅ 启用
测试数据增强: ✅ 启用
旋转角度范围: 3,8
训练增强倍数: 9,3,10
测试增强倍数: 9,3,10
测试镜像训练: ✅ 启用
镜像训练受试者: sub02,sub05,sub26

【🎯 核心性能指标】
总体性能:
- 🏆 UF1分数: N/A
- 📈 UAR分数: N/A

【📊 各表情类别准确率】
--------------------------------------------------
- 暂无各类别准确率数据


【各受试者评估结果】
--------------------------------------------------
受试者 sub01: UF1=0.0000, UAR=0.0000
受试者 sub02: UF1=0.0000, UAR=0.0000
受试者 sub03: UF1=0.0000, UAR=0.0000
受试者 sub04: UF1=0.0000, UAR=0.0000
受试者 sub05: UF1=0.0000, UAR=0.0000
受试者 sub06: UF1=0.0000, UAR=0.0000
受试者 sub07: UF1=0.0000, UAR=0.0000
受试者 sub08: UF1=0.0000, UAR=0.0000
受试者 sub09: UF1=0.0000, UAR=0.0000
受试者 sub11: UF1=0.0000, UAR=0.0000
受试者 sub12: UF1=0.0000, UAR=0.0000
受试者 sub13: UF1=0.0000, UAR=0.0000
受试者 sub15: UF1=0.0000, UAR=0.0000
受试者 sub16: UF1=0.0000, UAR=0.0000
受试者 sub17: UF1=0.0000, UAR=0.0000
受试者 sub19: UF1=0.0000, UAR=0.0000
受试者 sub20: UF1=0.0000, UAR=0.0000
受试者 sub21: UF1=0.0000, UAR=0.0000
受试者 sub22: UF1=0.0000, UAR=0.0000
受试者 sub23: UF1=0.0000, UAR=0.0000
受试者 sub24: UF1=0.0000, UAR=0.0000
受试者 sub25: UF1=0.0000, UAR=0.0000
受试者 sub26: UF1=0.0000, UAR=0.0000
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
  sub01      0.0000     0.0000     0.0000  
  sub02      0.0000     0.0000     0.0000  
  sub03      0.0000     0.0000     0.0000  
  sub04      0.0000     0.0000     0.0000  
  sub05      0.0000     0.0000     0.0000  
  sub06      0.0000     0.0000     0.0000  
  sub07      0.0000     0.0000     0.0000  
  sub08      0.0000     0.0000     0.0000  
  sub09      0.0000     0.0000     0.0000  
  sub11      0.0000     0.0000     0.0000  
  sub12      0.0000     0.0000     0.0000  
  sub13      0.0000     0.0000     0.0000  
  sub15      0.0000     0.0000     0.0000  
  sub16      0.0000     0.0000     0.0000  
  sub17      0.0000     0.0000     0.0000  
  sub19      0.0000     0.0000     0.0000  
  sub20      0.0000     0.0000     0.0000  
  sub21      0.0000     0.0000     0.0000  
  sub22      0.0000     0.0000     0.0000  
  sub23      0.0000     0.0000     0.0000  
  sub24      0.0000     0.0000     0.0000  
  sub25      0.0000     0.0000     0.0000  
  sub26      0.0000     0.0000     0.0000  
--------------------------------------------------
    平均       0.0000     0.0000     0.0000  

❌ 无法生成混淆矩阵：缺少预测或真实标签数据

【性能分析与改进建议】
============================================================
📊 总体性能评估:
   ❌ 较差: 模型性能需要显著改进

⏱️ 训练效率分析:
   ✅ 训练速度快

💡 改进建议:
   🔧 模型性能优化:
      - 尝试调整学习率 (当前: 0.0010)
      - 增加训练迭代次数 (当前: 20)
      - 考虑使用数据增强技术
      - 尝试不同的预训练模型
   ⚡ 性能优化:
      - 启用混合精度训练以提高速度

============================================================

【🔍 详细预测结果与标签对比】
============================================================

📋 受试者 sub01 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub02 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub03 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub04 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub05 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub06 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub07 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub08 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub09 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub11 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub12 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub13 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub15 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub16 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub17 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub19 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub20 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub21 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub22 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub23 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub24 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub25 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

📋 受试者 sub26 详细分析:
   🎯 性能指标: UF1=0.0000, UAR=0.0000
   ⚠️  警告: 缺少预测或真实标签数据
------------------------------------------------------------

【📝 实验总结】
============================================================
本次实验采用3分类 (正性、负性、惊讶)方案，在CASME2_LOSO_full数据集上进行训练。
总体UF1得分: N/A, UAR得分: N/A
训练耗时: 00:02:05
受试者数量: 23

实验配置摘要:
- 模型: SKD_TSTSAN
- 学习率: 0.001
- 批次大小: 32
- 迭代次数: 20
- 预训练: 是

================================================================================

================================================================================

【邮件通知】正在发送训练结果...

【邮件通知】邮件发送成功!
- 发件人: <EMAIL>
- 收件人: <EMAIL>
- 主题: [3分类 (正性、负性、惊讶)实验总结] SKD_TSTSAN_CASME2_class3_full_conv1_eca1_relu_fix_class3_conv1_eca1_relu - UF1=N/A, UAR=N/A

训练完成!

正在关闭日志系统...
