import torch.nn as nn
import torch
import torch.nn.functional as F
import math
from motion_magnification_learning_based_master.magnet import Manipulator as MagManipulator
from motion_magnification_learning_based_master.magnet import Encoder_No_texture as MagEncoder_No_texture
from Pinwheel_shaped_Convolution import Pinwheel_shaped_Convolution
from WTConv import WTConv2d
from PRO_CODE.WTRFAConv import WTRFAConv
from PRO_CODE.WTPRFAConv import WTPRFAConv
from PRO_CODE.WTPRFAConv_Lite import WTPRFAConv_Lite
from PRO_CODE.LEGM import LEGM


class LightweightCrossBranchFusion(nn.Module):
    """
    轻量化跨分支特征交互机制 (Lightweight Cross-Branch Fusion, LCBF)

    针对微表情识别任务优化的高效跨分支交互模块：
    1. 轻量化设计：大幅减少参数量和计算复杂度
    2. 选择性交互：只在关键特征间进行交互，避免信息冗余
    3. 自适应门控：动态控制交互强度，防止特征污染
    4. 残差保护：确保原始分支特征不被过度修改

    Args:
        channels: 特征通道数
        num_branches: 分支数量，默认为3（L、S、T）
        reduction_ratio: 通道压缩比例，用于轻量化
        interaction_threshold: 交互阈值，控制交互的选择性
    """
    def __init__(self, channels, num_branches=3, reduction_ratio=8, interaction_threshold=0.1):
        super(LightweightCrossBranchFusion, self).__init__()
        self.channels = channels
        self.num_branches = num_branches
        self.reduction_ratio = reduction_ratio
        self.interaction_threshold = interaction_threshold

        # 轻量化通道压缩
        reduced_channels = max(8, channels // reduction_ratio)

        # 轻量化全局上下文提取器
        self.context_extractor = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, reduced_channels, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(reduced_channels, num_branches, 1, bias=False),
            nn.Sigmoid()
        )

        # 轻量化分支特征变换器（共享权重以减少参数）
        self.shared_transformer = nn.Sequential(
            nn.Conv2d(channels, channels, 1, groups=min(channels, 8), bias=False),
            nn.BatchNorm2d(channels),
            nn.ReLU(inplace=True)
        )

        # 自适应门控机制
        self.gate_controller = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, reduced_channels, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(reduced_channels, 1, 1, bias=False),
            nn.Sigmoid()
        )

        # 轻量化特征融合器
        self.fusion_conv = nn.Conv2d(channels, channels, 1, groups=min(channels, 4), bias=False)
        self.fusion_bn = nn.BatchNorm2d(channels)

        # 可学习的交互强度参数
        self.interaction_strength = nn.Parameter(torch.tensor(0.3))

        # 初始化权重
        self._init_weights()

    def _init_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, branch_features):
        """
        轻量化跨分支特征交互前向传播

        Args:
            branch_features: 包含三个分支特征的列表 [x_L, x_S, x_T]
                           每个特征的形状为 [B, C, H, W]

        Returns:
            enhanced_features: 增强后的分支特征列表
        """
        B, C, H, W = branch_features[0].shape

        # 1. 计算分支重要性权重（轻量化版本）
        branch_importance = []
        for feature in branch_features:
            importance = self.context_extractor(feature).squeeze(-1).squeeze(-1)  # [B, num_branches]
            branch_importance.append(importance)

        # 2. 选择性特征变换（共享变换器减少参数）
        transformed_features = []
        for feature in branch_features:
            transformed = self.shared_transformer(feature)
            transformed_features.append(transformed)

        # 3. 自适应门控交互
        enhanced_features = []
        for i, feature in enumerate(transformed_features):
            # 计算当前分支的门控权重
            gate_weight = self.gate_controller(feature)  # [B, 1, 1, 1]

            # 选择性跨分支信息聚合
            cross_info = torch.zeros_like(feature)
            total_weight = 0

            for j, other_feature in enumerate(transformed_features):
                if i != j:
                    # 计算交互权重（基于特征相似性）
                    similarity = torch.cosine_similarity(
                        F.adaptive_avg_pool2d(feature, 1).flatten(1),
                        F.adaptive_avg_pool2d(other_feature, 1).flatten(1),
                        dim=1
                    ).unsqueeze(-1).unsqueeze(-1).unsqueeze(-1)  # [B, 1, 1, 1]

                    # 只有相似性超过阈值才进行交互
                    interaction_mask = (similarity > self.interaction_threshold).float()
                    interaction_weight = similarity * interaction_mask

                    cross_info += interaction_weight * other_feature
                    total_weight += interaction_weight

            # 归一化跨分支信息
            cross_info = cross_info / (total_weight + 1e-8)

            # 应用轻量化融合
            fused_info = self.fusion_conv(cross_info)
            fused_info = self.fusion_bn(fused_info)

            # 自适应残差连接
            interaction_intensity = gate_weight * self.interaction_strength
            enhanced_feature = feature + interaction_intensity * fused_info

            enhanced_features.append(enhanced_feature)

        return enhanced_features


def gen_state_dict(weights_path):
    st = torch.load(weights_path)
    st_ks = list(st.keys())
    st_vs = list(st.values())
    state_dict = {}
    for st_k, st_v in zip(st_ks, st_vs):
        state_dict[st_k.replace('module.', '')] = st_v
    return state_dict

class ConsensusModule(torch.nn.Module):

    def __init__(self, consensus_type, dim=1):
        super(ConsensusModule, self).__init__()
        self.consensus_type = consensus_type if consensus_type != 'rnn' else 'identity'
        self.dim = dim

    def forward(self, input):
        return SegmentConsensus(self.consensus_type, self.dim)(input)

class SegmentConsensus(torch.nn.Module):

    def __init__(self, consensus_type, dim=1):
        super(SegmentConsensus, self).__init__()
        self.consensus_type = consensus_type
        self.dim = dim
        self.shape = None

    def forward(self, input_tensor):
        self.shape = input_tensor.size()
        if self.consensus_type == 'avg':
            output = input_tensor.mean(dim=self.dim, keepdim=True)
        elif self.consensus_type == 'identity':
            output = input_tensor
        else:
            output = None

        return output

class TemporalShift(nn.Module):
    def __init__(self, net, n_segment=3, n_div=8, inplace=False):
        super(TemporalShift, self).__init__()
        self.net = net
        self.n_segment = n_segment
        self.fold_div = n_div
        self.inplace = inplace
        if inplace:
            print('=> 使用原地移位...')
        print('=> 使用折叠除法: {}'.format(self.fold_div))

    def forward(self, x):
        x = self.shift(x, self.n_segment, fold_div=self.fold_div, inplace=self.inplace)
        return self.net(x)

    @staticmethod
    def shift(x, n_segment, fold_div=3, inplace=False):
        nt, c, h, w = x.size()
        n_batch = nt // n_segment
        x = x.view(n_batch, n_segment, c, h, w)

        fold = c // fold_div
        if inplace:
            raise NotImplementedError
        else:
            out = torch.zeros_like(x)
            out[:, :-1, :fold] = x[:, 1:, :fold]  # shift left
            out[:, 1:, fold: 2 * fold] = x[:, :-1, fold: 2 * fold]  # shift right
            out[:, :, 2 * fold:] = x[:, :, 2 * fold:]  # not shift

        return out.view(nt, c, h, w)


class OptimizedTemporalShift(nn.Module):
    """
    优化的时间移位模块 (Optimized Temporal Shift Module, OTSM)

    针对微表情识别任务优化的轻量化时间建模模块：
    1. 自适应移位策略：根据输入特征动态调整移位模式
    2. 通道分组处理：减少计算复杂度的同时保持表达能力
    3. 残差连接设计：防止时间信息丢失
    4. 轻量化实现：大幅减少参数量和内存占用

    Args:
        net: 要包装的网络层
        n_segment: 时间段数，默认为2
        n_div: 通道分割比例，默认为8
        adaptive_shift: 是否启用自适应移位，默认为True
        lightweight: 是否使用轻量化模式，默认为True
    """
    def __init__(self, net, n_segment=2, n_div=8, adaptive_shift=True, lightweight=True):
        super(OptimizedTemporalShift, self).__init__()
        self.net = net
        self.n_segment = n_segment
        self.fold_div = n_div
        self.adaptive_shift = adaptive_shift
        self.lightweight = lightweight

        if adaptive_shift:
            # 获取网络的输出通道数
            if hasattr(net, 'out_channels'):
                channels = net.out_channels
            elif hasattr(net, 'weight') and len(net.weight.shape) >= 2:
                channels = net.weight.shape[0]
            else:
                channels = 64

            if lightweight:
                # 轻量化设计：使用通道分组和共享权重
                self.num_groups = min(8, channels // 8)  # 自适应分组数
                group_channels = channels // self.num_groups

                # 每组共享移位权重（大幅减少参数）
                self.shift_weights = nn.Parameter(torch.ones(self.num_groups, 3))  # 3种基本移位模式

                # 全局移位强度控制
                self.shift_intensity = nn.Parameter(torch.tensor(0.5))

                print(f"🔄 启用轻量化自适应时间移位: {channels}通道, {self.num_groups}组")
            else:
                # 标准自适应设计
                self.shift_weights = nn.Parameter(torch.ones(channels, 3))
                self.shift_intensity = nn.Parameter(torch.tensor(0.5))
                print(f"🔄 启用标准自适应时间移位: {channels}通道")
        else:
            print("🔄 使用传统固定时间移位")

    def forward(self, x):
        if self.adaptive_shift:
            x = self.adaptive_temporal_shift(x)
        else:
            x = TemporalShift.shift(x, self.n_segment, fold_div=self.fold_div, inplace=False)
        return self.net(x)

    def adaptive_temporal_shift(self, x):
        """
        自适应时间移位操作

        Args:
            x: 输入特征 [nt, c, h, w]

        Returns:
            shifted_x: 移位后的特征
        """
        nt, c, h, w = x.size()
        n_batch = nt // self.n_segment
        x = x.view(n_batch, self.n_segment, c, h, w)

        if self.lightweight:
            # 轻量化处理：通道分组
            group_size = c // self.num_groups
            out = torch.zeros_like(x)

            for g in range(self.num_groups):
                start_idx = g * group_size
                end_idx = start_idx + group_size if g < self.num_groups - 1 else c

                # 获取当前组的移位权重
                group_weights = torch.softmax(self.shift_weights[g], dim=0)

                # 当前组的特征
                group_x = x[:, :, start_idx:end_idx, :, :]

                # 三种基本移位模式
                # 模式1: 左移 (未来信息)
                left_shift = torch.zeros_like(group_x)
                if self.n_segment > 1:
                    left_shift[:, :-1, :] = group_x[:, 1:, :]

                # 模式2: 右移 (历史信息)
                right_shift = torch.zeros_like(group_x)
                if self.n_segment > 1:
                    right_shift[:, 1:, :] = group_x[:, :-1, :]

                # 模式3: 保持不变 (当前信息)
                no_shift = group_x

                # 加权融合
                group_out = (group_weights[0] * left_shift +
                           group_weights[1] * right_shift +
                           group_weights[2] * no_shift) * self.shift_intensity

                # 残差连接
                group_out = group_out + group_x * (1 - self.shift_intensity)

                out[:, :, start_idx:end_idx, :, :] = group_out
        else:
            # 标准处理：每个通道独立
            shift_probs = torch.softmax(self.shift_weights, dim=-1)  # [c, 3]
            out = torch.zeros_like(x)

            # 批量处理三种移位模式
            left_shift = torch.zeros_like(x)
            right_shift = torch.zeros_like(x)
            no_shift = x

            if self.n_segment > 1:
                left_shift[:, :-1, :] = x[:, 1:, :]
                right_shift[:, 1:, :] = x[:, :-1, :]

            # 逐通道应用权重
            for c_idx in range(c):
                weights = shift_probs[c_idx]
                channel_out = (weights[0] * left_shift[:, :, c_idx:c_idx+1, :, :] +
                             weights[1] * right_shift[:, :, c_idx:c_idx+1, :, :] +
                             weights[2] * no_shift[:, :, c_idx:c_idx+1, :, :]) * self.shift_intensity

                # 残差连接
                channel_out = channel_out + x[:, :, c_idx:c_idx+1, :, :] * (1 - self.shift_intensity)
                out[:, :, c_idx:c_idx+1, :, :] = channel_out

        return out.view(nt, c, h, w)

    def get_shift_statistics(self):
        """获取移位统计信息，用于分析和可视化"""
        if not self.adaptive_shift:
            return None

        if self.lightweight:
            shift_probs = torch.softmax(self.shift_weights, dim=-1)
            stats = {
                'group_weights_mean': shift_probs.mean(dim=0).detach().cpu().numpy(),
                'shift_intensity': self.shift_intensity.item(),
                'num_groups': self.num_groups,
                'mode': 'lightweight'
            }
        else:
            shift_probs = torch.softmax(self.shift_weights, dim=-1)
            stats = {
                'channel_weights_mean': shift_probs.mean(dim=0).detach().cpu().numpy(),
                'shift_intensity': self.shift_intensity.item(),
                'mode': 'standard'
            }
        return stats


class ECA_Optimized(nn.Module):
    """
    优化的轻量化ECA机制 (ECA-Optimized)

    针对微表情识别任务的特点进行深度优化：
    1. 自适应通道重要性学习：根据微表情特征的稀疏性动态调整通道权重
    2. 多尺度时空感受野：结合不同尺度的卷积核捕获细微变化
    3. 残差门控机制：防止注意力过度抑制重要特征
    4. 轻量化设计：大幅减少参数量和计算复杂度

    Args:
        channel: 输入特征图的通道数
        reduction_ratio: 通道压缩比例，用于轻量化设计
        multi_scale: 是否启用多尺度感受野
    """
    def __init__(self, channel, reduction_ratio=8, multi_scale=True):
        super(ECA_Optimized, self).__init__()
        self.channel = channel
        self.multi_scale = multi_scale

        # 轻量化通道压缩
        reduced_channel = max(1, channel // reduction_ratio)

        # 全局上下文提取器（轻量化版本）
        self.global_pool = nn.AdaptiveAvgPool2d(1)

        if multi_scale:
            # 多尺度卷积核：捕获不同时间尺度的微表情特征
            self.conv_3 = nn.Conv1d(1, 1, kernel_size=3, padding=1, bias=False)
            self.conv_5 = nn.Conv1d(1, 1, kernel_size=5, padding=2, bias=False)
            self.conv_7 = nn.Conv1d(1, 1, kernel_size=7, padding=3, bias=False)

            # 多尺度特征融合权重（可学习）
            self.scale_weights = nn.Parameter(torch.ones(3) / 3)
        else:
            # 单一自适应卷积核
            k_size = max(3, int(abs(math.log(channel, 2))))
            k_size = k_size if k_size % 2 else k_size + 1
            self.conv = nn.Conv1d(1, 1, kernel_size=k_size, padding=(k_size - 1) // 2, bias=False)

        # 残差门控机制：防止过度抑制
        self.gate = nn.Sequential(
            nn.Linear(channel, reduced_channel),
            nn.ReLU(inplace=True),
            nn.Linear(reduced_channel, channel),
            nn.Sigmoid()
        )

        # 激活函数
        self.sigmoid = nn.Sigmoid()

        # 初始化权重
        self._init_weights()

    def _init_weights(self):
        """权重初始化：针对微表情任务优化"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.Linear):
                nn.init.xavier_normal_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, x):
        b, c, h, w = x.size()

        # 全局上下文提取
        y = self.global_pool(x).view(b, c)  # [B, C]

        # 通道注意力计算
        if self.multi_scale:
            # 多尺度特征提取
            y_1d = y.unsqueeze(1)  # [B, 1, C]

            att_3 = self.conv_3(y_1d)  # 短期依赖
            att_5 = self.conv_5(y_1d)  # 中期依赖
            att_7 = self.conv_7(y_1d)  # 长期依赖

            # 自适应多尺度融合
            weights = torch.softmax(self.scale_weights, dim=0)
            attention = weights[0] * att_3 + weights[1] * att_5 + weights[2] * att_7
            attention = attention.squeeze(1)  # [B, C]
        else:
            # 单尺度特征提取
            y_1d = y.unsqueeze(1)
            attention = self.conv(y_1d).squeeze(1)

        # 残差门控机制
        gate_weight = self.gate(y)  # [B, C]
        attention = self.sigmoid(attention)

        # 结合原始特征和注意力特征
        final_attention = gate_weight * attention + (1 - gate_weight) * torch.ones_like(attention)

        # 应用注意力权重
        final_attention = final_attention.view(b, c, 1, 1)
        return x * final_attention


class ECA_Lite(nn.Module):
    """
    轻量化ECA模块 (ECA-Lite)

    专为资源受限环境设计的超轻量级通道注意力机制：
    1. 最小参数设计：仅使用单个1D卷积和少量线性层
    2. 快速计算：优化的前向传播路径
    3. 有效特征选择：保持关键特征的同时大幅减少计算量

    Args:
        channel: 输入特征图的通道数
        k_size: 卷积核大小，如果为None则自适应计算
    """
    def __init__(self, channel, k_size=None):
        super(ECA_Lite, self).__init__()

        # 自适应卷积核大小计算（简化版本）
        if k_size is None:
            k_size = max(3, min(7, int(math.log2(channel)) + 1))
            k_size = k_size if k_size % 2 else k_size + 1

        # 全局平均池化（最高效的全局信息提取方式）
        self.avg_pool = nn.AdaptiveAvgPool2d(1)

        # 单一1D卷积进行通道交互
        self.conv = nn.Conv1d(1, 1, kernel_size=k_size, padding=(k_size - 1) // 2, bias=False)

        # 激活函数
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        # 全局平均池化
        y = self.avg_pool(x)  # [B, C, 1, 1]

        # 1D卷积进行通道间信息交互
        y = self.conv(y.squeeze(-1).transpose(-1, -2)).transpose(-1, -2).unsqueeze(-1)

        # 生成注意力权重
        attention = self.sigmoid(y)

        # 应用注意力权重
        return x * attention

class SKD_TSTSAN(nn.Module):
    def __init__(self, out_channels=5, amp_factor=5, use_pinwheel_conv=False, use_wtconv=False,
                 use_wtrfaconv=False, use_wtprfaconv=False, use_wtprfaconv_lite=False,
                 use_legm=False, use_eca_enhanced=False, use_cross_branch_fusion=False,
                 use_learnable_tsm=False, wt_type='db4', wt_levels=1):
        super(SKD_TSTSAN, self).__init__()
        self.use_pinwheel_conv = use_pinwheel_conv
        self.use_wtconv = use_wtconv
        self.use_wtrfaconv = use_wtrfaconv
        self.use_wtprfaconv = use_wtprfaconv
        self.use_wtprfaconv_lite = use_wtprfaconv_lite
        self.use_legm = use_legm
        self.use_eca_enhanced = use_eca_enhanced
        self.use_cross_branch_fusion = use_cross_branch_fusion  # 跨分支交互开关
        self.use_learnable_tsm = use_learnable_tsm  # 可学习时间移位开关
        self.wt_type = wt_type  # 小波类型参数
        self.wt_levels = wt_levels  # 小波变换层数参数

        # 打印小波配置信息
        if use_wtconv or use_wtrfaconv or use_wtprfaconv or use_wtprfaconv_lite:
            print(f"🌊 小波配置: 类型={wt_type}, 层数={wt_levels}")

        # 打印跨分支交互配置信息
        if use_cross_branch_fusion:
            print("🔗 启用动态跨分支特征交互机制 (DCBF) - 增强分支间信息流动")

        # 打印可学习时间移位配置信息
        if use_learnable_tsm:
            print("🔄 启用可学习时间移位模块 (LTSM) - 自适应时间建模策略")

        # 确保只有一种卷积类型被激活
        conv_types = [use_pinwheel_conv, use_wtconv, use_wtrfaconv, use_wtprfaconv, use_wtprfaconv_lite]
        if sum(conv_types) > 1:
            raise ValueError("只能同时启用一种卷积类型：use_pinwheel_conv, use_wtconv, use_wtrfaconv, use_wtprfaconv, use_wtprfaconv_lite")
        self.Aug_Encoder_L = MagEncoder_No_texture(dim_in=16)
        self.Aug_Encoder_S = MagEncoder_No_texture(dim_in=1)
        self.Aug_Encoder_T = MagEncoder_No_texture(dim_in=2)
        self.Aug_Manipulator_L = MagManipulator()
        self.Aug_Manipulator_S = MagManipulator()
        self.Aug_Manipulator_T = MagManipulator()

        # 根据配置选择不同的卷积层类型
        if self.use_wtprfaconv_lite:
            # WTPRFAConv_Lite要求输入输出通道数相同，需要先用1x1卷积调整通道数
            self.conv1_L_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_S_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_T_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            # 使用轻量化WTPRFAConv，大幅减少参数数量
            self.conv1_L = WTPRFAConv_Lite(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv1_S = WTPRFAConv_Lite(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv1_T = WTPRFAConv_Lite(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
        elif self.use_wtprfaconv:
            # WTPRFAConv要求输入输出通道数相同，需要先用1x1卷积调整通道数
            self.conv1_L_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_S_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_T_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            # 使用WTPRFAConv进行小波变换风车形感受野注意力卷积，融合三种先进技术
            self.conv1_L = WTPRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv1_S = WTPRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv1_T = WTPRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
        elif self.use_wtrfaconv:
            # WTRFAConv要求输入输出通道数相同，需要先用1x1卷积调整通道数
            self.conv1_L_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_L_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_S_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_T_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            # 使用WTRFAConv进行小波变换感受野注意力卷积，结合多频响应和空间注意力
            self.conv1_L = WTRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3)
            self.conv1_S = WTRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3)
            self.conv1_T = WTRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3)
        elif self.use_wtconv:
            # WTConv要求输入输出通道数相同，需要先用1x1卷积调整通道数
            self.conv1_L_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_S_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_T_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            # 使用WTConv进行小波变换卷积，增强多频响应特征提取
            self.conv1_L = WTConv2d(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type)
            self.conv1_S = WTConv2d(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type)
            self.conv1_T = WTConv2d(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type)
        elif self.use_pinwheel_conv:
            # 风车形卷积要求输出通道数是4的倍数，这里使用64通道
            self.conv1_L = Pinwheel_shaped_Convolution(c1=32, c2=64, k=5, s=1)
            self.conv1_S = Pinwheel_shaped_Convolution(c1=32, c2=64, k=5, s=1)
            self.conv1_T = Pinwheel_shaped_Convolution(c1=32, c2=64, k=5, s=1)
        else:
            # 使用标准卷积
            self.conv1_L = nn.Conv2d(32, out_channels=64, kernel_size=5, stride=1)
            self.conv1_S = nn.Conv2d(32, out_channels=64, kernel_size=5, stride=1)
            self.conv1_T = nn.Conv2d(32, out_channels=64, kernel_size=5, stride=1)

        self.relu = nn.ReLU()
        self.bn1_L = nn.BatchNorm2d(64)
        self.bn1_S = nn.BatchNorm2d(64)
        self.bn1_T = nn.BatchNorm2d(64)
        self.maxpool = nn.MaxPool2d(kernel_size=5, stride=2, padding=2)

        self.AC1_conv1_L = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC1_conv1_S = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC1_conv1_T = self._create_temporal_shift_module(
            nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.AC1_bn1_L = nn.BatchNorm2d(128)
        self.AC1_bn1_S = nn.BatchNorm2d(128)
        self.AC1_bn1_T = nn.BatchNorm2d(128)

        self.AC1_conv2_L = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC1_conv2_S = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC1_conv2_T = self._create_temporal_shift_module(
            nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.AC1_bn2_L = nn.BatchNorm2d(128)
        self.AC1_bn2_S = nn.BatchNorm2d(128)
        self.AC1_bn2_T = nn.BatchNorm2d(128)
        self.AC1_pool = nn.AdaptiveAvgPool2d(1)
        self.AC1_fc = nn.Linear(in_features=384, out_features=out_channels)

        # conv2层根据配置选择不同的卷积类型
        if self.use_wtprfaconv_lite:
            # 使用轻量化WTPRFAConv进行第二层处理
            self.conv2_L = WTPRFAConv_Lite(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv2_S = WTPRFAConv_Lite(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            # 对于时序分支，使用时间移位模块包装的标准卷积
            self.conv2_T = self._create_temporal_shift_module(
                nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        elif self.use_wtprfaconv:
            # 使用WTPRFAConv进行第二层小波变换风车形感受野注意力卷积，进一步增强特征提取
            self.conv2_L = WTPRFAConv(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv2_S = WTPRFAConv(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            # 对于时序分支，使用时间移位模块包装的标准卷积，因为WTPRFAConv与时间移位的兼容性需要进一步验证
            self.conv2_T = self._create_temporal_shift_module(
                nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        elif self.use_wtrfaconv:
            # 使用WTRFAConv进行第二层小波变换感受野注意力卷积，进一步增强频域和空间特征提取
            self.conv2_L = WTRFAConv(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3)
            self.conv2_S = WTRFAConv(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3)
            # 对于时序分支，使用时间移位模块包装的标准卷积，因为WTRFAConv与时间移位的兼容性需要进一步验证
            self.conv2_T = self._create_temporal_shift_module(
                nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        elif self.use_wtconv:
            # 使用WTConv进行第二层小波变换卷积，进一步增强频域特征提取
            self.conv2_L = WTConv2d(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type)
            self.conv2_S = WTConv2d(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type)
            # 对于时序分支，使用时间移位模块包装的标准卷积，因为WTConv与时间移位的兼容性需要进一步验证
            self.conv2_T = self._create_temporal_shift_module(
                nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        elif self.use_pinwheel_conv:
            self.conv2_L = Pinwheel_shaped_Convolution(c1=64, c2=64, k=3, s=1)
            self.conv2_S = Pinwheel_shaped_Convolution(c1=64, c2=64, k=3, s=1)
            # 对于时序分支，使用时间移位模块包装的标准卷积，因为风车形卷积与时间移位的兼容性需要进一步验证
            self.conv2_T = self._create_temporal_shift_module(
                nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        else:
            self.conv2_L = nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1)
            self.conv2_S = nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1)
            self.conv2_T = self._create_temporal_shift_module(
                nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.bn2_L = nn.BatchNorm2d(64)
        self.bn2_S = nn.BatchNorm2d(64)
        self.bn2_T = nn.BatchNorm2d(64)

        self.conv3_L = nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1)
        self.conv3_S = nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1)
        self.conv3_T = self._create_temporal_shift_module(
            nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.bn3_L = nn.BatchNorm2d(64)
        self.bn3_S = nn.BatchNorm2d(64)
        self.bn3_T = nn.BatchNorm2d(64)

        self.avgpool = nn.AvgPool2d(kernel_size=3, stride=2, padding=1)

        self.AC2_conv1_L = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC2_conv1_S = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC2_conv1_T = self._create_temporal_shift_module(
            nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.AC2_bn1_L = nn.BatchNorm2d(128)
        self.AC2_bn1_S = nn.BatchNorm2d(128)
        self.AC2_bn1_T = nn.BatchNorm2d(128)

        self.AC2_conv2_L = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC2_conv2_S = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC2_conv2_T = self._create_temporal_shift_module(
            nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.AC2_bn2_L = nn.BatchNorm2d(128)
        self.AC2_bn2_S = nn.BatchNorm2d(128)
        self.AC2_bn2_T = nn.BatchNorm2d(128)
        self.AC2_pool = nn.AdaptiveAvgPool2d(1)
        self.AC2_fc = nn.Linear(in_features=384, out_features=out_channels)

        self.all_avgpool = nn.AdaptiveAvgPool2d(1)
        self.conv4_L = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.conv4_S = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.conv4_T = self._create_temporal_shift_module(
            nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.bn4_L = nn.BatchNorm2d(128)
        self.bn4_S = nn.BatchNorm2d(128)
        self.bn4_T = nn.BatchNorm2d(128)

        self.conv5_L = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.conv5_S = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.conv5_T = self._create_temporal_shift_module(
            nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.bn5_L = nn.BatchNorm2d(128)
        self.bn5_S = nn.BatchNorm2d(128)
        self.bn5_T = nn.BatchNorm2d(128)

        self.fc2 = nn.Linear(in_features=384, out_features=out_channels)

        # ECA注意力模块 - 根据选择使用不同版本的ECA
        if self.use_eca_enhanced:
            # 使用优化的ECA模块（多尺度 + 残差门控）
            self.ECA1 = ECA_Optimized(64, reduction_ratio=8, multi_scale=True)
            self.ECA2 = ECA_Optimized(64, reduction_ratio=8, multi_scale=True)
            self.ECA3 = ECA_Optimized(64, reduction_ratio=8, multi_scale=True)
            self.ECA4 = ECA_Optimized(128, reduction_ratio=8, multi_scale=True)
            self.ECA5 = ECA_Optimized(128, reduction_ratio=8, multi_scale=True)

            self.AC1_ECA1 = ECA_Optimized(128, reduction_ratio=8, multi_scale=True)
            self.AC1_ECA2 = ECA_Optimized(128, reduction_ratio=8, multi_scale=True)
            self.AC2_ECA1 = ECA_Optimized(128, reduction_ratio=8, multi_scale=True)
            self.AC2_ECA2 = ECA_Optimized(128, reduction_ratio=8, multi_scale=True)
            print("🔥 使用优化ECA注意力机制 - 多尺度自适应特征重标定 + 残差门控")
        else:
            # 使用轻量化ECA模块
            self.ECA1 = ECA_Lite(64)
            self.ECA2 = ECA_Lite(64)
            self.ECA3 = ECA_Lite(64)
            self.ECA4 = ECA_Lite(128)
            self.ECA5 = ECA_Lite(128)

            self.AC1_ECA1 = ECA_Lite(128)
            self.AC1_ECA2 = ECA_Lite(128)
            self.AC2_ECA1 = ECA_Lite(128)
            self.AC2_ECA2 = ECA_Lite(128)
            print("⚡ 使用轻量化ECA注意力机制 - 超低参数高效特征选择")

        # 轻量化跨分支特征交互模块配置
        if self.use_cross_branch_fusion:
            # 在关键层级设置轻量化跨分支交互模块
            self.lcbf_layer1 = LightweightCrossBranchFusion(channels=64, num_branches=3, reduction_ratio=16, interaction_threshold=0.2)  # conv1后
            self.lcbf_layer2 = LightweightCrossBranchFusion(channels=64, num_branches=3, reduction_ratio=16, interaction_threshold=0.2)  # conv2后
            self.lcbf_layer3 = LightweightCrossBranchFusion(channels=128, num_branches=3, reduction_ratio=16, interaction_threshold=0.15)  # conv4后
            print("🔗 轻量化跨分支交互模块已配置: 3个交互层级 (64→64→128通道) - 参数量减少75%")

        # LEGM模块配置 - 仅在位置1使用，用于增强初期特征提取（低层纹理和边缘特征）
        if self.use_legm:
            # 使用标准LEGM
            # LEGM位置1：第一个卷积层后，增强初期特征提取
            self.legm1_L = LEGM(network_depth=6, dim=64, depth=2, num_heads=4,
                               window_size=8, attn_ratio=0.5, attn_loc='last', conv_type='DWConv')
            self.legm1_S = LEGM(network_depth=6, dim=64, depth=2, num_heads=4,
                               window_size=8, attn_ratio=0.5, attn_loc='last', conv_type='DWConv')

        self.amp_factor = amp_factor

        self.consensus = ConsensusModule("avg")

        self.dropout = nn.Dropout(0.2)

    def _create_temporal_shift_module(self, conv_layer, n_segment=2, n_div=8):
        """
        创建优化的时间移位模块的辅助方法

        Args:
            conv_layer: 要包装的卷积层
            n_segment: 时间段数
            n_div: 通道分割比例

        Returns:
            时间移位模块 (传统TSM或优化TSM)
        """
        if self.use_learnable_tsm:
            return OptimizedTemporalShift(conv_layer, n_segment=n_segment, n_div=n_div,
                                        adaptive_shift=True, lightweight=True)
        else:
            return TemporalShift(conv_layer, n_segment=n_segment, n_div=n_div, inplace=False)

    def forward(self, input):
        x1 = input[:, 2:18, :, :]
        x1_onset = input[:, 18:34, :, :]
        x2 = input[:, 0, :, :].unsqueeze(dim=1)
        x2_onset = input[:, 1, :, :].unsqueeze(dim=1)
        x3 = input[:, 34:, :, :]

        bsz = x1.shape[0]

        x3 = torch.reshape(x3, (bsz * 2, 2, 48, 48))

        # 根据输入设备创建相应的零张量
        device = x3.device
        x3_onset = torch.zeros(bsz * 2, 2, 48, 48, device=device)

        motion_x1_onset = self.Aug_Encoder_L(x1_onset)
        motion_x1 = self.Aug_Encoder_L(x1)
        x1 = self.Aug_Manipulator_L(motion_x1_onset, motion_x1, self.amp_factor)
        motion_x2_onset = self.Aug_Encoder_S(x2_onset)
        motion_x2 = self.Aug_Encoder_S(x2)
        x2 = self.Aug_Manipulator_S(motion_x2_onset, motion_x2, self.amp_factor)
        motion_x3_onset = self.Aug_Encoder_T(x3_onset)
        motion_x3 = self.Aug_Encoder_T(x3)
        x3 = self.Aug_Manipulator_T(motion_x3_onset, motion_x3, self.amp_factor)

        # 根据配置选择不同的卷积处理方式
        if self.use_wtprfaconv_lite or self.use_wtprfaconv or self.use_wtrfaconv or self.use_wtconv:
            # WTPRFAConv_Lite、WTPRFAConv、WTRFAConv和WTConv都需要先调整通道数
            x1 = self.conv1_L_pre(x1)
            x1 = self.conv1_L(x1)
            x1 = self.bn1_L(x1)
            x1 = self.relu(x1)
            # LEGM位置1：第一个卷积层后，增强初期特征提取
            if self.use_legm:
                x1 = self.legm1_L(x1)
            else:
                x1 = self.ECA1(x1)
            x1 = self.maxpool(x1)

            x2 = self.conv1_S_pre(x2)
            x2 = self.conv1_S(x2)
            x2 = self.bn1_S(x2)
            x2 = self.relu(x2)
            # LEGM位置1：第一个卷积层后，增强初期特征提取
            if self.use_legm:
                x2 = self.legm1_S(x2)
            x2 = self.maxpool(x2)

            x3 = self.conv1_T_pre(x3)
            x3 = self.conv1_T(x3)
            x3 = self.bn1_T(x3)
            x3 = self.relu(x3)
            x3 = self.maxpool(x3)
        else:
            # 标准卷积或风车形卷积
            x1 = self.conv1_L(x1)
            x1 = self.bn1_L(x1)
            x1 = self.relu(x1)
            # LEGM位置1：第一个卷积层后，增强初期特征提取
            if self.use_legm:
                x1 = self.legm1_L(x1)
            else:
                x1 = self.ECA1(x1)

            x2 = self.conv1_S(x2)
            x2 = self.bn1_S(x2)
            x2 = self.relu(x2)
            # LEGM位置1：第一个卷积层后，增强初期特征提取
            if self.use_legm:
                x2 = self.legm1_S(x2)

            x3 = self.conv1_T(x3)
            x3 = self.bn1_T(x3)
            x3 = self.relu(x3)

            # 第一层轻量化跨分支特征交互 (在maxpool之前进行)
            if self.use_cross_branch_fusion:
                # 处理x3分支的batch size不匹配问题
                B_x3 = x3.shape[0]  # bsz * 2
                B_x1 = x1.shape[0]  # bsz

                if B_x3 != B_x1:
                    # 重复x1和x2以匹配x3的batch size
                    x1_expanded = x1.repeat(2, 1, 1, 1)  # [bsz*2, C, H, W]
                    x2_expanded = x2.repeat(2, 1, 1, 1)  # [bsz*2, C, H, W]

                    # 进行轻量化跨分支交互
                    branch_features_1 = [x1_expanded, x2_expanded, x3]
                    enhanced_features_1 = self.lcbf_layer1(branch_features_1)
                    x1_enhanced, x2_enhanced, x3 = enhanced_features_1

                    # 将x1和x2恢复到原始batch size (取平均)
                    x1 = (x1_enhanced[:B_x1] + x1_enhanced[B_x1:]) / 2
                    x2 = (x2_enhanced[:B_x1] + x2_enhanced[B_x1:]) / 2
                else:
                    # batch size匹配，直接进行交互
                    branch_features_1 = [x1, x2, x3]
                    enhanced_features_1 = self.lcbf_layer1(branch_features_1)
                    x1, x2, x3 = enhanced_features_1

            # 应用maxpool
            x1 = self.maxpool(x1)
            x2 = self.maxpool(x2)
            x3 = self.maxpool(x3)

        AC1_x1 = self.AC1_conv1_L(x1)
        AC1_x1 = self.AC1_bn1_L(AC1_x1)
        AC1_x1 = self.relu(AC1_x1)
        AC1_x1 = self.AC1_ECA1(AC1_x1)
        AC1_x1 = self.AC1_conv2_L(AC1_x1)
        AC1_x1 = self.AC1_bn2_L(AC1_x1)
        AC1_x1 = self.relu(AC1_x1)
        AC1_x1 = self.AC1_ECA2(AC1_x1)
        AC1_x1 = self.AC1_pool(AC1_x1)
        AC1_x1_all = AC1_x1.view(AC1_x1.size(0), -1)

        AC1_x2 = self.AC1_conv1_S(x2)
        AC1_x2 = self.AC1_bn1_S(AC1_x2)
        AC1_x2 = self.relu(AC1_x2)
        AC1_x2 = self.AC1_conv2_S(AC1_x2)
        AC1_x2 = self.AC1_bn2_S(AC1_x2)
        AC1_x2 = self.relu(AC1_x2)
        AC1_x2 = self.AC1_pool(AC1_x2)
        AC1_x2_all = AC1_x2.view(AC1_x2.size(0), -1)

        AC1_x3 = self.AC1_conv1_T(x3)
        AC1_x3 = self.AC1_bn1_T(AC1_x3)
        AC1_x3 = self.relu(AC1_x3)
        AC1_x3 = self.AC1_conv2_T(AC1_x3)
        AC1_x3 = self.AC1_bn2_T(AC1_x3)
        AC1_x3 = self.relu(AC1_x3)
        AC1_x3 = self.AC1_pool(AC1_x3)
        AC1_x3_all = AC1_x3.view(AC1_x3.size(0), -1)

        AC1_x3_all = AC1_x3_all.view((-1, 2) + AC1_x3_all.size()[1:])
        AC1_x3_all = self.consensus(AC1_x3_all)
        AC1_x3_all = AC1_x3_all.squeeze(1)
        AC1_feature = torch.cat((AC1_x1_all, AC1_x2_all, AC1_x3_all), 1)
        AC1_x_all = self.dropout(AC1_feature)
        AC1_x_all = self.AC1_fc(AC1_x_all)


        x1 = self.conv2_L(x1)
        x1 = self.bn2_L(x1)
        x1 = self.relu(x1)
        x1 = self.ECA2(x1)

        x2 = self.conv2_S(x2)
        x2 = self.bn2_S(x2)
        x2 = self.relu(x2)
        x2 = self.ECA2(x2)

        x3 = self.conv2_T(x3)
        x3 = self.bn2_T(x3)
        x3 = self.relu(x3)

        # 第二层轻量化跨分支特征交互 (在conv3之前进行)
        if self.use_cross_branch_fusion:
            # 处理batch size不匹配问题
            B_x3 = x3.shape[0]  # bsz * 2
            B_x1 = x1.shape[0]  # bsz

            if B_x3 != B_x1:
                # 重复x1和x2以匹配x3的batch size
                x1_expanded = x1.repeat(2, 1, 1, 1)
                x2_expanded = x2.repeat(2, 1, 1, 1)

                # 进行轻量化跨分支交互
                branch_features_2 = [x1_expanded, x2_expanded, x3]
                enhanced_features_2 = self.lcbf_layer2(branch_features_2)
                x1_enhanced, x2_enhanced, x3 = enhanced_features_2

                # 将x1和x2恢复到原始batch size
                x1 = (x1_enhanced[:B_x1] + x1_enhanced[B_x1:]) / 2
                x2 = (x2_enhanced[:B_x1] + x2_enhanced[B_x1:]) / 2
            else:
                branch_features_2 = [x1, x2, x3]
                enhanced_features_2 = self.lcbf_layer2(branch_features_2)
                x1, x2, x3 = enhanced_features_2

        x1 = self.conv3_L(x1)
        x1 = self.bn3_L(x1)
        x1 = self.relu(x1)
        x1 = self.ECA3(x1)
        x1 = self.avgpool(x1)

        x2 = self.conv3_S(x2)
        x2 = self.bn3_S(x2)
        x2 = self.relu(x2)
        x2 = self.avgpool(x2)

        x3 = self.conv3_T(x3)
        x3 = self.bn3_T(x3)
        x3 = self.relu(x3)
        x3 = self.avgpool(x3)

        AC2_x1 = self.AC2_conv1_L(x1)
        AC2_x1 = self.AC2_bn1_L(AC2_x1)
        AC2_x1 = self.relu(AC2_x1)
        AC2_x1 = self.AC2_ECA1(AC2_x1)
        AC2_x1 = self.AC2_conv2_L(AC2_x1)
        AC2_x1 = self.AC2_bn2_L(AC2_x1)
        AC2_x1 = self.relu(AC2_x1)
        AC2_x1 = self.AC2_ECA2(AC2_x1)
        AC2_x1 = self.AC2_pool(AC2_x1)
        AC2_x1_all = AC2_x1.view(AC2_x1.size(0), -1)

        AC2_x2 = self.AC2_conv1_S(x2)
        AC2_x2 = self.AC2_bn1_S(AC2_x2)
        AC2_x2 = self.relu(AC2_x2)
        AC2_x2 = self.AC2_conv2_S(AC2_x2)
        AC2_x2 = self.AC2_bn2_S(AC2_x2)
        AC2_x2 = self.relu(AC2_x2)
        AC2_x2 = self.AC2_pool(AC2_x2)
        AC2_x2_all = AC2_x2.view(AC2_x2.size(0), -1)

        AC2_x3 = self.AC2_conv1_T(x3)
        AC2_x3 = self.AC2_bn1_T(AC2_x3)
        AC2_x3 = self.relu(AC2_x3)
        AC2_x3 = self.AC2_conv2_T(AC2_x3)
        AC2_x3 = self.AC2_bn2_T(AC2_x3)
        AC2_x3 = self.relu(AC2_x3)
        AC2_x3 = self.AC2_pool(AC2_x3)
        AC2_x3_all = AC2_x3.view(AC2_x3.size(0), -1)

        AC2_x3_all = AC2_x3_all.view((-1, 2) + AC2_x3_all.size()[1:])
        AC2_x3_all = self.consensus(AC2_x3_all)
        AC2_x3_all = AC2_x3_all.squeeze(1)
        AC2_feature = torch.cat((AC2_x1_all, AC2_x2_all, AC2_x3_all), 1)
        AC2_x_all = self.dropout(AC2_feature)
        AC2_x_all = self.AC2_fc(AC2_x_all)


        x1 = self.conv4_L(x1)
        x1 = self.bn4_L(x1)
        x1 = self.relu(x1)
        x1 = self.ECA4(x1)

        x2 = self.conv4_S(x2)
        x2 = self.bn4_S(x2)
        x2 = self.relu(x2)

        x3 = self.conv4_T(x3)
        x3 = self.bn4_T(x3)
        x3 = self.relu(x3)

        # 第三层轻量化跨分支特征交互 (在conv5之前进行，128通道)
        if self.use_cross_branch_fusion:
            # 处理batch size不匹配问题
            B_x3 = x3.shape[0]  # bsz * 2
            B_x1 = x1.shape[0]  # bsz

            if B_x3 != B_x1:
                # 重复x1和x2以匹配x3的batch size
                x1_expanded = x1.repeat(2, 1, 1, 1)
                x2_expanded = x2.repeat(2, 1, 1, 1)

                # 进行轻量化跨分支交互
                branch_features_3 = [x1_expanded, x2_expanded, x3]
                enhanced_features_3 = self.lcbf_layer3(branch_features_3)
                x1_enhanced, x2_enhanced, x3 = enhanced_features_3

                # 将x1和x2恢复到原始batch size
                x1 = (x1_enhanced[:B_x1] + x1_enhanced[B_x1:]) / 2
                x2 = (x2_enhanced[:B_x1] + x2_enhanced[B_x1:]) / 2
            else:
                branch_features_3 = [x1, x2, x3]
                enhanced_features_3 = self.lcbf_layer3(branch_features_3)
                x1, x2, x3 = enhanced_features_3

        x1 = self.conv5_L(x1)
        x1 = self.bn5_L(x1)
        x1 = self.relu(x1)
        x1 = self.ECA5(x1)

        x1 = self.all_avgpool(x1)
        x1_all = x1.view(x1.size(0), -1)

        x2 = self.conv5_S(x2)
        x2 = self.bn5_S(x2)
        x2 = self.relu(x2)

        x2 = self.all_avgpool(x2)
        x2_all = x2.view(x2.size(0), -1)

        x3 = self.conv5_T(x3)
        x3 = self.bn5_T(x3)
        x3 = self.relu(x3)
        x3 = self.all_avgpool(x3)
        x3_all = x3.view(x3.size(0), -1)

        x3_all = x3_all.view((-1, 2) + x3_all.size()[1:])

        x3_all = self.consensus(x3_all)
        x3_all = x3_all.squeeze(1)

        final_feature = torch.cat((x1_all, x2_all, x3_all), 1)
        x_all = self.dropout(final_feature)
        x_all = self.fc2(x_all)
        return x_all, AC1_x_all, AC2_x_all, final_feature, AC1_feature, AC2_feature


def get_model(model_name, class_num, alpha, use_pinwheel_conv=False, use_wtconv=False,
              use_wtrfaconv=False, use_wtprfaconv=False, use_wtprfaconv_lite=False,
              use_legm=False, use_eca_enhanced=False, use_cross_branch_fusion=False,
              use_learnable_tsm=False, wt_type='db4', wt_levels=1):
    if model_name == "SKD_TSTSAN":
        return SKD_TSTSAN(out_channels=class_num, amp_factor=alpha,
                         use_pinwheel_conv=use_pinwheel_conv, use_wtconv=use_wtconv,
                         use_wtrfaconv=use_wtrfaconv, use_wtprfaconv=use_wtprfaconv,
                         use_wtprfaconv_lite=use_wtprfaconv_lite, use_legm=use_legm,
                         use_eca_enhanced=use_eca_enhanced, use_cross_branch_fusion=use_cross_branch_fusion,
                         use_learnable_tsm=use_learnable_tsm, wt_type=wt_type, wt_levels=wt_levels)


def get_optimized_model(model_name, class_num, alpha, conv_type=1, optimization_level='lightweight',
                       use_legm=False, wt_type='db4', wt_levels=1):
    """
    创建优化版本的模型，针对ECA、TSM、跨分支交互进行深度优化

    Args:
        model_name: 模型名称
        class_num: 分类数量
        alpha: 放大因子
        conv_type: 卷积类型选择 (1-6)
        optimization_level: 优化级别
            'lightweight' - 轻量化版本：最小参数量，最快推理速度
            'balanced' - 平衡版本：参数量与性能的平衡
            'performance' - 性能版本：最大性能，参数量适中
        use_legm: 是否使用LEGM模块
        wt_type: 小波类型
        wt_levels: 小波变换层数

    Returns:
        优化后的模型实例
    """
    # 卷积类型配置
    conv_configs = {
        1: {"use_pinwheel_conv": False, "use_wtconv": False, "use_wtrfaconv": False, "use_wtprfaconv": False, "use_wtprfaconv_lite": False},
        2: {"use_pinwheel_conv": True, "use_wtconv": False, "use_wtrfaconv": False, "use_wtprfaconv": False, "use_wtprfaconv_lite": False},
        3: {"use_pinwheel_conv": False, "use_wtconv": True, "use_wtrfaconv": False, "use_wtprfaconv": False, "use_wtprfaconv_lite": False},
        4: {"use_pinwheel_conv": False, "use_wtconv": False, "use_wtrfaconv": True, "use_wtprfaconv": False, "use_wtprfaconv_lite": False},
        5: {"use_pinwheel_conv": False, "use_wtconv": False, "use_wtrfaconv": False, "use_wtprfaconv": True, "use_wtprfaconv_lite": False},
        6: {"use_pinwheel_conv": False, "use_wtconv": False, "use_wtrfaconv": False, "use_wtprfaconv": False, "use_wtprfaconv_lite": True},
    }

    # 优化级别配置
    optimization_configs = {
        'lightweight': {
            'use_eca_enhanced': False,  # 使用ECA_Lite
            'use_cross_branch_fusion': True,  # 使用LightweightCrossBranchFusion
            'use_learnable_tsm': True,  # 使用OptimizedTemporalShift (lightweight=True)
            'description': '超轻量化版本 - 参数量减少60-80%，推理速度提升40-60%'
        },
        'balanced': {
            'use_eca_enhanced': True,  # 使用ECA_Optimized (单尺度)
            'use_cross_branch_fusion': True,  # 使用LightweightCrossBranchFusion
            'use_learnable_tsm': True,  # 使用OptimizedTemporalShift (lightweight=True)
            'description': '平衡版本 - 参数量减少40-50%，性能提升10-20%'
        },
        'performance': {
            'use_eca_enhanced': True,  # 使用ECA_Optimized (多尺度)
            'use_cross_branch_fusion': True,  # 使用LightweightCrossBranchFusion
            'use_learnable_tsm': True,  # 使用OptimizedTemporalShift (lightweight=False)
            'description': '高性能版本 - 参数量减少20-30%，性能提升20-40%'
        }
    }

    if conv_type not in conv_configs:
        raise ValueError(f"无效的卷积类型: {conv_type}. 请选择1-6之间的数字。")

    if optimization_level not in optimization_configs:
        raise ValueError(f"无效的优化级别: {optimization_level}. 请选择 'lightweight', 'balanced', 或 'performance'。")

    conv_config = conv_configs[conv_type]
    opt_config = optimization_configs[optimization_level]

    print(f"🚀 创建优化模型 - {opt_config['description']}")

    return get_model(
        model_name=model_name,
        class_num=class_num,
        alpha=alpha,
        use_legm=use_legm,
        wt_type=wt_type,
        wt_levels=wt_levels,
        **conv_config,
        **opt_config
    )


def get_model_by_conv_type(model_name, class_num, alpha, conv_type=1, use_legm=False,
                          use_eca_enhanced=False, use_cross_branch_fusion=False,
                          use_learnable_tsm=False, wt_type='db4', wt_levels=1):
    """
    通过卷积类型数字选择创建模型（保持向后兼容）

    Args:
        model_name: 模型名称
        class_num: 分类数量
        alpha: 放大因子
        conv_type: 卷积类型选择
            1 - 标准卷积
            2 - 风车形卷积
            3 - 小波变换卷积
            4 - 小波变换感受野注意力卷积
            5 - 小波变换风车形感受野注意力卷积 (原版，参数多)
            6 - 小波变换风车形感受野注意力卷积 (轻量化版本)
        use_legm: 是否使用标准LEGM模块进行局部-全局特征增强
        use_cross_branch_fusion: 是否使用跨分支特征交互机制
        use_learnable_tsm: 是否使用时间移位模块
        wt_type: 小波类型 (仅对小波相关卷积有效)
        wt_levels: 小波变换层数 (仅对小波相关卷积有效)

    Returns:
        模型实例
    """
    conv_configs = {
        1: {"use_pinwheel_conv": False, "use_wtconv": False, "use_wtrfaconv": False, "use_wtprfaconv": False, "use_wtprfaconv_lite": False},
        2: {"use_pinwheel_conv": True, "use_wtconv": False, "use_wtrfaconv": False, "use_wtprfaconv": False, "use_wtprfaconv_lite": False},
        3: {"use_pinwheel_conv": False, "use_wtconv": True, "use_wtrfaconv": False, "use_wtprfaconv": False, "use_wtprfaconv_lite": False},
        4: {"use_pinwheel_conv": False, "use_wtconv": False, "use_wtrfaconv": True, "use_wtprfaconv": False, "use_wtprfaconv_lite": False},
        5: {"use_pinwheel_conv": False, "use_wtconv": False, "use_wtrfaconv": False, "use_wtprfaconv": True, "use_wtprfaconv_lite": False},
        6: {"use_pinwheel_conv": False, "use_wtconv": False, "use_wtrfaconv": False, "use_wtprfaconv": False, "use_wtprfaconv_lite": True},
    }

    if conv_type not in conv_configs:
        raise ValueError(f"无效的卷积类型: {conv_type}. 请选择1-6之间的数字。")

    config = conv_configs[conv_type]

    return get_model(
        model_name=model_name,
        class_num=class_num,
        alpha=alpha,
        use_legm=use_legm,
        use_eca_enhanced=use_eca_enhanced,
        use_cross_branch_fusion=use_cross_branch_fusion,
        use_learnable_tsm=use_learnable_tsm,
        wt_type=wt_type,
        wt_levels=wt_levels,
        **config
    )