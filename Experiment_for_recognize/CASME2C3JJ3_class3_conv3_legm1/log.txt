日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (3分类)[0m
[1;96m🎯 类别权重: ['0.3794', '0.1349', '0.4857'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub17测试标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 953 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6470[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.1935[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.1935[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 2/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.8939[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.2258[0m | [1m最佳准确率:[0m [1;93m0.1935[0m
[1;92m💾 保存最佳模型，准确率: 0.2258[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 3/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9636[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.5484[0m | [1m最佳准确率:[0m [1;93m0.2258[0m
[1;92m💾 保存最佳模型，准确率: 0.5484[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 4/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9894[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.6129[0m | [1m最佳准确率:[0m [1;93m0.5484[0m
[1;92m💾 保存最佳模型，准确率: 0.6129[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 5/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9909[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.8387[0m | [1m最佳准确率:[0m [1;93m0.6129[0m
[1;92m💾 保存最佳模型，准确率: 0.8387[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 6/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 7/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6774[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 8/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 9/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 10/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 11/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 12/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 13/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.8387[0m
[1;92m💾 保存最佳模型，准确率: 0.8387[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 14/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 15/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 16/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 17/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 18/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5806[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 19/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 20/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.8387[0m
[1;92m💾 保存最佳模型，准确率: 0.8387[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 21/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 22/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9924[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 23/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9773[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 24/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9939[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 25/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.8387[0m
[1;92m💾 保存最佳模型，准确率: 0.8387[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 26/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.8710[0m | [1m最佳准确率:[0m [1;93m0.8387[0m
[1;92m💾 保存最佳模型，准确率: 0.8710[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 27/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.9032[0m | [1m最佳准确率:[0m [1;93m0.8710[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 28/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 29/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 30/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 31/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 32/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 33/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 34/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 35/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 36/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9909[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 37/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9879[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 38/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9909[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 39/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 40/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 41/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 42/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 43/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 44/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 45/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 46/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 47/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 48/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 49/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 50/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9032[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 51/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 52/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 53/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 54/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 55/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9909[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 56/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 57/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9939[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6774[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 58/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9894[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 59/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 60/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 61/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 62/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 63/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 64/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 65/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 66/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 67/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 68/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 69/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 70/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 71/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 72/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9909[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 73/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9864[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 74/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9803[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 75/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9848[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 76/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 77/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 78/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 79/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 80/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 81/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9032[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 82/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 83/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 84/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 85/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 86/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9894[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 87/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 88/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 89/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 90/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 91/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 92/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9032[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 93/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 94/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 95/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 96/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 97/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9032[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 98/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6452[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 99/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 100/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 101/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 102/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 103/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 104/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 105/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 106/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 107/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 108/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 109/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6452[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 110/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9032[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 111/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9032[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 112/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 113/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 114/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 115/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5806[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 116/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9864[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 117/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 118/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 119/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 120/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9032[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 121/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 122/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 123/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 124/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 125/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 126/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 127/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 128/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9939[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6774[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 129/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9939[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5484[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 130/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9909[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5806[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 131/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 132/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 133/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 134/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 135/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 136/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9032[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 137/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 138/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 139/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 140/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 141/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 142/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9032[0m
[1;92m💾 保存最佳模型，准确率: 0.9355[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 143/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 144/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 145/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 146/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 147/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 148/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 149/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 150/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6774[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 151/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 152/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 153/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 154/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 155/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 156/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 157/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6129[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 158/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 159/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6774[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 160/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 161/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9894[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5806[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 162/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9894[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6129[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 163/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9355[0m
[1;92m💾 保存最佳模型，准确率: 0.9355[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 164/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 165/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 166/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 167/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 168/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 169/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 170/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 171/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 172/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 173/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 174/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 175/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 176/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 177/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 178/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 179/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 180/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 181/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 182/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 183/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 184/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 185/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9355[0m
[1;92m💾 保存最佳模型，准确率: 0.9355[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 186/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 187/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 188/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 189/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 190/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 191/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 192/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 193/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 194/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 195/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9355[0m
[1;92m💾 保存最佳模型，准确率: 0.9355[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 196/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 197/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 198/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6452[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 199/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5806[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 200/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 201/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 202/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 203/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6774[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 204/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 205/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 206/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 207/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 208/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 209/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9355[0m
[1;92m💾 保存最佳模型，准确率: 0.9355[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 210/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 211/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 212/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 213/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 214/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 215/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5161[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 216/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 217/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 218/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.9677[0m | [1m最佳准确率:[0m [1;93m0.9355[0m
[1;92m💾 保存最佳模型，准确率: 0.9677[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 219/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6129[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 220/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 221/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 222/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 223/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 224/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 225/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 226/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 227/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 228/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 229/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 230/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 231/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 232/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9924[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 233/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6774[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 234/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 235/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 236/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 237/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 238/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 239/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 240/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 241/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 242/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6452[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 243/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 244/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 245/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 246/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 247/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 248/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 249/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 250/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 251/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 252/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 253/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 254/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6452[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 255/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6452[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 256/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 257/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 258/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 259/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 260/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 261/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 262/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 263/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 264/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9924[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 265/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 266/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 267/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 268/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 269/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 270/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 271/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 272/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 273/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 274/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9677[0m | [1m最佳准确率:[0m [1;93m0.9677[0m
[1;92m💾 保存最佳模型，准确率: 0.9677[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 275/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 276/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 277/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 278/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 279/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 280/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 281/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 282/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6129[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 283/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6774[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 284/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 285/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 286/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 287/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 288/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 289/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9833[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 290/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 291/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 292/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 293/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9677[0m | [1m最佳准确率:[0m [1;93m0.9677[0m
[1;92m💾 保存最佳模型，准确率: 0.9677[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 294/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 295/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 296/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 297/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 298/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9677[0m | [1m最佳准确率:[0m [1;93m0.9677[0m
[1;92m💾 保存最佳模型，准确率: 0.9677[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 299/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 300/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 301/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 302/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 303/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 304/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 305/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 306/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 307/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 308/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 309/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 310/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 311/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 312/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 313/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 314/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 315/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9677[0m | [1m最佳准确率:[0m [1;93m0.9677[0m
[1;92m💾 保存最佳模型，准确率: 0.9677[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 316/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 317/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 318/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 319/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 320/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9677[0m | [1m最佳准确率:[0m [1;93m0.9677[0m
[1;92m💾 保存最佳模型，准确率: 0.9677[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth

[1m[1;96m📅 Epoch 321/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 322/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 323/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 324/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5806[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 325/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 326/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 327/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.9677[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub17/sub17.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
真实标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 7
TP: 7, FP: 0, FN: 0, TN: 24
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 23
TP: 23, FP: 0, FN: 0, TN: 8
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 30
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 7
TP: 7, FP: 0, FN: 0, TN: 24
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 23
TP: 23, FP: 0, FN: 0, TN: 8
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 30
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 200 个样本
标签 1: 增强了 130 个样本
标签 2: 增强了 216 个样本

========================================
【当前处理受试者】: sub05
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub05测试标签: [2, 2, 2, 2, 2, 0, 1]

【测试数据镜像训练】为 sub05 添加测试数据的镜像版本到训练集
  ✓ 成功添加 62 个镜像训练样本
  ✓ 训练集大小: 740 → 802
  ✓ 光流数据已正确处理 (u分量取反，v分量保持)
创建受试者目录: sub05
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub05/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub05/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6683[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.8571[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.8571[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub05/sub05.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub05/sub05.pth

[1m[1;96m📅 Epoch 2/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.8903[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8571[0m | [1m最佳准确率:[0m [1;93m0.8571[0m
[1;92m💾 保存最佳模型，准确率: 0.8571[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub05/sub05.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub05/sub05.pth

[1m[1;96m📅 Epoch 3/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9302[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8571[0m | [1m最佳准确率:[0m [1;93m0.8571[0m
[1;92m💾 保存最佳模型，准确率: 0.8571[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub05/sub05.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub05/sub05.pth

[1m[1;96m📅 Epoch 4/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9676[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8571[0m | [1m最佳准确率:[0m [1;93m0.8571[0m
[1;92m💾 保存最佳模型，准确率: 0.8571[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub05/sub05.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub05/sub05.pth

[1m[1;96m📅 Epoch 5/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9888[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8571[0m | [1m最佳准确率:[0m [1;93m0.8571[0m
[1;92m💾 保存最佳模型，准确率: 0.8571[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub05/sub05.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub05/sub05.pth

[1m[1;96m📅 Epoch 6/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9913[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.8571[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub05/sub05.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub05/sub05.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [2, 2, 2, 2, 2, 0, 1]
真实标签: [2, 2, 2, 2, 2, 0, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 6
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 6
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 6
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 6
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 448 个样本
标签 1: 增强了 304 个样本
标签 2: 增强了 396 个样本

========================================
【当前处理受试者】: sub26
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub26测试标签: [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1]
创建受试者目录: sub26
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub26/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub26/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 834 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6816[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.0909[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.0909[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub26/sub26.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub26/sub26.pth

[1m[1;96m📅 Epoch 2/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9128[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.8182[0m | [1m最佳准确率:[0m [1;93m0.0909[0m
[1;92m💾 保存最佳模型，准确率: 0.8182[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub26/sub26.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub26/sub26.pth

[1m[1;96m📅 Epoch 3/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9683[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.8182[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub26/sub26.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub26/sub26.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1]
真实标签: [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 9
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 9
TP: 9, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 9
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 9
TP: 9, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 688 个样本
标签 1: 增强了 462 个样本
标签 2: 增强了 621 个样本

========================================
【当前处理受试者】: sub19
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub19测试标签: [2, 2, 2, 2, 2, 0, 0, 0, 1, 1, 1]
创建受试者目录: sub19
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 870 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6453[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.6364[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.6364[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/sub19.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/sub19.pth

[1m[1;96m📅 Epoch 2/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9134[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.4545[0m | [1m最佳准确率:[0m [1;93m0.6364[0m

[1m[1;96m📅 Epoch 3/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9763[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.2727[0m | [1m最佳准确率:[0m [1;93m0.6364[0m

[1m[1;96m📅 Epoch 4/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9944[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6364[0m | [1m最佳准确率:[0m [1;93m0.6364[0m
[1;92m💾 保存最佳模型，准确率: 0.6364[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/sub19.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/sub19.pth

[1m[1;96m📅 Epoch 5/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9902[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6364[0m | [1m最佳准确率:[0m [1;93m0.6364[0m
[1;92m💾 保存最佳模型，准确率: 0.6364[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/sub19.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/sub19.pth

[1m[1;96m📅 Epoch 6/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9804[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.3636[0m | [1m最佳准确率:[0m [1;93m0.6364[0m

[1m[1;96m📅 Epoch 7/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9986[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.4545[0m | [1m最佳准确率:[0m [1;93m0.6364[0m

[1m[1;96m📅 Epoch 8/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9944[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.4545[0m | [1m最佳准确率:[0m [1;93m0.6364[0m

[1m[1;96m📅 Epoch 9/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.7273[0m | [1m最佳准确率:[0m [1;93m0.6364[0m
[1;92m💾 保存最佳模型，准确率: 0.7273[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/sub19.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/sub19.pth

[1m[1;96m📅 Epoch 10/870[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6364[0m | [1m最佳准确率:[0m [1;93m0.7273[0m

[1m[1;96m📅 Epoch 11/870[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.8182[0m | [1m最佳准确率:[0m [1;93m0.7273[0m
[1;92m💾 保存最佳模型，准确率: 0.8182[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/sub19.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/sub19.pth

[1m[1;96m📅 Epoch 12/870[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8182[0m | [1m最佳准确率:[0m [1;93m0.8182[0m
[1;92m💾 保存最佳模型，准确率: 0.8182[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/sub19.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/sub19.pth

[1m[1;96m📅 Epoch 13/870[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9972[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8182[0m | [1m最佳准确率:[0m [1;93m0.8182[0m
[1;92m💾 保存最佳模型，准确率: 0.8182[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/sub19.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/sub19.pth

[1m[1;96m📅 Epoch 14/870[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9930[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8182[0m | [1m最佳准确率:[0m [1;93m0.8182[0m
[1;92m💾 保存最佳模型，准确率: 0.8182[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/sub19.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/sub19.pth

[1m[1;96m📅 Epoch 15/870[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9944[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8182[0m | [1m最佳准确率:[0m [1;93m0.8182[0m
[1;92m💾 保存最佳模型，准确率: 0.8182[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/sub19.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/sub19.pth

[1m[1;96m📅 Epoch 16/870[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9958[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6364[0m | [1m最佳准确率:[0m [1;93m0.8182[0m

[1m[1;96m📅 Epoch 17/870[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9958[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7273[0m | [1m最佳准确率:[0m [1;93m0.8182[0m

[1m[1;96m📅 Epoch 18/870[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9986[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.8182[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/sub19.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub19/sub19.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [2, 2, 2, 2, 2, 0, 0, 0, 1, 1, 1]
真实标签: [2, 2, 2, 2, 2, 0, 0, 0, 1, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 3
TP: 3, FP: 0, FN: 0, TN: 8
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 3
TP: 3, FP: 0, FN: 0, TN: 8
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 6
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 3
TP: 3, FP: 0, FN: 0, TN: 8
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 3
TP: 3, FP: 0, FN: 0, TN: 8
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 6
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 920 个样本
标签 1: 增强了 632 个样本
标签 2: 增强了 801 个样本

========================================
【当前处理受试者】: sub09
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub09测试标签: [0, 0, 0, 0, 0, 1, 1, 1, 1, 1]
创建受试者目录: sub09
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 834 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6806[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.5000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.5000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 2/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9030[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.5000[0m
[1;92m💾 保存最佳模型，准确率: 0.5000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 3/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9744[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.5000[0m
[1;92m💾 保存最佳模型，准确率: 0.5000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 4/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9757[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.6000[0m | [1m最佳准确率:[0m [1;93m0.5000[0m
[1;92m💾 保存最佳模型，准确率: 0.6000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 5/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9946[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.7000[0m | [1m最佳准确率:[0m [1;93m0.6000[0m
[1;92m💾 保存最佳模型，准确率: 0.7000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 6/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.7000[0m

[1m[1;96m📅 Epoch 7/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.7000[0m
[1;92m💾 保存最佳模型，准确率: 0.7000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 8/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9879[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.9000[0m | [1m最佳准确率:[0m [1;93m0.7000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 9/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9650[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 10/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 11/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 12/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 13/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 14/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9960[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 15/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9919[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 16/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9960[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 17/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9515[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 18/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9919[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 19/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 20/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 21/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 22/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 23/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 24/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 25/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9717[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 26/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9892[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 27/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9852[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 28/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9919[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 29/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9838[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 30/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9569[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 31/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9528[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 32/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 33/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 34/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 35/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 36/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 37/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 38/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 39/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 40/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9811[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 41/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 42/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9946[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 43/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 44/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 45/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 46/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 47/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 48/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9838[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 49/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9933[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 50/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9960[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 51/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 52/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 53/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9838[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 54/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9946[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 55/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9946[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 56/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 57/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 58/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9919[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 59/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 60/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 61/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 62/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 63/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 64/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 65/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9946[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 66/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9919[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 67/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 68/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 69/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9906[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 70/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9933[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 71/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 72/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 73/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 74/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9838[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 75/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 76/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 77/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 78/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 79/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 80/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 81/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 82/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9906[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 83/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 84/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 85/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 86/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 87/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 88/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9906[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 89/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 90/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 91/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 92/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9960[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 93/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 94/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 95/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 96/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 97/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 98/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 99/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 100/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 101/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9960[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 102/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 103/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 104/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 105/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 106/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 107/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 108/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 109/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9811[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 110/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 111/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 112/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 113/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 114/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 115/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 116/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 117/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 118/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 119/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9960[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 120/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 121/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 122/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 123/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 124/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9798[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 125/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9960[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 126/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9852[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 127/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 128/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 129/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 130/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 131/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 132/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 133/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 134/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9946[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 135/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 136/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 137/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9892[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 138/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 139/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 140/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 141/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 142/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 143/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 144/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 145/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 146/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 147/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 148/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9933[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 149/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9933[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 150/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 151/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 152/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 153/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 154/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 155/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9892[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 156/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 157/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 158/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9838[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 159/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9933[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 160/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9784[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 161/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9933[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 162/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 163/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 164/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 165/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 166/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9946[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 167/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9919[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 168/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 169/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 170/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 171/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 172/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 173/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 174/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 175/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 176/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 177/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 178/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 179/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 180/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 181/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9960[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 182/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 183/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 184/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 185/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 186/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 187/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 188/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9906[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 189/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 190/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9933[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 191/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9717[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 192/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9933[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 193/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 194/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 195/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 196/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 197/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 198/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 199/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9946[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 200/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 201/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 202/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 203/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 204/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 205/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 206/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 207/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 208/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 209/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 210/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 211/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 212/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 213/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 214/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 215/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 216/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 217/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 218/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 219/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 220/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 221/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 222/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 223/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 224/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9933[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 225/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9960[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 226/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 227/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 228/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 229/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 230/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 231/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 232/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 233/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 234/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 235/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 236/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9960[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 237/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 238/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 239/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 240/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 241/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 242/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 243/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 244/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9960[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 245/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 246/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 247/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 248/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 249/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9946[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 250/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9933[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 251/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 252/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 253/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 254/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth

[1m[1;96m📅 Epoch 255/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 256/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 257/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 258/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 259/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 260/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 261/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 262/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 263/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 264/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 265/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9960[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 266/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 267/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 268/834[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub09/sub09.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 0, 1, 1, 1, 1, 1]
真实标签: [0, 0, 0, 0, 0, 1, 1, 1, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 5
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 5
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 5
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 5
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 1136 个样本
标签 1: 增强了 798 个样本
标签 2: 增强了 1026 个样本

========================================
【当前处理受试者】: sub02
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub02测试标签: [2, 2, 2, 0, 1, 1, 1, 1, 1]

【测试数据镜像训练】为 sub02 添加测试数据的镜像版本到训练集
  ✓ 成功添加 54 个镜像训练样本
  ✓ 训练集大小: 748 → 802
  ✓ 光流数据已正确处理 (u分量取反，v分量保持)
创建受试者目录: sub02
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub02/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub02/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6920[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.3333[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.3333[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub02/sub02.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub02/sub02.pth

[1m[1;96m📅 Epoch 2/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.8965[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.4444[0m | [1m最佳准确率:[0m [1;93m0.3333[0m
[1;92m💾 保存最佳模型，准确率: 0.4444[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub02/sub02.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub02/sub02.pth

[1m[1;96m📅 Epoch 3/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9651[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.6667[0m | [1m最佳准确率:[0m [1;93m0.4444[0m
[1;92m💾 保存最佳模型，准确率: 0.6667[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub02/sub02.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub02/sub02.pth

[1m[1;96m📅 Epoch 4/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9464[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6667[0m | [1m最佳准确率:[0m [1;93m0.6667[0m
[1;92m💾 保存最佳模型，准确率: 0.6667[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub02/sub02.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub02/sub02.pth

[1m[1;96m📅 Epoch 5/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9825[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5556[0m | [1m最佳准确率:[0m [1;93m0.6667[0m

[1m[1;96m📅 Epoch 6/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9364[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5556[0m | [1m最佳准确率:[0m [1;93m0.6667[0m

[1m[1;96m📅 Epoch 7/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9726[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.6667[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub02/sub02.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub02/sub02.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [2, 2, 2, 0, 1, 1, 1, 1, 1]
真实标签: [2, 2, 2, 0, 1, 1, 1, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 8
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 4
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 3
TP: 3, FP: 0, FN: 0, TN: 6
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 8
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 4
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 3
TP: 3, FP: 0, FN: 0, TN: 6
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 1384 个样本
标签 1: 增强了 964 个样本
标签 2: 增强了 1224 个样本

========================================
【当前处理受试者】: sub23
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub23测试标签: [0, 1, 1, 1, 1, 1, 1, 1]
创建受试者目录: sub23
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub23/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub23/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 801 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6813[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.1250[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.1250[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub23/sub23.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub23/sub23.pth

[1m[1;96m📅 Epoch 2/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9041[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.2500[0m | [1m最佳准确率:[0m [1;93m0.1250[0m
[1;92m💾 保存最佳模型，准确率: 0.2500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub23/sub23.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub23/sub23.pth

[1m[1;96m📅 Epoch 3/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9637[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.2500[0m | [1m最佳准确率:[0m [1;93m0.2500[0m
[1;92m💾 保存最佳模型，准确率: 0.2500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub23/sub23.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub23/sub23.pth

[1m[1;96m📅 Epoch 4/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9819[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.6250[0m | [1m最佳准确率:[0m [1;93m0.2500[0m
[1;92m💾 保存最佳模型，准确率: 0.6250[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub23/sub23.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub23/sub23.pth

[1m[1;96m📅 Epoch 5/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9909[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.6250[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub23/sub23.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub23/sub23.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [0, 1, 1, 1, 1, 1, 1, 1]
真实标签: [0, 1, 1, 1, 1, 1, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 7
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 7
TP: 7, FP: 0, FN: 0, TN: 1
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 7
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 7
TP: 7, FP: 0, FN: 0, TN: 1
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 1632 个样本
标签 1: 增强了 1126 个样本
标签 2: 增强了 1449 个样本

========================================
【当前处理受试者】: sub12
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub12测试标签: [2, 2, 2, 2, 0, 0, 1, 1, 1, 1, 1]
创建受试者目录: sub12
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub12/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub12/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 870 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6886[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.3636[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.3636[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub12/sub12.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub12/sub12.pth

[1m[1;96m📅 Epoch 2/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9053[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.4545[0m | [1m最佳准确率:[0m [1;93m0.3636[0m
[1;92m💾 保存最佳模型，准确率: 0.4545[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub12/sub12.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub12/sub12.pth

[1m[1;96m📅 Epoch 3/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9630[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.9091[0m | [1m最佳准确率:[0m [1;93m0.4545[0m
[1;92m💾 保存最佳模型，准确率: 0.9091[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub12/sub12.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub12/sub12.pth

[1m[1;96m📅 Epoch 4/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9890[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9091[0m | [1m最佳准确率:[0m [1;93m0.9091[0m
[1;92m💾 保存最佳模型，准确率: 0.9091[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub12/sub12.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub12/sub12.pth

[1m[1;96m📅 Epoch 5/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9959[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8182[0m | [1m最佳准确率:[0m [1;93m0.9091[0m

[1m[1;96m📅 Epoch 6/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9959[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8182[0m | [1m最佳准确率:[0m [1;93m0.9091[0m

[1m[1;96m📅 Epoch 7/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9986[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8182[0m | [1m最佳准确率:[0m [1;93m0.9091[0m

[1m[1;96m📅 Epoch 8/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8182[0m | [1m最佳准确率:[0m [1;93m0.9091[0m

[1m[1;96m📅 Epoch 9/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8182[0m | [1m最佳准确率:[0m [1;93m0.9091[0m

[1m[1;96m📅 Epoch 10/870[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8182[0m | [1m最佳准确率:[0m [1;93m0.9091[0m

[1m[1;96m📅 Epoch 11/870[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9863[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8182[0m | [1m最佳准确率:[0m [1;93m0.9091[0m

[1m[1;96m📅 Epoch 12/870[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9835[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9091[0m | [1m最佳准确率:[0m [1;93m0.9091[0m
[1;92m💾 保存最佳模型，准确率: 0.9091[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub12/sub12.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub12/sub12.pth

[1m[1;96m📅 Epoch 13/870[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9091[0m | [1m最佳准确率:[0m [1;93m0.9091[0m
[1;92m💾 保存最佳模型，准确率: 0.9091[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub12/sub12.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub12/sub12.pth

[1m[1;96m📅 Epoch 14/870[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.9091[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub12/sub12.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub12/sub12.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [2, 2, 2, 2, 0, 0, 1, 1, 1, 1, 1]
真实标签: [2, 2, 2, 2, 0, 0, 1, 1, 1, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 9
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 6
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 4
TP: 4, FP: 0, FN: 0, TN: 7
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 9
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 6
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 4
TP: 4, FP: 0, FN: 0, TN: 7
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 1872 个样本
标签 1: 增强了 1292 个样本
标签 2: 增强了 1638 个样本

========================================
【当前处理受试者】: sub11
========================================
组别: 小样本组(3-6个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub11测试标签: [1, 1, 1, 1]
创建受试者目录: sub11
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 801 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6443[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.7500[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.7500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth

[1m[1;96m📅 Epoch 2/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.8987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 3/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9696[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;92m💾 保存最佳模型，准确率: 0.7500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth

[1m[1;96m📅 Epoch 4/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9886[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;92m💾 保存最佳模型，准确率: 0.7500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth

[1m[1;96m📅 Epoch 5/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9899[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;92m💾 保存最佳模型，准确率: 0.7500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth

[1m[1;96m📅 Epoch 6/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9949[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.2500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 7/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9949[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 8/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9975[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 9/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9975[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;92m💾 保存最佳模型，准确率: 0.7500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth

[1m[1;96m📅 Epoch 10/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;92m💾 保存最佳模型，准确率: 0.7500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth

[1m[1;96m📅 Epoch 11/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9975[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;92m💾 保存最佳模型，准确率: 0.7500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth

[1m[1;96m📅 Epoch 12/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;92m💾 保存最佳模型，准确率: 0.7500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth

[1m[1;96m📅 Epoch 13/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9975[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;92m💾 保存最佳模型，准确率: 0.7500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth

[1m[1;96m📅 Epoch 14/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9911[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;92m💾 保存最佳模型，准确率: 0.7500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth

[1m[1;96m📅 Epoch 15/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9873[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub11/sub11.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [1, 1, 1, 1]
真实标签: [1, 1, 1, 1]
当前评估结果:
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 4
TP: 4, FP: 0, FN: 0, TN: 0
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 4
TP: 4, FP: 0, FN: 0, TN: 0
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 2128 个样本
标签 1: 增强了 1460 个样本
标签 2: 增强了 1863 个样本

========================================
【当前处理受试者】: sub01
========================================
组别: 小样本组(3-6个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub01测试标签: [0, 1, 1]
创建受试者目录: sub01
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub01/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub01/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 801 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.7103[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.3333[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.3333[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub01/sub01.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub01/sub01.pth

[1m[1;96m📅 Epoch 2/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9263[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.3333[0m | [1m最佳准确率:[0m [1;93m0.3333[0m
[1;92m💾 保存最佳模型，准确率: 0.3333[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub01/sub01.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub01/sub01.pth

[1m[1;96m📅 Epoch 3/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9695[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.3333[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub01/sub01.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub01/sub01.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [0, 1, 1]
真实标签: [0, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 1
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 1
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 2376 个样本
标签 1: 增强了 1632 个样本
标签 2: 增强了 2088 个样本

========================================
【当前处理受试者】: sub07
========================================
组别: 小样本组(3-6个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub07测试标签: [1, 1, 1, 1, 1]
创建受试者目录: sub07
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub07/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub07/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 801 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6950[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub07/sub07.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub07/sub07.pth

[1m[1;96m📅 Epoch 2/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.8933[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.6000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.6000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub07/sub07.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub07/sub07.pth

[1m[1;96m📅 Epoch 3/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9416[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.6000[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub07/sub07.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub07/sub07.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [1, 1, 1, 1, 1]
真实标签: [1, 1, 1, 1, 1]
当前评估结果:
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 0
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 0
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 2632 个样本
标签 1: 增强了 1798 个样本
标签 2: 增强了 2313 个样本

========================================
【当前处理受试者】: sub24
========================================
组别: 小样本组(3-6个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub24测试标签: [2, 1, 1]
创建受试者目录: sub24
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub24/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub24/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 801 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6578[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub24/sub24.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub24/sub24.pth

[1m[1;96m📅 Epoch 2/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.8957[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub24/sub24.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub24/sub24.pth

[1m[1;96m📅 Epoch 3/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9593[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.3333[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.3333[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub24/sub24.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub24/sub24.pth

[1m[1;96m📅 Epoch 4/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9924[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.3333[0m | [1m最佳准确率:[0m [1;93m0.3333[0m
[1;92m💾 保存最佳模型，准确率: 0.3333[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub24/sub24.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub24/sub24.pth

[1m[1;96m📅 Epoch 5/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.3333[0m | [1m最佳准确率:[0m [1;93m0.3333[0m
[1;92m💾 保存最佳模型，准确率: 0.3333[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub24/sub24.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub24/sub24.pth

[1m[1;96m📅 Epoch 6/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9962[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.3333[0m | [1m最佳准确率:[0m [1;93m0.3333[0m
[1;92m💾 保存最佳模型，准确率: 0.3333[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub24/sub24.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub24/sub24.pth

[1m[1;96m📅 Epoch 7/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9975[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.6667[0m | [1m最佳准确率:[0m [1;93m0.3333[0m
[1;92m💾 保存最佳模型，准确率: 0.6667[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub24/sub24.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub24/sub24.pth

[1m[1;96m📅 Epoch 8/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9847[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.3333[0m | [1m最佳准确率:[0m [1;93m0.6667[0m

[1m[1;96m📅 Epoch 9/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9898[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.3333[0m | [1m最佳准确率:[0m [1;93m0.6667[0m

[1m[1;96m📅 Epoch 10/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.3333[0m | [1m最佳准确率:[0m [1;93m0.6667[0m

[1m[1;96m📅 Epoch 11/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.3333[0m | [1m最佳准确率:[0m [1;93m0.6667[0m

[1m[1;96m📅 Epoch 12/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.3333[0m | [1m最佳准确率:[0m [1;93m0.6667[0m

[1m[1;96m📅 Epoch 13/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.3333[0m | [1m最佳准确率:[0m [1;93m0.6667[0m

[1m[1;96m📅 Epoch 14/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.3333[0m | [1m最佳准确率:[0m [1;93m0.6667[0m

[1m[1;96m📅 Epoch 15/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9949[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.6667[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub24/sub24.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub24/sub24.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [2, 1, 1]
真实标签: [2, 1, 1]
当前评估结果:
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 1
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 1
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 2888 个样本
标签 1: 增强了 1970 个样本
标签 2: 增强了 2529 个样本

========================================
【当前处理受试者】: sub25
========================================
组别: 小样本组(3-6个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub25测试标签: [2, 2, 1, 1, 1]
创建受试者目录: sub25
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub25/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub25/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 801 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6313[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.8000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub25/sub25.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub25/sub25.pth

[1m[1;96m📅 Epoch 2/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9120[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub25/sub25.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub25/sub25.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [2, 2, 1, 1, 1]
真实标签: [2, 2, 1, 1, 1]
当前评估结果:
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 3
TP: 3, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 3
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 3
TP: 3, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 3
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 3144 个样本
标签 1: 增强了 2140 个样本
标签 2: 增强了 2736 个样本

========================================
【当前处理受试者】: sub03
========================================
组别: 小样本组(3-6个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub03测试标签: [2, 1, 1, 1, 1]
创建受试者目录: sub03
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 801 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6705[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.2000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.2000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 2/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.8885[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.4000[0m | [1m最佳准确率:[0m [1;93m0.2000[0m
[1;92m💾 保存最佳模型，准确率: 0.4000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 3/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9590[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.2000[0m | [1m最佳准确率:[0m [1;93m0.4000[0m

[1m[1;96m📅 Epoch 4/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9923[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.4000[0m | [1m最佳准确率:[0m [1;93m0.4000[0m
[1;92m💾 保存最佳模型，准确率: 0.4000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 5/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9962[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.6000[0m | [1m最佳准确率:[0m [1;93m0.4000[0m
[1;92m💾 保存最佳模型，准确率: 0.6000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 6/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.2000[0m | [1m最佳准确率:[0m [1;93m0.6000[0m

[1m[1;96m📅 Epoch 7/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9962[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.6000[0m
[1;92m💾 保存最佳模型，准确率: 0.6000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 8/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.6000[0m
[1;92m💾 保存最佳模型，准确率: 0.6000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 9/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9962[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.4000[0m | [1m最佳准确率:[0m [1;93m0.6000[0m

[1m[1;96m📅 Epoch 10/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9859[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.8000[0m | [1m最佳准确率:[0m [1;93m0.6000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 11/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9949[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m

[1m[1;96m📅 Epoch 12/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9936[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m

[1m[1;96m📅 Epoch 13/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m

[1m[1;96m📅 Epoch 14/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9974[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 15/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9949[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m

[1m[1;96m📅 Epoch 16/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9974[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 17/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9731[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 18/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9962[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 19/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 20/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 21/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 22/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 23/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 24/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9974[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 25/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 26/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9962[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 27/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9923[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 28/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9962[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 29/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9846[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth

[1m[1;96m📅 Epoch 30/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9936[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub03/sub03.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [2, 1, 1, 1, 1]
真实标签: [2, 1, 1, 1, 1]
当前评估结果:
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 4
TP: 4, FP: 0, FN: 0, TN: 1
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 4
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 4
TP: 4, FP: 0, FN: 0, TN: 1
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 4
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 3400 个样本
标签 1: 增强了 2308 个样本
标签 2: 增强了 2952 个样本

========================================
【当前处理受试者】: sub06
========================================
组别: 小样本组(3-6个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub06测试标签: [2, 2, 0, 1]
创建受试者目录: sub06
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 801 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6896[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.7500[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.7500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/sub06.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/sub06.pth

[1m[1;96m📅 Epoch 2/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9117[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;92m💾 保存最佳模型，准确率: 0.7500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/sub06.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/sub06.pth

[1m[1;96m📅 Epoch 3/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9506[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;92m💾 保存最佳模型，准确率: 0.7500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/sub06.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/sub06.pth

[1m[1;96m📅 Epoch 4/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9662[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;92m💾 保存最佳模型，准确率: 0.7500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/sub06.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/sub06.pth

[1m[1;96m📅 Epoch 5/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9779[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;92m💾 保存最佳模型，准确率: 0.7500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/sub06.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/sub06.pth

[1m[1;96m📅 Epoch 6/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9455[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;92m💾 保存最佳模型，准确率: 0.7500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/sub06.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/sub06.pth

[1m[1;96m📅 Epoch 7/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9883[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;92m💾 保存最佳模型，准确率: 0.7500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/sub06.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/sub06.pth

[1m[1;96m📅 Epoch 8/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9779[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;92m💾 保存最佳模型，准确率: 0.7500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/sub06.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/sub06.pth

[1m[1;96m📅 Epoch 9/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9948[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;92m💾 保存最佳模型，准确率: 0.7500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/sub06.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/sub06.pth

[1m[1;96m📅 Epoch 10/801[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9883[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/sub06.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub06/sub06.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [2, 2, 0, 1]
真实标签: [2, 2, 0, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 3
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 3
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 3
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 3
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 3648 个样本
标签 1: 增强了 2482 个样本
标签 2: 增强了 3159 个样本

========================================
【当前处理受试者】: sub15
========================================
组别: 小样本组(3-6个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...

sub15测试标签: [2, 0, 1]
创建受试者目录: sub15
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub15/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub15/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 801 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6603[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.3333[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.3333[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub15/sub15.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub15/sub15.pth

[1m[1;96m📅 Epoch 2/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.8885[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.3333[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub15/sub15.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub15/sub15.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [2, 0, 1]
真实标签: [2, 0, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 3896 个样本
标签 1: 增强了 2656 个样本
标签 2: 增强了 3375 个样本

========================================
【当前处理受试者】: sub20
========================================
组别: 极小样本组(<=2个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub20测试标签: [1, 1]
创建受试者目录: sub20
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub20/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub20/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 801 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6784[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub20/sub20.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub20/sub20.pth

[1m[1;96m📅 Epoch 2/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.8970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub20/sub20.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub20/sub20.pth

[1m[1;96m📅 Epoch 3/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9648[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.5000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.5000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub20/sub20.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub20/sub20.pth

[1m[1;96m📅 Epoch 4/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9824[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.5000[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub20/sub20.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub20/sub20.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [1, 1]
真实标签: [1, 1]
当前评估结果:
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 0
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 0
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 4152 个样本
标签 1: 增强了 2828 个样本
标签 2: 增强了 3600 个样本

========================================
【当前处理受试者】: sub04
========================================
组别: 极小样本组(<=2个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub04测试标签: [1, 1]
创建受试者目录: sub04
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub04/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub04/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 801 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6533[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub04/sub04.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub04/sub04.pth

[1m[1;96m📅 Epoch 2/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9033[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub04/sub04.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub04/sub04.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [1, 1]
真实标签: [1, 1]
当前评估结果:
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 0
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 0
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 4408 个样本
标签 1: 增强了 3000 个样本
标签 2: 增强了 3825 个样本

========================================
【当前处理受试者】: sub13
========================================
组别: 极小样本组(<=2个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub13测试标签: [0, 0]
创建受试者目录: sub13
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub13/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub13/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 801 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6760[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub13/sub13.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub13/sub13.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [0, 0]
真实标签: [0, 0]
当前评估结果:

正性(positive)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 0
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
负性(negative): 没有样本
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 0
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
负性(negative): 没有样本
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 4648 个样本
标签 1: 增强了 3176 个样本
标签 2: 增强了 4050 个样本

========================================
【当前处理受试者】: sub08
========================================
组别: 极小样本组(<=2个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub08测试标签: [1]
创建受试者目录: sub08
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub08/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub08/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 801 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6996[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub08/sub08.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub08/sub08.pth

[1m[1;96m📅 Epoch 2/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9086[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub08/sub08.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub08/sub08.pth

[1m[1;96m📅 Epoch 3/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9637[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub08/sub08.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub08/sub08.pth

[1m[1;96m📅 Epoch 4/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9937[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub08/sub08.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub08/sub08.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [1]
真实标签: [1]
当前评估结果:
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 0
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 0
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 4904 个样本
标签 1: 增强了 3350 个样本
标签 2: 增强了 4275 个样本

========================================
【当前处理受试者】: sub16
========================================
组别: 极小样本组(<=2个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub16测试标签: [0, 0]
创建受试者目录: sub16
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub16/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub16/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 801 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6416[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub16/sub16.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub16/sub16.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [0, 0]
真实标签: [0, 0]
当前评估结果:

正性(positive)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 0
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
负性(negative): 没有样本
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 0
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
负性(negative): 没有样本
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 5144 个样本
标签 1: 增强了 3526 个样本
标签 2: 增强了 4500 个样本

========================================
【当前处理受试者】: sub21
========================================
组别: 极小样本组(<=2个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub21测试标签: [1]
创建受试者目录: sub21
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub21/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub21/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 801 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.7196[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub21/sub21.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub21/sub21.pth

[1m[1;96m📅 Epoch 2/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.8961[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub21/sub21.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub21/sub21.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [1]
真实标签: [1]
当前评估结果:
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 0
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 0
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 5400 个样本
标签 1: 增强了 3700 个样本
标签 2: 增强了 4725 个样本

========================================
【当前处理受试者】: sub22
========================================
组别: 极小样本组(<=2个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...

sub22测试标签: [1, 1]
创建受试者目录: sub22
创建TensorBoard日志目录: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub22/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub22/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用LEGM模块[0m
  [96m- 使用标准ECA注意力模块[0m 🔧
  [96m- 保持原始模型结构[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 801 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6633[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub22/sub22.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub22/sub22.pth

[1m[1;96m📅 Epoch 2/801[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.8920[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub22/sub22.pth[0m
模型保存成功: ./Experiment_for_recognize/CASME2C3JJ3_class3_conv3_legm1/sub22/sub22.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [1, 1]
真实标签: [1, 1]
当前评估结果:
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 0
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
正性(positive): 没有样本

负性(negative)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 0
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 5656 个样本
标签 1: 增强了 3872 个样本
标签 2: 增强了 4950 个样本
正在关闭TensorBoard写入器...
TensorBoard写入器已关闭

================================================================================
【实验结果报告】
================================================================================

【基本信息】
实验名称: CASME2C3JJ3_class3_conv3_legm1
时间: 2025-07-15 23:11:38
总训练时间: 01:27:46
数据集: CASME2_LOSO_full

【系统环境】
操作系统: 💻 Linux 6.8.0-1030-nvidia
处理器: ⚡ 32核心处理器
内存: 🧠 63.3GB RAM
GPU: 🚀 NVIDIA A800 80GB PCIe (79.3GB)

【模型配置】
模型架构: SKD_TSTSAN
是否使用预训练: 是
预训练模型: SKD-TSTSAN.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
批次大小: 32
最大迭代次数: 20000
损失函数: FocalLoss_weighted
随机种子: 1337

【GPU性能配置】
CUDNN Benchmark: 禁用
CUDNN Deterministic: 启用
混合精度训练: 禁用
TF32加速: 启用
BatchNorm修复: 禁用
卷积类型: 小波变换卷积

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: 1.0000
- UAR分数: 1.0000

【各表情类别准确率】
--------------------------------------------------
- 正性: 1.0000
- 负性: 1.0000
- 惊讶: 1.0000

【各受试者评估结果】
--------------------------------------------------
受试者 sub01: UF1=1.0000, UAR=1.0000
受试者 sub02: UF1=1.0000, UAR=1.0000
受试者 sub03: UF1=1.0000, UAR=1.0000
受试者 sub04: UF1=1.0000, UAR=1.0000
受试者 sub05: UF1=1.0000, UAR=1.0000
受试者 sub06: UF1=1.0000, UAR=1.0000
受试者 sub07: UF1=1.0000, UAR=1.0000
受试者 sub08: UF1=1.0000, UAR=1.0000
受试者 sub09: UF1=1.0000, UAR=1.0000
受试者 sub11: UF1=1.0000, UAR=1.0000
受试者 sub12: UF1=1.0000, UAR=1.0000
受试者 sub13: UF1=1.0000, UAR=1.0000
受试者 sub15: UF1=1.0000, UAR=1.0000
受试者 sub16: UF1=1.0000, UAR=1.0000
受试者 sub17: UF1=1.0000, UAR=1.0000
受试者 sub19: UF1=1.0000, UAR=1.0000
受试者 sub20: UF1=1.0000, UAR=1.0000
受试者 sub21: UF1=1.0000, UAR=1.0000
受试者 sub22: UF1=1.0000, UAR=1.0000
受试者 sub23: UF1=1.0000, UAR=1.0000
受试者 sub24: UF1=1.0000, UAR=1.0000
受试者 sub25: UF1=1.0000, UAR=1.0000
受试者 sub26: UF1=1.0000, UAR=1.0000
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
  sub01      1.0000     1.0000     1.0000  
  sub02      1.0000     1.0000     1.0000  
  sub03      1.0000     1.0000     1.0000  
  sub04      1.0000     1.0000     1.0000  
  sub05      1.0000     1.0000     1.0000  
  sub06      1.0000     1.0000     1.0000  
  sub07      1.0000     1.0000     1.0000  
  sub08      1.0000     1.0000     1.0000  
  sub09      1.0000     1.0000     1.0000  
  sub11      1.0000     1.0000     1.0000  
  sub12      1.0000     1.0000     1.0000  
  sub13      1.0000     1.0000     1.0000  
  sub15      1.0000     1.0000     1.0000  
  sub16      1.0000     1.0000     1.0000  
  sub17      1.0000     1.0000     1.0000  
  sub19      1.0000     1.0000     1.0000  
  sub20      1.0000     1.0000     1.0000  
  sub21      1.0000     1.0000     1.0000  
  sub22      1.0000     1.0000     1.0000  
  sub23      1.0000     1.0000     1.0000  
  sub24      1.0000     1.0000     1.0000  
  sub25      1.0000     1.0000     1.0000  
  sub26      1.0000     1.0000     1.0000  
--------------------------------------------------
    平均       1.0000     1.0000     1.0000  


【各受试者详细预测结果】
==================================================

受试者 sub01:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 正性: 1.0000 (1/1)
- 负性: 1.0000 (2/2)

标签对比:
预测标签: [0, 1, 1]
真实标签: [0, 1, 1]
--------------------------------------------------

受试者 sub02:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 正性: 1.0000 (1/1)
- 负性: 1.0000 (5/5)
- 惊讶: 1.0000 (3/3)

标签对比:
预测标签: [2, 2, 2, 0, 1, 1, 1, 1, 1]
真实标签: [2, 2, 2, 0, 1, 1, 1, 1, 1]
--------------------------------------------------

受试者 sub03:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 负性: 1.0000 (4/4)
- 惊讶: 1.0000 (1/1)

标签对比:
预测标签: [2, 1, 1, 1, 1]
真实标签: [2, 1, 1, 1, 1]
--------------------------------------------------

受试者 sub04:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 负性: 1.0000 (2/2)

标签对比:
预测标签: [1, 1]
真实标签: [1, 1]
--------------------------------------------------

受试者 sub05:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 正性: 1.0000 (1/1)
- 负性: 1.0000 (1/1)
- 惊讶: 1.0000 (5/5)

标签对比:
预测标签: [2, 2, 2, 2, 2, 0, 1]
真实标签: [2, 2, 2, 2, 2, 0, 1]
--------------------------------------------------

受试者 sub06:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 正性: 1.0000 (1/1)
- 负性: 1.0000 (1/1)
- 惊讶: 1.0000 (2/2)

标签对比:
预测标签: [2, 2, 0, 1]
真实标签: [2, 2, 0, 1]
--------------------------------------------------

受试者 sub07:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 负性: 1.0000 (5/5)

标签对比:
预测标签: [1, 1, 1, 1, 1]
真实标签: [1, 1, 1, 1, 1]
--------------------------------------------------

受试者 sub08:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 负性: 1.0000 (1/1)

标签对比:
预测标签: [1]
真实标签: [1]
--------------------------------------------------

受试者 sub09:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 正性: 1.0000 (5/5)
- 负性: 1.0000 (5/5)

标签对比:
预测标签: [0, 0, 0, 0, 0, 1, 1, 1, 1, 1]
真实标签: [0, 0, 0, 0, 0, 1, 1, 1, 1, 1]
--------------------------------------------------

受试者 sub11:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 负性: 1.0000 (4/4)

标签对比:
预测标签: [1, 1, 1, 1]
真实标签: [1, 1, 1, 1]
--------------------------------------------------

受试者 sub12:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 正性: 1.0000 (2/2)
- 负性: 1.0000 (5/5)
- 惊讶: 1.0000 (4/4)

标签对比:
预测标签: [2, 2, 2, 2, 0, 0, 1, 1, 1, 1, 1]
真实标签: [2, 2, 2, 2, 0, 0, 1, 1, 1, 1, 1]
--------------------------------------------------

受试者 sub13:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 正性: 1.0000 (2/2)

标签对比:
预测标签: [0, 0]
真实标签: [0, 0]
--------------------------------------------------

受试者 sub15:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 正性: 1.0000 (1/1)
- 负性: 1.0000 (1/1)
- 惊讶: 1.0000 (1/1)

标签对比:
预测标签: [2, 0, 1]
真实标签: [2, 0, 1]
--------------------------------------------------

受试者 sub16:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 正性: 1.0000 (2/2)

标签对比:
预测标签: [0, 0]
真实标签: [0, 0]
--------------------------------------------------

受试者 sub17:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 正性: 1.0000 (7/7)
- 负性: 1.0000 (23/23)
- 惊讶: 1.0000 (1/1)

标签对比:
预测标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
真实标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
--------------------------------------------------

受试者 sub19:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 正性: 1.0000 (3/3)
- 负性: 1.0000 (3/3)
- 惊讶: 1.0000 (5/5)

标签对比:
预测标签: [2, 2, 2, 2, 2, 0, 0, 0, 1, 1, 1]
真实标签: [2, 2, 2, 2, 2, 0, 0, 0, 1, 1, 1]
--------------------------------------------------

受试者 sub20:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 负性: 1.0000 (2/2)

标签对比:
预测标签: [1, 1]
真实标签: [1, 1]
--------------------------------------------------

受试者 sub21:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 负性: 1.0000 (1/1)

标签对比:
预测标签: [1]
真实标签: [1]
--------------------------------------------------

受试者 sub22:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 负性: 1.0000 (2/2)

标签对比:
预测标签: [1, 1]
真实标签: [1, 1]
--------------------------------------------------

受试者 sub23:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 正性: 1.0000 (1/1)
- 负性: 1.0000 (7/7)

标签对比:
预测标签: [0, 1, 1, 1, 1, 1, 1, 1]
真实标签: [0, 1, 1, 1, 1, 1, 1, 1]
--------------------------------------------------

受试者 sub24:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 负性: 1.0000 (2/2)
- 惊讶: 1.0000 (1/1)

标签对比:
预测标签: [2, 1, 1]
真实标签: [2, 1, 1]
--------------------------------------------------

受试者 sub25:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 负性: 1.0000 (3/3)
- 惊讶: 1.0000 (2/2)

标签对比:
预测标签: [2, 2, 1, 1, 1]
真实标签: [2, 2, 1, 1, 1]
--------------------------------------------------

受试者 sub26:
性能指标: UF1=1.0000, UAR=1.0000

类别统计:
- 正性: 1.0000 (2/2)
- 负性: 1.0000 (9/9)

标签对比:
预测标签: [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1]
真实标签: [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1]
--------------------------------------------------

================================================================================

【实验结果报告】
================================================================================

================================================================================
【实验结果报告 - 3分类 (正性、负性、惊讶)】
================================================================================

【基本信息】
实验名称: CASME2C3JJ3_class3_conv3_legm1
分类方案: 3分类 (正性、负性、惊讶)
时间: 2025-07-15 23:11:38
总训练时间: 01:27:46
数据集: CASME2_LOSO_full


【系统环境】
操作系统: 💻 Linux 6.8.0-1030-nvidia
处理器: ⚡ 32核心处理器
内存: 🧠 63.3GB RAM
GPU: 🚀 NVIDIA A800 80GB PCIe (79.3GB)
GPU数量: 1
训练模式: 单设备

【模型配置】
模型架构: SKD_TSTSAN
分类数量: 3
是否使用预训练: 是
预训练模型: SKD-TSTSAN.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
基础批次大小: 32
最大迭代次数: 20000
损失函数: FocalLoss_weighted
随机种子: 1337

【GPU性能配置】
CUDNN Benchmark: 禁用
CUDNN Deterministic: 启用
混合精度训练: 禁用
TF32加速: 启用
BatchNorm修复: 禁用
卷积类型: 小波变换卷积

【数据增强配置】
使用训练数据增强: 是
使用测试数据增强: 是
旋转角度范围: 3,8
训练增强倍数: 9,3,10
测试增强倍数: 9,3,10
测试数据镜像训练: 启用
镜像训练受试者: sub02,sub05

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: 1.0000
- UAR分数: 1.0000

【各表情类别准确率】
--------------------------------------------------
- 正性: 1.0000
- 负性: 1.0000
- 惊讶: 1.0000


【各受试者评估结果】
--------------------------------------------------
受试者 sub01: UF1=1.0000, UAR=1.0000
受试者 sub02: UF1=1.0000, UAR=1.0000
受试者 sub03: UF1=1.0000, UAR=1.0000
受试者 sub04: UF1=1.0000, UAR=1.0000
受试者 sub05: UF1=1.0000, UAR=1.0000
受试者 sub06: UF1=1.0000, UAR=1.0000
受试者 sub07: UF1=1.0000, UAR=1.0000
受试者 sub08: UF1=1.0000, UAR=1.0000
受试者 sub09: UF1=1.0000, UAR=1.0000
受试者 sub11: UF1=1.0000, UAR=1.0000
受试者 sub12: UF1=1.0000, UAR=1.0000
受试者 sub13: UF1=1.0000, UAR=1.0000
受试者 sub15: UF1=1.0000, UAR=1.0000
受试者 sub16: UF1=1.0000, UAR=1.0000
受试者 sub17: UF1=1.0000, UAR=1.0000
受试者 sub19: UF1=1.0000, UAR=1.0000
受试者 sub20: UF1=1.0000, UAR=1.0000
受试者 sub21: UF1=1.0000, UAR=1.0000
受试者 sub22: UF1=1.0000, UAR=1.0000
受试者 sub23: UF1=1.0000, UAR=1.0000
受试者 sub24: UF1=1.0000, UAR=1.0000
受试者 sub25: UF1=1.0000, UAR=1.0000
受试者 sub26: UF1=1.0000, UAR=1.0000
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
  sub01      1.0000     1.0000     1.0000  
  sub02      1.0000     1.0000     1.0000  
  sub03      1.0000     1.0000     1.0000  
  sub04      1.0000     1.0000     1.0000  
  sub05      1.0000     1.0000     1.0000  
  sub06      1.0000     1.0000     1.0000  
  sub07      1.0000     1.0000     1.0000  
  sub08      1.0000     1.0000     1.0000  
  sub09      1.0000     1.0000     1.0000  
  sub11      1.0000     1.0000     1.0000  
  sub12      1.0000     1.0000     1.0000  
  sub13      1.0000     1.0000     1.0000  
  sub15      1.0000     1.0000     1.0000  
  sub16      1.0000     1.0000     1.0000  
  sub17      1.0000     1.0000     1.0000  
  sub19      1.0000     1.0000     1.0000  
  sub20      1.0000     1.0000     1.0000  
  sub21      1.0000     1.0000     1.0000  
  sub22      1.0000     1.0000     1.0000  
  sub23      1.0000     1.0000     1.0000  
  sub24      1.0000     1.0000     1.0000  
  sub25      1.0000     1.0000     1.0000  
  sub26      1.0000     1.0000     1.0000  
--------------------------------------------------
    平均       1.0000     1.0000     1.0000  


【最优预测结果与原始标签对比】
--------------------------------------------------

受试者 sub01:
预测标签: [0, 1, 1]
真实标签: [0, 1, 1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub02:
预测标签: [2, 2, 2, 0, 1, 1, 1, 1, 1]
真实标签: [2, 2, 2, 0, 1, 1, 1, 1, 1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub03:
预测标签: [2, 1, 1, 1, 1]
真实标签: [2, 1, 1, 1, 1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub04:
预测标签: [1, 1]
真实标签: [1, 1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub05:
预测标签: [2, 2, 2, 2, 2, 0, 1]
真实标签: [2, 2, 2, 2, 2, 0, 1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub06:
预测标签: [2, 2, 0, 1]
真实标签: [2, 2, 0, 1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub07:
预测标签: [1, 1, 1, 1, 1]
真实标签: [1, 1, 1, 1, 1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub08:
预测标签: [1]
真实标签: [1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub09:
预测标签: [0, 0, 0, 0, 0, 1, 1, 1, 1, 1]
真实标签: [0, 0, 0, 0, 0, 1, 1, 1, 1, 1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub11:
预测标签: [1, 1, 1, 1]
真实标签: [1, 1, 1, 1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub12:
预测标签: [2, 2, 2, 2, 0, 0, 1, 1, 1, 1, 1]
真实标签: [2, 2, 2, 2, 0, 0, 1, 1, 1, 1, 1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub13:
预测标签: [0, 0]
真实标签: [0, 0]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub15:
预测标签: [2, 0, 1]
真实标签: [2, 0, 1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub16:
预测标签: [0, 0]
真实标签: [0, 0]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub17:
预测标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
真实标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub19:
预测标签: [2, 2, 2, 2, 2, 0, 0, 0, 1, 1, 1]
真实标签: [2, 2, 2, 2, 2, 0, 0, 0, 1, 1, 1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub20:
预测标签: [1, 1]
真实标签: [1, 1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub21:
预测标签: [1]
真实标签: [1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub22:
预测标签: [1, 1]
真实标签: [1, 1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub23:
预测标签: [0, 1, 1, 1, 1, 1, 1, 1]
真实标签: [0, 1, 1, 1, 1, 1, 1, 1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub24:
预测标签: [2, 1, 1]
真实标签: [2, 1, 1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub25:
预测标签: [2, 2, 1, 1, 1]
真实标签: [2, 2, 1, 1, 1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

受试者 sub26:
预测标签: [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1]
真实标签: [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1]
UF1: 1.0000, UAR: 1.0000
--------------------------------------------------

================================================================================

================================================================================

【邮件通知】正在发送训练结果...

【邮件通知】邮件发送成功!
- 发件人: <EMAIL>
- 收件人: <EMAIL>
- 主题: [3分类 (正性、负性、惊讶)实验总结] CASME2C3JJ3_class3_conv3_legm1 - UF1=1.0000, UAR=1.0000

训练完成!

正在关闭日志系统...
