
================================================================================
【MMEW数据集训练开始】
================================================================================
使用设备: cuda
训练顺序: ['S30', 'S06', 'S13', 'S09', 'S10', 'S21', 'S16', 'S05', 'S11', 'S15', 'S03', 'S08', 'S01', 'S18', 'S02', 'S04', 'S12', 'S20', 'S22', 'S24', 'S25', 'S29', 'S07', 'S17', 'S19', 'S27', 'S28', 'S14', 'S23', 'S26']
总共需要训练 30 个受试者

============================================================
训练受试者 1/30: S30
受试者组别: 大样本组(>15个测试样本)
============================================================
正在加载MMEW数据集，测试受试者: S30
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 5: 61 个原始样本, 扩充倍数: 6
  加载 S01 训练数据: 696 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 5: 61 个原始样本, 扩充倍数: 6
  加载 S02 训练数据: 697 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 82 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 5: 61 个原始样本, 扩充倍数: 6
  加载 S03 训练数据: 700 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 69 个原始样本, 扩充倍数: 2
    情绪类别 5: 64 个原始样本, 扩充倍数: 6
  加载 S04 训练数据: 713 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 5: 62 个原始样本, 扩充倍数: 6
  加载 S05 训练数据: 697 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 5: 55 个原始样本, 扩充倍数: 6
  加载 S06 训练数据: 657 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 5: 66 个原始样本, 扩充倍数: 6
  加载 S07 训练数据: 732 个样本
    情绪类别 0: 31 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 70 个原始样本, 扩充倍数: 2
    情绪类别 5: 64 个原始样本, 扩充倍数: 6
  加载 S08 训练数据: 705 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 65 个原始样本, 扩充倍数: 2
    情绪类别 5: 65 个原始样本, 扩充倍数: 6
  加载 S09 训练数据: 709 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 66 个原始样本, 扩充倍数: 2
    情绪类别 5: 62 个原始样本, 扩充倍数: 6
  加载 S10 训练数据: 693 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 5: 65 个原始样本, 扩充倍数: 6
  加载 S11 训练数据: 715 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 5: 65 个原始样本, 扩充倍数: 6
  加载 S12 训练数据: 727 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 65 个原始样本, 扩充倍数: 2
    情绪类别 5: 65 个原始样本, 扩充倍数: 6
  加载 S13 训练数据: 704 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 5: 65 个原始样本, 扩充倍数: 6
  加载 S14 训练数据: 728 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 5: 63 个原始样本, 扩充倍数: 6
  加载 S15 训练数据: 705 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 62 个原始样本, 扩充倍数: 2
    情绪类别 5: 64 个原始样本, 扩充倍数: 6
  加载 S16 训练数据: 703 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 5: 65 个原始样本, 扩充倍数: 6
  加载 S17 训练数据: 728 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 5: 64 个原始样本, 扩充倍数: 6
  加载 S18 训练数据: 712 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 5: 65 个原始样本, 扩充倍数: 6
  加载 S19 训练数据: 729 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 69 个原始样本, 扩充倍数: 2
    情绪类别 5: 66 个原始样本, 扩充倍数: 6
  加载 S20 训练数据: 729 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 5: 62 个原始样本, 扩充倍数: 6
  加载 S21 训练数据: 699 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 70 个原始样本, 扩充倍数: 2
    情绪类别 5: 66 个原始样本, 扩充倍数: 6
  加载 S22 训练数据: 729 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 5: 65 个原始样本, 扩充倍数: 6
  加载 S23 训练数据: 731 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 5: 66 个原始样本, 扩充倍数: 6
  加载 S24 训练数据: 731 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 5: 65 个原始样本, 扩充倍数: 6
  加载 S25 训练数据: 728 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 5: 65 个原始样本, 扩充倍数: 6
  加载 S26 训练数据: 730 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 5: 65 个原始样本, 扩充倍数: 6
  加载 S27 训练数据: 728 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 5: 65 个原始样本, 扩充倍数: 6
  加载 S28 训练数据: 728 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 5: 66 个原始样本, 扩充倍数: 6
  加载 S29 训练数据: 733 个样本
    情绪类别 0: 4 个原始样本, 扩充倍数: 1
    情绪类别 1: 14 个原始样本, 扩充倍数: 1
    情绪类别 2: 12 个原始样本, 扩充倍数: 1
    情绪类别 5: 5 个原始样本, 扩充倍数: 1
  加载 S30 测试数据: 35 个样本
数据加载完成 - 训练: 20716, 测试: 35
训练数据最终形状: (20716, 48, 48, 36)
测试数据最终形状: (35, 48, 48, 36)
训练样本数: 20716, 测试样本数: 35
训练标签分布: {np.int64(0): np.int64(3036), np.int64(1): np.int64(2506), np.int64(2): np.int64(4056), np.int64(5): np.int64(11118)}
测试标签分布: {np.int64(0): np.int64(4), np.int64(1): np.int64(14), np.int64(2): np.int64(12), np.int64(5): np.int64(5)}
加载预训练模型: /home/<USER>/data/ajq/SKD-TSTSAN-new/Pretrained_model/SKD-TSTSAN.pth
跳过不存在的层: bn1_L.weight
跳过不存在的层: bn1_L.bias
跳过不存在的层: bn1_L.running_mean
跳过不存在的层: bn1_L.running_var
跳过不存在的层: bn1_L.num_batches_tracked
跳过不存在的层: bn1_S.weight
跳过不存在的层: bn1_S.bias
跳过不存在的层: bn1_S.running_mean
跳过不存在的层: bn1_S.running_var
跳过不存在的层: bn1_S.num_batches_tracked
跳过不存在的层: bn1_T.weight
跳过不存在的层: bn1_T.bias
跳过不存在的层: bn1_T.running_mean
跳过不存在的层: bn1_T.running_var
跳过不存在的层: bn1_T.num_batches_tracked
跳过不存在的层: AC1_fc.weight
跳过不存在的层: AC1_fc.bias
跳过不存在的层: AC2_fc.weight
跳过不存在的层: AC2_fc.bias
跳过形状不匹配的层: fc2.weight (预训练: torch.Size([7, 384]), 当前: torch.Size([4, 384]))
跳过形状不匹配的层: fc2.bias (预训练: torch.Size([7]), 当前: torch.Size([4]))
预训练模型部分加载成功，加载了 234/255 层
开始训练受试者 S30，总共1个epoch...
自适应参数 - Alpha: 0.1000, Temperature: 3.0, Beta: 1.00e-06

📊 资源使用情况:
  CPU使用率: 98.0%
  内存使用率: 49.0%
  可用内存: 32.3GB
  GPU内存: 已分配: 0.0GB, 已保留: 0.0GB

Epoch 1/1

训练过程出错: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.


【MMEW数据集实验结果报告】
================================================================================

================================================================================
【MMEW数据集实验结果报告】
================================================================================

【基本信息】
实验名称: MMEW_4class_fixed_v2
时间: 2025-07-11 15:09:20
总训练时间: 00:00:46
数据集: MMEW_LOSO_full


【系统环境】
操作系统: Linux 6.8.0-1030-nvidia
处理器: 32核
内存: 63.3GB
GPU: NVIDIA A800 80GB PCIe (79.3GB)

【模型配置】
模型架构: SKD_TSTSAN
是否使用预训练: 是
预训练模型: SKD-TSTSAN.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
批次大小: 8
最大迭代次数: 50
损失函数: FocalLoss_weighted
随机种子: 1337

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: N/A
- UAR分数: N/A

【各表情类别准确率】
--------------------------------------------------


【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
    平均        nan        nan        nan    


【最优预测结果与原始标签对比】
--------------------------------------------------

================================================================================

================================================================================

【邮件通知】正在发送训练结果...

【邮件通知】邮件发送成功!
- 发件人: <EMAIL>
- 收件人: <EMAIL>
- 主题: [MMEW实验总结] MMEW_4class_fixed_v2 - UF1=N/A, UAR=N/A
