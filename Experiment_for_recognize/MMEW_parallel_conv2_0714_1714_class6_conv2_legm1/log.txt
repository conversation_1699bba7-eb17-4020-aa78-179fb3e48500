
================================================================================
【训练开始】
================================================================================

【硬件信息】使用单个GPU进行训练
✓ TF32加速已启用
✓ 混合精度训练已启用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

【损失函数配置】
使用加权Focal Loss (6分类)
类别权重: ['0.1339', '0.0542', '0.0669', '0.3012', '0.3708', '0.0730']
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: S30
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

S30测试标签: [4, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5, 3]
创建受试者目录: S30
创建日志目录: ./Experiment_for_recognize/MMEW_parallel_conv2_0714_1714_class6_conv2_legm1/S30/logs
正在初始化 风车形卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用风车形卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用风车形卷积
  - 在中间卷积层(conv2_L, conv2_S)中使用风车形卷积
  - 增强微表情细节特征提取能力
  - 扩大感受野范围，更好匹配微表情空间分布
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共910个epoch...

Epoch 1/910

训练集准确率: 0.3076

开始验证...
验证集准确率: 0.0526 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.0526

Epoch 2/910

训练集准确率: 0.4443

开始验证...
验证集准确率: 0.0263 | 最佳准确率: 0.0526

Epoch 3/910

训练集准确率: 0.5840

开始验证...
验证集准确率: 0.0263 | 最佳准确率: 0.0526

Epoch 4/910

训练集准确率: 0.6835

开始验证...
验证集准确率: 0.0789 | 最佳准确率: 0.0526
保存最佳模型,准确率: 0.0789

Epoch 5/910

训练集准确率: 0.7236

开始验证...
验证集准确率: 0.4474 | 最佳准确率: 0.0789
保存最佳模型,准确率: 0.4474

Epoch 6/910

训练集准确率: 0.7845

开始验证...
验证集准确率: 0.5526 | 最佳准确率: 0.4474
保存最佳模型,准确率: 0.5526

Epoch 7/910

训练集准确率: 0.8128

开始验证...
验证集准确率: 0.5526 | 最佳准确率: 0.5526
保存最佳模型,准确率: 0.5526

Epoch 8/910

训练集准确率: 0.8678

开始验证...
验证集准确率: 0.6579 | 最佳准确率: 0.5526
保存最佳模型,准确率: 0.6579

Epoch 9/910

训练集准确率: 0.8975

开始验证...
验证集准确率: 0.5789 | 最佳准确率: 0.6579

Epoch 10/910

训练集准确率: 0.9346

开始验证...
验证集准确率: 0.5263 | 最佳准确率: 0.6579

Epoch 11/910

训练集准确率: 0.9376

开始验证...
验证集准确率: 0.4737 | 最佳准确率: 0.6579

Epoch 12/910

训练集准确率: 0.9361

开始验证...
验证集准确率: 0.6053 | 最佳准确率: 0.6579

Epoch 13/910

训练集准确率: 0.9346

开始验证...
验证集准确率: 0.3684 | 最佳准确率: 0.6579

Epoch 14/910

训练集准确率: 0.9495

开始验证...
验证集准确率: 0.7105 | 最佳准确率: 0.6579
保存最佳模型,准确率: 0.7105

Epoch 15/910

训练集准确率: 0.9822

开始验证...
验证集准确率: 0.5789 | 最佳准确率: 0.7105

Epoch 16/910

训练集准确率: 0.9822

开始验证...
验证集准确率: 0.4737 | 最佳准确率: 0.7105

Epoch 17/910

训练集准确率: 0.9733

开始验证...
验证集准确率: 0.5789 | 最佳准确率: 0.7105

Epoch 18/910

训练集准确率: 0.9703

开始验证...
验证集准确率: 0.6316 | 最佳准确率: 0.7105

Epoch 19/910

训练集准确率: 0.9777

开始验证...
验证集准确率: 0.6842 | 最佳准确率: 0.7105

Epoch 20/910

训练集准确率: 0.9658

开始验证...
验证集准确率: 0.6842 | 最佳准确率: 0.7105

Epoch 21/910

训练集准确率: 0.9866

开始验证...
验证集准确率: 0.6053 | 最佳准确率: 0.7105

Epoch 22/910

训练集准确率: 0.9851

开始验证...
验证集准确率: 0.5789 | 最佳准确率: 0.7105

Epoch 23/910

训练集准确率: 0.9881

开始验证...
验证集准确率: 0.6316 | 最佳准确率: 0.7105

Epoch 24/910

训练集准确率: 0.9792

开始验证...
验证集准确率: 0.5000 | 最佳准确率: 0.7105

Epoch 25/910

训练集准确率: 0.9881

开始验证...
验证集准确率: 0.7105 | 最佳准确率: 0.7105
保存最佳模型,准确率: 0.7105

Epoch 26/910

训练集准确率: 0.9777

开始验证...
验证集准确率: 0.6316 | 最佳准确率: 0.7105

Epoch 27/910

训练集准确率: 0.9926

开始验证...
验证集准确率: 0.7105 | 最佳准确率: 0.7105
保存最佳模型,准确率: 0.7105

Epoch 28/910

训练集准确率: 0.9822

开始验证...
验证集准确率: 0.3947 | 最佳准确率: 0.7105

Epoch 29/910

训练集准确率: 0.9837

开始验证...
验证集准确率: 0.6842 | 最佳准确率: 0.7105

Epoch 30/910

训练集准确率: 0.9673

开始验证...
验证集准确率: 0.5263 | 最佳准确率: 0.7105

Epoch 31/910

训练集准确率: 0.9777

开始验证...
验证集准确率: 0.6579 | 最佳准确率: 0.7105

Epoch 32/910

训练集准确率: 0.9911

开始验证...
验证集准确率: 0.6842 | 最佳准确率: 0.7105

Epoch 33/910

训练集准确率: 0.9777

开始验证...
验证集准确率: 0.5789 | 最佳准确率: 0.7105

Epoch 34/910

训练集准确率: 0.9762

开始验证...
验证集准确率: 0.6053 | 最佳准确率: 0.7105

Epoch 35/910

训练集准确率: 0.9866

开始验证...
验证集准确率: 0.6579 | 最佳准确率: 0.7105

Epoch 36/910

训练集准确率: 0.9866

开始验证...
验证集准确率: 0.7105 | 最佳准确率: 0.7105
保存最佳模型,准确率: 0.7105

Epoch 37/910

训练集准确率: 0.9896

开始验证...
验证集准确率: 0.6053 | 最佳准确率: 0.7105

Epoch 38/910

训练集准确率: 0.9614

开始验证...
验证集准确率: 0.6579 | 最佳准确率: 0.7105

Epoch 39/910

训练集准确率: 0.9747

开始验证...
验证集准确率: 0.5526 | 最佳准确率: 0.7105

Epoch 40/910

训练集准确率: 0.9733

开始验证...
验证集准确率: 0.5789 | 最佳准确率: 0.7105

Epoch 41/910

训练集准确率: 0.9733

开始验证...
验证集准确率: 0.6579 | 最佳准确率: 0.7105

Epoch 42/910

训练集准确率: 0.9747

开始验证...
验证集准确率: 0.6053 | 最佳准确率: 0.7105

Epoch 43/910

训练集准确率: 0.9822

开始验证...
验证集准确率: 0.6053 | 最佳准确率: 0.7105

Epoch 44/910
