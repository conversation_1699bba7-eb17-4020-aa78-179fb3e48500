日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (3分类)[0m
[1;96m🎯 类别权重: ['0.3794', '0.1349', '0.4857'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub17测试标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_bior2.2_L1_eca1/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_bior2.2_L1_eca1/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=1
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
⚡ 使用轻量化ECA注意力机制 - 超低参数高效特征选择
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 1)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用跨分支交互[0m
  [96m- 保持原始三分支独立处理[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡
  [96m- 传统架构，稳定可靠[0m 🔧

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...
正在关闭TensorBoard写入器...
TensorBoard写入器已关闭

训练过程出错: Error(s) in loading state_dict for SKD_TSTSAN:
	size mismatch for ECA1.conv.weight: copying a param with shape torch.Size([1, 1, 3]) from checkpoint, the shape in current model is torch.Size([1, 1, 7]).
	size mismatch for ECA2.conv.weight: copying a param with shape torch.Size([1, 1, 3]) from checkpoint, the shape in current model is torch.Size([1, 1, 7]).
	size mismatch for ECA3.conv.weight: copying a param with shape torch.Size([1, 1, 3]) from checkpoint, the shape in current model is torch.Size([1, 1, 7]).
	size mismatch for ECA4.conv.weight: copying a param with shape torch.Size([1, 1, 5]) from checkpoint, the shape in current model is torch.Size([1, 1, 7]).
	size mismatch for ECA5.conv.weight: copying a param with shape torch.Size([1, 1, 5]) from checkpoint, the shape in current model is torch.Size([1, 1, 7]).
	size mismatch for AC1_ECA1.conv.weight: copying a param with shape torch.Size([1, 1, 5]) from checkpoint, the shape in current model is torch.Size([1, 1, 7]).
	size mismatch for AC1_ECA2.conv.weight: copying a param with shape torch.Size([1, 1, 5]) from checkpoint, the shape in current model is torch.Size([1, 1, 7]).
	size mismatch for AC2_ECA1.conv.weight: copying a param with shape torch.Size([1, 1, 5]) from checkpoint, the shape in current model is torch.Size([1, 1, 7]).
	size mismatch for AC2_ECA2.conv.weight: copying a param with shape torch.Size([1, 1, 5]) from checkpoint, the shape in current model is torch.Size([1, 1, 7]).
详细错误信息: Traceback (most recent call last):
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSANCASME2.py", line 1060, in <module>
    results_dict = main_SKD_TSTSAN_with_Aug_with_SKD(config)
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN_functions_CASME2.py", line 1634, in main_SKD_TSTSAN_with_Aug_with_SKD
    model.load_state_dict(filtered_dict, strict=False)
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/nn/modules/module.py", line 2215, in load_state_dict
    raise RuntimeError('Error(s) in loading state_dict for {}:\n\t{}'.format(
RuntimeError: Error(s) in loading state_dict for SKD_TSTSAN:
	size mismatch for ECA1.conv.weight: copying a param with shape torch.Size([1, 1, 3]) from checkpoint, the shape in current model is torch.Size([1, 1, 7]).
	size mismatch for ECA2.conv.weight: copying a param with shape torch.Size([1, 1, 3]) from checkpoint, the shape in current model is torch.Size([1, 1, 7]).
	size mismatch for ECA3.conv.weight: copying a param with shape torch.Size([1, 1, 3]) from checkpoint, the shape in current model is torch.Size([1, 1, 7]).
	size mismatch for ECA4.conv.weight: copying a param with shape torch.Size([1, 1, 5]) from checkpoint, the shape in current model is torch.Size([1, 1, 7]).
	size mismatch for ECA5.conv.weight: copying a param with shape torch.Size([1, 1, 5]) from checkpoint, the shape in current model is torch.Size([1, 1, 7]).
	size mismatch for AC1_ECA1.conv.weight: copying a param with shape torch.Size([1, 1, 5]) from checkpoint, the shape in current model is torch.Size([1, 1, 7]).
	size mismatch for AC1_ECA2.conv.weight: copying a param with shape torch.Size([1, 1, 5]) from checkpoint, the shape in current model is torch.Size([1, 1, 7]).
	size mismatch for AC2_ECA1.conv.weight: copying a param with shape torch.Size([1, 1, 5]) from checkpoint, the shape in current model is torch.Size([1, 1, 7]).
	size mismatch for AC2_ECA2.conv.weight: copying a param with shape torch.Size([1, 1, 5]) from checkpoint, the shape in current model is torch.Size([1, 1, 7]).


【实验结果报告】
================================================================================

================================================================================
【实验结果报告 - 3分类 (正性、负性、惊讶)】
================================================================================

【基本信息】
实验名称: SKD_TSTSAN_CASME2_class3_full_conv3_bior2.2_L1_eca1
分类方案: 3分类 (正性、负性、惊讶)
时间: 2025-07-20 20:50:58
总训练时间: 00:00:01
数据集: CASME2_LOSO_full


【系统环境】
操作系统: 💻 Linux 6.8.0-1030-nvidia
处理器: ⚡ 32核心处理器
内存: 🧠 63.3GB RAM
GPU: 🚀 NVIDIA A800 80GB PCIe (79.3GB)
GPU数量: 1
训练模式: 单设备

【模型配置】
模型架构: SKD_TSTSAN
分类数量: 3
是否使用预训练: 是
预训练模型: SKD-TSTSAN.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
基础批次大小: 32
最大迭代次数: 20000
损失函数: FocalLoss_weighted
随机种子: 1337

【GPU性能配置】
CUDNN Benchmark: 禁用
CUDNN Deterministic: 启用
混合精度训练: 禁用
TF32加速: 启用
BatchNorm修复: 禁用
卷积类型: 小波变换卷积

【数据增强配置】
使用训练数据增强: 是
使用测试数据增强: 是
旋转角度范围: 3,8
训练增强倍数: 9,3,10
测试增强倍数: 9,3,10
测试数据镜像训练: 启用
镜像训练受试者: sub02,sub05

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: 0.0000
- UAR分数: 0.0000

【各表情类别准确率】
--------------------------------------------------
- 暂无各类别准确率数据


【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
    平均        nan        nan        nan    

================================================================================

================================================================================

【邮件通知】正在发送训练结果...

【邮件通知】邮件发送成功!
- 发件人: <EMAIL>
- 收件人: <EMAIL>
- 主题: [3分类 (正性、负性、惊讶)实验总结] SKD_TSTSAN_CASME2_class3_full_conv3_bior2.2_L1_eca1 - UF1=0.0000, UAR=0.0000

正在关闭日志系统...
