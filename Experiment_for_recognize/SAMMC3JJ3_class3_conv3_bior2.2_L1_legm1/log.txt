日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[95m──────────────────────────────────────────────────[0m
[1m[95m📊 损失函数配置[0m
[95m──────────────────────────────────────────────────[0m
[92m⚖️ 使用加权Focal Loss (3分类)[0m
[36m🎯 类别权重: ['0.3794', '0.1349', '0.4857'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub17测试标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/logs

[94m──────────────────────────────────────────────────[0m
[1m[94m🤖 模型初始化[0m
[94m──────────────────────────────────────────────────[0m
[36m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=1
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 小波类型: bior2.2 (层数: 1)[0m 🌊
  [36m- 通过小波变换实现多频响应特征提取[0m 🌊
  [36m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [36m- 实现非常大的感受野而不会过度参数化[0m 📡
  [36m- 特别适合微表情的频域特征分析[0m 📊
[33m🚫 未启用LEGM模块[0m
  [36m- 使用标准ECA注意力模块[0m 🔧
  [36m- 保持原始模型结构[0m 📦
  [36m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[92m==============[0m
[1m[92m🎯 🚀 开始训练征程 🚀 🎯[0m
[92m==============[0m

[1m[95m🌟 总共 1112 个epoch的精彩旅程即将开始！[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 1/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.6110[0m

开始验证...
📊 [1m验证集准确率:[0m [92m0.2581[0m | [1m最佳准确率:[0m [93m0.0000[0m
[92m💾 保存最佳模型，准确率: 0.2581[0m
[36m📁 模型保存路径: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 2/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.8624[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.2258[0m | [1m最佳准确率:[0m [93m0.2581[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 3/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9541[0m

开始验证...
📊 [1m验证集准确率:[0m [92m0.4839[0m | [1m最佳准确率:[0m [93m0.2581[0m
[92m💾 保存最佳模型，准确率: 0.4839[0m
[36m📁 模型保存路径: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 4/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9651[0m

开始验证...
📊 [1m验证集准确率:[0m [92m0.6129[0m | [1m最佳准确率:[0m [93m0.4839[0m
[92m💾 保存最佳模型，准确率: 0.6129[0m
[36m📁 模型保存路径: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 5/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9780[0m

开始验证...
📊 [1m验证集准确率:[0m [92m0.6452[0m | [1m最佳准确率:[0m [93m0.6129[0m
[92m💾 保存最佳模型，准确率: 0.6452[0m
[36m📁 模型保存路径: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 6/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9853[0m

开始验证...
📊 [1m验证集准确率:[0m [92m0.7419[0m | [1m最佳准确率:[0m [93m0.6452[0m
[92m💾 保存最佳模型，准确率: 0.7419[0m
[36m📁 模型保存路径: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 7/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9963[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.6129[0m | [1m最佳准确率:[0m [93m0.7419[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 8/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9963[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.5161[0m | [1m最佳准确率:[0m [93m0.7419[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 9/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9908[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.5806[0m | [1m最佳准确率:[0m [93m0.7419[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 10/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7419[0m | [1m最佳准确率:[0m [93m0.7419[0m
[92m💾 保存最佳模型，准确率: 0.7419[0m
[36m📁 模型保存路径: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 11/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9890[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7419[0m | [1m最佳准确率:[0m [93m0.7419[0m
[92m💾 保存最佳模型，准确率: 0.7419[0m
[36m📁 模型保存路径: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 12/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9963[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7419[0m | [1m最佳准确率:[0m [93m0.7419[0m
[92m💾 保存最佳模型，准确率: 0.7419[0m
[36m📁 模型保存路径: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 13/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9945[0m

开始验证...
📊 [1m验证集准确率:[0m [92m0.8387[0m | [1m最佳准确率:[0m [93m0.7419[0m
[92m💾 保存最佳模型，准确率: 0.8387[0m
[36m📁 模型保存路径: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 14/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9982[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7419[0m | [1m最佳准确率:[0m [93m0.8387[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 15/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9963[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.6129[0m | [1m最佳准确率:[0m [93m0.8387[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 16/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9927[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7419[0m | [1m最佳准确率:[0m [93m0.8387[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 17/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9982[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.8387[0m | [1m最佳准确率:[0m [93m0.8387[0m
[92m💾 保存最佳模型，准确率: 0.8387[0m
[36m📁 模型保存路径: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 18/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9982[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.8387[0m | [1m最佳准确率:[0m [93m0.8387[0m
[92m💾 保存最佳模型，准确率: 0.8387[0m
[36m📁 模型保存路径: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 19/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9945[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.8065[0m | [1m最佳准确率:[0m [93m0.8387[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 20/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9945[0m

开始验证...
📊 [1m验证集准确率:[0m [92m0.9032[0m | [1m最佳准确率:[0m [93m0.8387[0m
[92m💾 保存最佳模型，准确率: 0.9032[0m
[36m📁 模型保存路径: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 21/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9982[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7742[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 22/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9945[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.8065[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 23/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9963[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.8387[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 24/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9945[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7419[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 25/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9963[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.8065[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 26/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9982[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7097[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 27/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.8065[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 28/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9982[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7419[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 29/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9927[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.8387[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 30/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9908[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.8387[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 31/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9908[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7419[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 32/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9945[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.6129[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 33/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9927[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7097[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 34/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9982[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7419[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 35/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9982[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7097[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 36/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9927[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7742[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 37/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9945[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7097[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 38/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.6452[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 39/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9982[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7097[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 40/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7097[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 41/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9963[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7742[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 42/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9982[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7419[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 43/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9982[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7742[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 44/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9927[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7742[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 45/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7742[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 46/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9982[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7419[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 47/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9927[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7419[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 48/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9982[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7097[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 49/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9963[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7097[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 50/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9963[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7419[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 51/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9927[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7097[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 52/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9908[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.8387[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 53/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9982[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.8065[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 54/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9963[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.7419[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 55/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9982[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.8387[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 56/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9945[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.8065[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 57/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9963[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.8710[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 58/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.9032[0m | [1m最佳准确率:[0m [93m0.9032[0m
[92m💾 保存最佳模型，准确率: 0.9032[0m
[36m📁 模型保存路径: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 59/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9982[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.8387[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 60/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9945[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.9032[0m | [1m最佳准确率:[0m [93m0.9032[0m
[92m💾 保存最佳模型，准确率: 0.9032[0m
[36m📁 模型保存路径: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/sub17/sub17.pth

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 61/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9982[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.8387[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 62/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9963[0m

开始验证...
📊 [1m验证集准确率:[0m [96m0.8065[0m | [1m最佳准确率:[0m [93m0.9032[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 63/1112[0m
[96m──────────────────────────────────────────────────[0m
[92m🎯 训练集准确率: 0.9963[0m

开始验证...
正在关闭TensorBoard写入器...
TensorBoard写入器已关闭

正在关闭日志系统...
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[95m──────────────────────────────────────────────────[0m
[1m[95m📊 损失函数配置[0m
[95m──────────────────────────────────────────────────[0m
[92m⚖️ 使用加权Focal Loss (5分类)[0m
[36m🎯 类别权重: ['0.3309', '0.0956', '0.5735'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: 011
========================================
组别: 大样本组(>8个测试样本)
正在加载训练数据...
正在加载测试数据...

011测试标签: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0]
创建受试者目录: 011
创建TensorBoard日志目录: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/011/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1/011/logs

[94m──────────────────────────────────────────────────[0m
[1m[94m🤖 模型初始化[0m
[94m──────────────────────────────────────────────────[0m
[36m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=1
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[92m✅ 启用WTConv小波变换卷积功能:[0m
  [96m- 小波类型: bior2.2 (层数: 1)[0m 🌊
  [36m- 通过小波变换实现多频响应特征提取[0m 🌊
  [36m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [36m- 实现非常大的感受野而不会过度参数化[0m 📡
  [36m- 特别适合微表情的频域特征分析[0m 📊
[33m🚫 未启用LEGM模块[0m
  [36m- 使用标准ECA注意力模块[0m 🔧
  [36m- 保持原始模型结构[0m 📦
  [36m- 最快推理速度，最低内存占用[0m ⚡

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[92m==============[0m
[1m[92m🎯 🚀 开始训练征程 🚀 🎯[0m
[92m==============[0m

[1m[95m🌟 总共 1429 个epoch的精彩旅程即将开始！[0m

[96m──────────────────────────────────────────────────[0m
[1m[96m📅 Epoch 1/1429[0m
[96m──────────────────────────────────────────────────[0m
正在关闭TensorBoard写入器...
TensorBoard写入器已关闭

训练过程出错: weight tensor should be defined either for all 5 classes or no classes but got weight tensor of shape: [3]
详细错误信息: Traceback (most recent call last):
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSANSAMM.py", line 961, in <module>
    results_dict = main_SKD_TSTSAN_with_Aug_with_SKD(config)
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN_functions_SAMM.py", line 1664, in main_SKD_TSTSAN_with_Aug_with_SKD
    # 计算各种损失
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN_functions_SAMM.py", line 493, in forward
    logpt = (1 - pt) ** self.gamma * logpt
  File "/home/<USER>/data/miniconda3/envs/cuda121/lib/python3.8/site-packages/torch/nn/functional.py", line 2778, in nll_loss
    return torch._C._nn.nll_loss_nd(input, target, weight, _Reduction.get_enum(reduction), ignore_index)
RuntimeError: weight tensor should be defined either for all 5 classes or no classes but got weight tensor of shape: [3]


【实验结果报告】
================================================================================

================================================================================
【实验结果报告 - 5分类 (快乐、惊讶、厌恶、压抑、其他)】
================================================================================

【基本信息】
实验名称: SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1
分类方案: 5分类 (快乐、惊讶、厌恶、压抑、其他)
时间: 2025-07-17 10:59:15
总训练时间: 00:00:10
数据集: SAMM_LOSO_full


【系统环境】
操作系统: 💻 Linux 6.8.0-1030-nvidia
处理器: ⚡ 32核心处理器
内存: 🧠 63.3GB RAM
GPU: 🚀 NVIDIA A800 80GB PCIe (79.3GB)
GPU数量: 1
训练模式: 单设备

【模型配置】
模型架构: SKD_TSTSAN
分类数量: 5
是否使用预训练: 是
预训练模型: SKD-TSTSAN.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
基础批次大小: 32
最大迭代次数: 20000
损失函数: FocalLoss_weighted
随机种子: 1337

【GPU性能配置】
CUDNN Benchmark: 禁用
CUDNN Deterministic: 启用
混合精度训练: 禁用
TF32加速: 启用
BatchNorm修复: 禁用
卷积类型: 小波变换卷积

【数据增强配置】
使用训练数据增强: 是
使用测试数据增强: 是
旋转角度范围: 3,8
训练增强倍数: 7,2,10
测试增强倍数: 7,2,10
测试数据镜像训练: 启用
镜像训练受试者: sub02,sub05

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: 0.0000
- UAR分数: 0.0000

【各表情类别准确率】
--------------------------------------------------
- 暂无各类别准确率数据


【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
    平均        nan        nan        nan    

================================================================================

================================================================================

【邮件通知】正在发送训练结果...

【邮件通知】邮件发送成功!
- 发件人: <EMAIL>
- 收件人: <EMAIL>
- 主题: [5分类 (快乐、惊讶、厌恶、压抑、其他)实验总结] SAMMC3JJ3_class3_conv3_bior2.2_L1_legm1 - UF1=0.0000, UAR=0.0000

正在关闭日志系统...
