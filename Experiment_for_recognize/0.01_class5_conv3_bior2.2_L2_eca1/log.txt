日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签: [4, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/0.01_class5_conv3_bior2.2_L2_eca1/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/0.01_class5_conv3_bior2.2_L2_eca1/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m✅ 启用标准LEGM模块:[0m
  [96m- 在conv1后增强初期特征提取[0m 🎯
  [96m- 专注于低层纹理和边缘特征捕获[0m 🔍
  [96m- 参数增加约6.5%，性能提升显著[0m 📈
  [1;95m- 推荐选择，性能与效率平衡[0m ⭐

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 1 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.3292[0m (268/814)
   • 中间层1准确率: [96m0.3464[0m (282/814)
   • 中间层2准确率: [96m0.3428[0m (279/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 1 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.0000[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.4412[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/0.01_class5_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/0.01_class5_conv3_bior2.2_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 2/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 2 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.3907[0m (318/814)
   • 中间层1准确率: [96m0.3735[0m (304/814)
   • 中间层2准确率: [96m0.3710[0m (302/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 2 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.4412[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.4412[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/0.01_class5_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/0.01_class5_conv3_bior2.2_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 3/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 3 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.5639[0m (459/814)
   • 中间层1准确率: [96m0.5246[0m (427/814)
   • 中间层2准确率: [96m0.5541[0m (451/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 3 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2941[0m (10/34)
🏆 [1m最佳准确率:[0m [1;93m0.4412[0m
============================================================


[1m[1;96m📅 Epoch 4/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 4 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.6106[0m (497/814)
   • 中间层1准确率: [96m0.6388[0m (520/814)
   • 中间层2准确率: [96m0.6143[0m (500/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 4 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2059[0m (7/34)
🏆 [1m最佳准确率:[0m [1;93m0.4412[0m
============================================================


[1m[1;96m📅 Epoch 5/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 5 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.6450[0m (525/814)
   • 中间层1准确率: [96m0.6437[0m (524/814)
   • 中间层2准确率: [96m0.6413[0m (522/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 5 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.4412[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.4412[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/0.01_class5_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/0.01_class5_conv3_bior2.2_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 6/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 6 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7162[0m (583/814)
   • 中间层1准确率: [96m0.6990[0m (569/814)
   • 中间层2准确率: [96m0.7260[0m (591/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 6 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3235[0m (11/34)
🏆 [1m最佳准确率:[0m [1;93m0.4412[0m
============================================================


[1m[1;96m📅 Epoch 7/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 7 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.6794[0m (553/814)
   • 中间层1准确率: [96m0.7088[0m (577/814)
   • 中间层2准确率: [96m0.6830[0m (556/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 7 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7353[0m (25/34)
🏆 [1m最佳准确率:[0m [1;93m0.4412[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.7353[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/0.01_class5_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/0.01_class5_conv3_bior2.2_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 8/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 8 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7273[0m (592/814)
   • 中间层1准确率: [96m0.7420[0m (604/814)
   • 中间层2准确率: [96m0.7371[0m (600/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 8 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 9/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 9 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7113[0m (579/814)
   • 中间层1准确率: [96m0.7236[0m (589/814)
   • 中间层2准确率: [96m0.7039[0m (573/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 9 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 10/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 10 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7531[0m (613/814)
   • 中间层1准确率: [96m0.7531[0m (613/814)
   • 中间层2准确率: [96m0.7666[0m (624/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 10 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 11/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 11 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7531[0m (613/814)
   • 中间层1准确率: [96m0.7666[0m (624/814)
   • 中间层2准确率: [96m0.7457[0m (607/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 11 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 12/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 12 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7494[0m (610/814)
   • 中间层1准确率: [96m0.7604[0m (619/814)
   • 中间层2准确率: [96m0.7396[0m (602/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 12 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3235[0m (11/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 13/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 13 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7334[0m (597/814)
   • 中间层1准确率: [96m0.7408[0m (603/814)
   • 中间层2准确率: [96m0.7273[0m (592/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 13 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2647[0m (9/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 14/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 14 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7445[0m (606/814)
   • 中间层1准确率: [96m0.7420[0m (604/814)
   • 中间层2准确率: [96m0.7543[0m (614/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 14 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3235[0m (11/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 15/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 15 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7764[0m (632/814)
   • 中间层1准确率: [96m0.7973[0m (649/814)
   • 中间层2准确率: [96m0.7776[0m (633/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 15 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 16/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 16 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7715[0m (628/814)
   • 中间层1准确率: [96m0.7506[0m (611/814)
   • 中间层2准确率: [96m0.7715[0m (628/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 16 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 17/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 17 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7998[0m (651/814)
   • 中间层1准确率: [96m0.7912[0m (644/814)
   • 中间层2准确率: [96m0.8010[0m (652/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 17 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3235[0m (11/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 18/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 18 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8022[0m (653/814)
   • 中间层1准确率: [96m0.7678[0m (625/814)
   • 中间层2准确率: [96m0.7998[0m (651/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 18 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.0882[0m (3/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 19/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 19 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7678[0m (625/814)
   • 中间层1准确率: [96m0.7531[0m (613/814)
   • 中间层2准确率: [96m0.7604[0m (619/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 19 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 20/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 20 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7826[0m (637/814)
   • 中间层1准确率: [96m0.7703[0m (627/814)
   • 中间层2准确率: [96m0.7789[0m (634/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 20 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3235[0m (11/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 21/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 21 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7838[0m (638/814)
   • 中间层1准确率: [96m0.7789[0m (634/814)
   • 中间层2准确率: [96m0.7985[0m (650/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 21 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2941[0m (10/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 22/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 22 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7740[0m (630/814)
   • 中间层1准确率: [96m0.7752[0m (631/814)
   • 中间层2准确率: [96m0.7801[0m (635/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 22 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 23/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 23 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7887[0m (642/814)
   • 中间层1准确率: [96m0.7580[0m (617/814)
   • 中间层2准确率: [96m0.7776[0m (633/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 23 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 24/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 24 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7899[0m (643/814)
   • 中间层1准确率: [96m0.7666[0m (624/814)
   • 中间层2准确率: [96m0.7826[0m (637/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 24 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.1471[0m (5/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 25/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 25 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7727[0m (629/814)
   • 中间层1准确率: [96m0.7776[0m (633/814)
   • 中间层2准确率: [96m0.7875[0m (641/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 25 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 26/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 26 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8096[0m (659/814)
   • 中间层1准确率: [96m0.8022[0m (653/814)
   • 中间层2准确率: [96m0.8084[0m (658/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 26 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2353[0m (8/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 27/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 27 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8489[0m (691/814)
   • 中间层1准确率: [96m0.8145[0m (663/814)
   • 中间层2准确率: [96m0.8489[0m (691/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 27 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.1471[0m (5/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 28/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 28 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8292[0m (675/814)
   • 中间层1准确率: [96m0.8120[0m (661/814)
   • 中间层2准确率: [96m0.8182[0m (666/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 28 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.0588[0m (2/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 29/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 29 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8342[0m (679/814)
   • 中间层1准确率: [96m0.7985[0m (650/814)
   • 中间层2准确率: [96m0.8182[0m (666/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 29 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.1176[0m (4/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 30/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 30 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7469[0m (608/814)
   • 中间层1准确率: [96m0.7236[0m (589/814)
   • 中间层2准确率: [96m0.7482[0m (609/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 30 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 31/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 31 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7887[0m (642/814)
   • 中间层1准确率: [96m0.7776[0m (633/814)
   • 中间层2准确率: [96m0.7715[0m (628/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 31 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 32/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 32 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8034[0m (654/814)
   • 中间层1准确率: [96m0.8145[0m (663/814)
   • 中间层2准确率: [96m0.8022[0m (653/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 32 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 33/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 33 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8133[0m (662/814)
   • 中间层1准确率: [96m0.8206[0m (668/814)
   • 中间层2准确率: [96m0.8256[0m (672/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 33 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 34/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 34 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8145[0m (663/814)
   • 中间层1准确率: [96m0.8378[0m (682/814)
   • 中间层2准确率: [96m0.8243[0m (671/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 34 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 35/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 35 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8342[0m (679/814)
   • 中间层1准确率: [96m0.8624[0m (702/814)
   • 中间层2准确率: [96m0.8391[0m (683/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 35 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 36/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 36 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8538[0m (695/814)
   • 中间层1准确率: [96m0.8391[0m (683/814)
   • 中间层2准确率: [96m0.8464[0m (689/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 36 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 37/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 37 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8391[0m (683/814)
   • 中间层1准确率: [96m0.8317[0m (677/814)
   • 中间层2准确率: [96m0.8489[0m (691/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 37 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.1471[0m (5/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 38/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 38 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8170[0m (665/814)
   • 中间层1准确率: [96m0.8047[0m (655/814)
   • 中间层2准确率: [96m0.8096[0m (659/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 38 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 39/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 39 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7973[0m (649/814)
   • 中间层1准确率: [96m0.7961[0m (648/814)
   • 中间层2准确率: [96m0.8010[0m (652/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 39 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2353[0m (8/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 40/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 40 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8243[0m (671/814)
   • 中间层1准确率: [96m0.8256[0m (672/814)
   • 中间层2准确率: [96m0.8378[0m (682/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 40 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 41/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 41 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8649[0m (704/814)
   • 中间层1准确率: [96m0.8354[0m (680/814)
   • 中间层2准确率: [96m0.8563[0m (697/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 41 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 42/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 42 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8378[0m (682/814)
   • 中间层1准确率: [96m0.8354[0m (680/814)
   • 中间层2准确率: [96m0.8526[0m (694/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 42 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3235[0m (11/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 43/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 43 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8661[0m (705/814)
   • 中间层1准确率: [96m0.8464[0m (689/814)
   • 中间层2准确率: [96m0.8428[0m (686/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 43 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.1176[0m (4/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 44/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 44 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8428[0m (686/814)
   • 中间层1准确率: [96m0.8243[0m (671/814)
   • 中间层2准确率: [96m0.8464[0m (689/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 44 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 45/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 45 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7445[0m (606/814)
   • 中间层1准确率: [96m0.7740[0m (630/814)
   • 中间层2准确率: [96m0.7469[0m (608/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 45 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 46/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 46 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8280[0m (674/814)
   • 中间层1准确率: [96m0.8428[0m (686/814)
   • 中间层2准确率: [96m0.8219[0m (669/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 46 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 47/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 47 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8550[0m (696/814)
   • 中间层1准确率: [96m0.8464[0m (689/814)
   • 中间层2准确率: [96m0.8538[0m (695/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 47 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 48/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 48 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8808[0m (717/814)
   • 中间层1准确率: [96m0.8833[0m (719/814)
   • 中间层2准确率: [96m0.8686[0m (707/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 48 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 49/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 49 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8784[0m (715/814)
   • 中间层1准确率: [96m0.8575[0m (698/814)
   • 中间层2准确率: [96m0.8833[0m (719/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 49 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 50/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 50 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8587[0m (699/814)
   • 中间层1准确率: [96m0.8391[0m (683/814)
   • 中间层2准确率: [96m0.8612[0m (701/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 50 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 51/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 51 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8649[0m (704/814)
   • 中间层1准确率: [96m0.8194[0m (667/814)
   • 中间层2准确率: [96m0.8538[0m (695/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 51 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 52/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 52 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7850[0m (639/814)
   • 中间层1准确率: [96m0.7727[0m (629/814)
   • 中间层2准确率: [96m0.7875[0m (641/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 52 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 53/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 53 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7948[0m (647/814)
   • 中间层1准确率: [96m0.8157[0m (664/814)
   • 中间层2准确率: [96m0.7998[0m (651/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 53 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 54/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 54 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8550[0m (696/814)
   • 中间层1准确率: [96m0.8292[0m (675/814)
   • 中间层2准确率: [96m0.8489[0m (691/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 54 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 55/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 55 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8575[0m (698/814)
   • 中间层1准确率: [96m0.8514[0m (693/814)
   • 中间层2准确率: [96m0.8575[0m (698/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 55 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 56/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 56 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8943[0m (728/814)
   • 中间层1准确率: [96m0.8612[0m (701/814)
   • 中间层2准确率: [96m0.8857[0m (721/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 56 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 57/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 57 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9079[0m (739/814)
   • 中间层1准确率: [96m0.8686[0m (707/814)
   • 中间层2准确率: [96m0.9054[0m (737/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 57 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 58/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 58 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8686[0m (707/814)
   • 中间层1准确率: [96m0.8415[0m (685/814)
   • 中间层2准确率: [96m0.8477[0m (690/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 58 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 59/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 59 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8022[0m (653/814)
   • 中间层1准确率: [96m0.8280[0m (674/814)
   • 中间层2准确率: [96m0.8059[0m (656/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 59 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 60/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 60 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8403[0m (684/814)
   • 中间层1准确率: [96m0.8600[0m (700/814)
   • 中间层2准确率: [96m0.8477[0m (690/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 60 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 61/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 61 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8698[0m (708/814)
   • 中间层1准确率: [96m0.8833[0m (719/814)
   • 中间层2准确率: [96m0.8759[0m (713/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 61 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 62/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 62 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8514[0m (693/814)
   • 中间层1准确率: [96m0.8698[0m (708/814)
   • 中间层2准确率: [96m0.8587[0m (699/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 62 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 63/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 63 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8182[0m (666/814)
   • 中间层1准确率: [96m0.8194[0m (667/814)
   • 中间层2准确率: [96m0.8108[0m (660/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 63 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 64/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 64 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8084[0m (658/814)
   • 中间层1准确率: [96m0.8243[0m (671/814)
   • 中间层2准确率: [96m0.8256[0m (672/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 64 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 65/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 65 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8698[0m (708/814)
   • 中间层1准确率: [96m0.8477[0m (690/814)
   • 中间层2准确率: [96m0.8649[0m (704/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 65 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 66/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 66 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8698[0m (708/814)
   • 中间层1准确率: [96m0.8722[0m (710/814)
   • 中间层2准确率: [96m0.8722[0m (710/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 66 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 67/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 67 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8931[0m (727/814)
   • 中间层1准确率: [96m0.8931[0m (727/814)
   • 中间层2准确率: [96m0.8956[0m (729/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 67 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2059[0m (7/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 68/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 68 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8833[0m (719/814)
   • 中间层1准确率: [96m0.8735[0m (711/814)
   • 中间层2准确率: [96m0.9017[0m (734/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 68 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 69/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 69 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8845[0m (720/814)
   • 中间层1准确率: [96m0.8489[0m (691/814)
   • 中间层2准确率: [96m0.8907[0m (725/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 69 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 70/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 70 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8649[0m (704/814)
   • 中间层1准确率: [96m0.8415[0m (685/814)
   • 中间层2准确率: [96m0.8661[0m (705/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 70 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 71/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 71 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8391[0m (683/814)
   • 中间层1准确率: [96m0.8366[0m (681/814)
   • 中间层2准确率: [96m0.8354[0m (680/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 71 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 72/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 72 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8759[0m (713/814)
   • 中间层1准确率: [96m0.8735[0m (711/814)
   • 中间层2准确率: [96m0.8771[0m (714/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 72 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 73/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 73 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8808[0m (717/814)
   • 中间层1准确率: [96m0.8526[0m (694/814)
   • 中间层2准确率: [96m0.8710[0m (709/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 73 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 74/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 74 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7826[0m (637/814)
   • 中间层1准确率: [96m0.7764[0m (632/814)
   • 中间层2准确率: [96m0.7948[0m (647/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 74 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 75/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 75 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8292[0m (675/814)
   • 中间层1准确率: [96m0.8292[0m (675/814)
   • 中间层2准确率: [96m0.8133[0m (662/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 75 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 76/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 76 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8329[0m (678/814)
   • 中间层1准确率: [96m0.8477[0m (690/814)
   • 中间层2准确率: [96m0.8317[0m (677/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 76 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 77/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 77 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8391[0m (683/814)
   • 中间层1准确率: [96m0.8133[0m (662/814)
   • 中间层2准确率: [96m0.8305[0m (676/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 77 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3235[0m (11/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 78/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 78 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8403[0m (684/814)
   • 中间层1准确率: [96m0.8440[0m (687/814)
   • 中间层2准确率: [96m0.8415[0m (685/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 78 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2353[0m (8/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 79/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 79 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8452[0m (688/814)
   • 中间层1准确率: [96m0.8575[0m (698/814)
   • 中间层2准确率: [96m0.8403[0m (684/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 79 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 80/770[0m
[1;96m----------------[0m
