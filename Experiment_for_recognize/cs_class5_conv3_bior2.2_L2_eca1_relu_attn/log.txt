日志系统初始化成功

================================================================================
【训练开始 - GPU预加载 + 并行训练模式】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...

[1m[1;94m⚙️ 训练模式配置[0m
[1;94m----------[0m
[1;96mℹ️ GPU数据预加载: ✅ 启用[0m
[1;96mℹ️ 并行训练: ✅ 启用[0m
[1;96mℹ️ 并行工作线程数: 4[0m

[1m[1;96m🚀 GPU数据预加载器初始化[0m
[1;96m----------------[0m
[1;96mℹ️ 数据集路径: /home/<USER>/data/ajq/SKD-TSTSAN-new/CASME2_LOSO_full[0m
[1;96mℹ️ 分类数量: 5[0m
[1;96mℹ️ 目标设备: cuda[0m

[1;92m========================[0m
[1m[1;92m🚀 开始预加载CASME2数据集到GPU显存 🚀[0m
[1;92m========================[0m

[1;96m📊 正在估算数据集显存需求...[0m
[1;92m📈 估算结果: 6344 个样本[0m
[1;92m💾 预计显存需求: 2.07 GB[0m
[1;96mℹ️ GPU总显存: 79.25 GB[0m
[1;96mℹ️ 可用显存: 63.40 GB[0m
[1;96mℹ️ 发现 26 个受试者: ['sub01', 'sub02', 'sub03', 'sub04', 'sub05', 'sub06', 'sub07', 'sub08', 'sub09', 'sub10', 'sub11', 'sub12', 'sub13', 'sub14', 'sub15', 'sub16', 'sub17', 'sub18', 'sub19', 'sub20', 'sub21', 'sub22', 'sub23', 'sub24', 'sub25', 'sub26'][0m
[1;96m📊 预加载进度: 1/26 - sub01[0m
[1;96m📥 正在预加载受试者 sub01 的数据...[0m
[1;92m✅ 受试者 sub01 数据预加载完成[0m
[1;96mℹ️ 训练样本: 235, 测试样本: 9[0m
[1;96m💾 当前显存使用: 0.08 GB[0m
[1;96m📊 预加载进度: 2/26 - sub02[0m
[1;96m📥 正在预加载受试者 sub02 的数据...[0m
[1;92m✅ 受试者 sub02 数据预加载完成[0m
[1;96mℹ️ 训练样本: 231, 测试样本: 13[0m
[1;96m💾 当前显存使用: 0.16 GB[0m
[1;96m📊 预加载进度: 3/26 - sub03[0m
[1;96m📥 正在预加载受试者 sub03 的数据...[0m
[1;92m✅ 受试者 sub03 数据预加载完成[0m
[1;96mℹ️ 训练样本: 237, 测试样本: 7[0m
[1;96m💾 当前显存使用: 0.24 GB[0m
[1;96m📊 预加载进度: 4/26 - sub04[0m
[1;96m📥 正在预加载受试者 sub04 的数据...[0m
[1;92m✅ 受试者 sub04 数据预加载完成[0m
[1;96mℹ️ 训练样本: 240, 测试样本: 4[0m
[1;96m💾 当前显存使用: 0.32 GB[0m
[1;96m📊 预加载进度: 5/26 - sub05[0m
[1;96m📥 正在预加载受试者 sub05 的数据...[0m
[1;92m✅ 受试者 sub05 数据预加载完成[0m
[1;96mℹ️ 训练样本: 225, 测试样本: 19[0m
[1;96m💾 当前显存使用: 0.40 GB[0m
[1;96m📊 预加载进度: 6/26 - sub06[0m
[1;96m📥 正在预加载受试者 sub06 的数据...[0m
[1;92m✅ 受试者 sub06 数据预加载完成[0m
[1;96mℹ️ 训练样本: 239, 测试样本: 5[0m
[1;96m💾 当前显存使用: 0.48 GB[0m
[1;96m📊 预加载进度: 7/26 - sub07[0m
[1;96m📥 正在预加载受试者 sub07 的数据...[0m
[1;92m✅ 受试者 sub07 数据预加载完成[0m
[1;96mℹ️ 训练样本: 235, 测试样本: 9[0m
[1;96m💾 当前显存使用: 0.56 GB[0m
[1;96m📊 预加载进度: 8/26 - sub08[0m
[1;96m📥 正在预加载受试者 sub08 的数据...[0m
[1;92m✅ 受试者 sub08 数据预加载完成[0m
[1;96mℹ️ 训练样本: 241, 测试样本: 3[0m
[1;96m💾 当前显存使用: 0.64 GB[0m
[1;96m📊 预加载进度: 9/26 - sub09[0m
[1;96m📥 正在预加载受试者 sub09 的数据...[0m
[1;92m✅ 受试者 sub09 数据预加载完成[0m
[1;96mℹ️ 训练样本: 231, 测试样本: 13[0m
[1;96m💾 当前显存使用: 0.72 GB[0m
[1;96m📊 预加载进度: 10/26 - sub10[0m
[1;96m📥 正在预加载受试者 sub10 的数据...[0m
[1;92m✅ 受试者 sub10 数据预加载完成[0m
[1;96mℹ️ 训练样本: 231, 测试样本: 13[0m
[1;96m💾 当前显存使用: 0.80 GB[0m
[1;96m📊 预加载进度: 11/26 - sub11[0m
[1;96m📥 正在预加载受试者 sub11 的数据...[0m
[1;92m✅ 受试者 sub11 数据预加载完成[0m
[1;96mℹ️ 训练样本: 234, 测试样本: 10[0m
[1;96m💾 当前显存使用: 0.88 GB[0m
[1;96m📊 预加载进度: 12/26 - sub12[0m
[1;96m📥 正在预加载受试者 sub12 的数据...[0m
[1;92m✅ 受试者 sub12 数据预加载完成[0m
[1;96mℹ️ 训练样本: 232, 测试样本: 12[0m
[1;96m💾 当前显存使用: 0.96 GB[0m
[1;96m📊 预加载进度: 13/26 - sub13[0m
[1;96m📥 正在预加载受试者 sub13 的数据...[0m
[1;92m✅ 受试者 sub13 数据预加载完成[0m
[1;96mℹ️ 训练样本: 236, 测试样本: 8[0m
[1;96m💾 当前显存使用: 1.04 GB[0m
[1;96m📊 预加载进度: 14/26 - sub14[0m
[1;96m📥 正在预加载受试者 sub14 的数据...[0m
[1;92m✅ 受试者 sub14 数据预加载完成[0m
[1;96mℹ️ 训练样本: 240, 测试样本: 4[0m
[1;96m💾 当前显存使用: 1.12 GB[0m
[1;96m📊 预加载进度: 15/26 - sub15[0m
[1;96m📥 正在预加载受试者 sub15 的数据...[0m
[1;92m✅ 受试者 sub15 数据预加载完成[0m
[1;96mℹ️ 训练样本: 241, 测试样本: 3[0m
[1;96m💾 当前显存使用: 1.20 GB[0m
[1;96m📊 预加载进度: 16/26 - sub16[0m
[1;96m📥 正在预加载受试者 sub16 的数据...[0m
[1;92m✅ 受试者 sub16 数据预加载完成[0m
[1;96mℹ️ 训练样本: 241, 测试样本: 3[0m
[1;96m💾 当前显存使用: 1.28 GB[0m
[1;96m📊 预加载进度: 17/26 - sub17[0m
[1;96m📥 正在预加载受试者 sub17 的数据...[0m
[1;92m✅ 受试者 sub17 数据预加载完成[0m
[1;96mℹ️ 训练样本: 210, 测试样本: 34[0m
[1;96m💾 当前显存使用: 1.36 GB[0m
[1;96m📊 预加载进度: 18/26 - sub18[0m
[1;96m📥 正在预加载受试者 sub18 的数据...[0m
[1;92m✅ 受试者 sub18 数据预加载完成[0m
[1;96mℹ️ 训练样本: 241, 测试样本: 3[0m
[1;96m💾 当前显存使用: 1.44 GB[0m
[1;96m📊 预加载进度: 19/26 - sub19[0m
[1;96m📥 正在预加载受试者 sub19 的数据...[0m
[1;92m✅ 受试者 sub19 数据预加载完成[0m
[1;96mℹ️ 训练样本: 229, 测试样本: 15[0m
[1;96m💾 当前显存使用: 1.52 GB[0m
[1;96m📊 预加载进度: 20/26 - sub20[0m
[1;96m📥 正在预加载受试者 sub20 的数据...[0m
[1;92m✅ 受试者 sub20 数据预加载完成[0m
[1;96mℹ️ 训练样本: 233, 测试样本: 11[0m
[1;96m💾 当前显存使用: 1.60 GB[0m
[1;96m📊 预加载进度: 21/26 - sub21[0m
[1;96m📥 正在预加载受试者 sub21 的数据...[0m
[1;92m✅ 受试者 sub21 数据预加载完成[0m
[1;96mℹ️ 训练样本: 242, 测试样本: 2[0m
[1;96m💾 当前显存使用: 1.68 GB[0m
[1;96m📊 预加载进度: 22/26 - sub22[0m
[1;96m📥 正在预加载受试者 sub22 的数据...[0m
[1;92m✅ 受试者 sub22 数据预加载完成[0m
[1;96mℹ️ 训练样本: 242, 测试样本: 2[0m
[1;96m💾 当前显存使用: 1.76 GB[0m
[1;96m📊 预加载进度: 23/26 - sub23[0m
[1;96m📥 正在预加载受试者 sub23 的数据...[0m
[1;92m✅ 受试者 sub23 数据预加载完成[0m
[1;96mℹ️ 训练样本: 232, 测试样本: 12[0m
[1;96m💾 当前显存使用: 1.84 GB[0m
[1;96m📊 预加载进度: 24/26 - sub24[0m
[1;96m📥 正在预加载受试者 sub24 的数据...[0m
[1;92m✅ 受试者 sub24 数据预加载完成[0m
[1;96mℹ️ 训练样本: 237, 测试样本: 7[0m
[1;96m💾 当前显存使用: 1.92 GB[0m
[1;96m📊 预加载进度: 25/26 - sub25[0m
[1;96m📥 正在预加载受试者 sub25 的数据...[0m
[1;92m✅ 受试者 sub25 数据预加载完成[0m
[1;96mℹ️ 训练样本: 237, 测试样本: 7[0m
[1;96m💾 当前显存使用: 2.00 GB[0m
[1;96m📊 预加载进度: 26/26 - sub26[0m
[1;96m📥 正在预加载受试者 sub26 的数据...[0m
[1;92m✅ 受试者 sub26 数据预加载完成[0m
[1;96mℹ️ 训练样本: 228, 测试样本: 16[0m
[1;96m💾 当前显存使用: 2.08 GB[0m

[1;92m===========[0m
[1m[1;92m✅ 数据预加载完成 ✅[0m
[1;92m===========[0m

[1;92m✅ 成功预加载: 26 个受试者[0m
[1m[1;95m💾 最终显存使用: 2.08 GB[0m
根据配置,不使用Visdom可视化

[1;95m=====================[0m
[1m[1;95m🚀 使用GPU预加载 + 并行训练模式 🚀[0m
[1;95m=====================[0m

[1;96mℹ️ 可用受试者: 25 个[0m
[1;96mℹ️ 受试者列表: ['sub17', 'sub05', 'sub26', 'sub19', 'sub09', 'sub02', 'sub10', 'sub23', 'sub12', 'sub20', 'sub11', 'sub01', 'sub07', 'sub24', 'sub25', 'sub03', 'sub04', 'sub06', 'sub13', 'sub08', 'sub15', 'sub16', 'sub18', 'sub21', 'sub22'][0m

[1;95m==================[0m
[1m[1;95m⚡ 开始并行训练 25 个受试者 ⚡[0m
[1;95m==================[0m

[1;96mℹ️ 并行工作线程数: 4[0m
[1;96m🎯 开始训练受试者: sub17[0m[1;96m🎯 开始训练受试者: sub05[0m

[1;96m🎯 开始训练受试者: sub26[0m[1;96m🎯 开始训练受试者: sub19[0m[1;96mℹ️ 训练样本: 225, 测试样本: 19[0m


[1;96mℹ️ 训练样本: 228, 测试样本: 16[0m[1;96mℹ️ 训练样本: 229, 测试样本: 15[0m
[1;96mℹ️ 训练样本: 210, 测试样本: 34[0m

[1;96mℹ️ 训练数据形状: torch.Size([229, 38, 48, 48]), 测试数据形状: torch.Size([15, 38, 48, 48])[0m[1;96mℹ️ 训练数据形状: torch.Size([228, 38, 48, 48]), 测试数据形状: torch.Size([16, 38, 48, 48])[0m[1;96mℹ️ 训练数据形状: torch.Size([210, 38, 48, 48]), 测试数据形状: torch.Size([34, 38, 48, 48])[0m

[1;96mℹ️ 训练数据形状: torch.Size([225, 38, 48, 48]), 测试数据形状: torch.Size([19, 38, 48, 48])[0m
🌊 小波配置: 类型=bior2.2, 层数=2🌊 小波配置: 类型=bior2.2, 层数=2


🌊 小波配置: 类型=bior2.2, 层数=2🌊 小波配置: 类型=bior2.2, 层数=2

🔧 激活函数配置: 标准ReLU
🔧 激活函数配置: 标准ReLU
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8=> 使用折叠除法: 8

=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 启用三模态特征融合: attention
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8🔥 启用三模态特征融合: attention

🔥 启用三模态特征融合: attention🔥 启用三模态特征融合: attention

✅ 三模态融合模块初始化完成
✅ 三模态融合模块初始化完成
✅ 三模态融合模块初始化完成
✅ 三模态融合模块初始化完成
[1;96mℹ️ 加载预训练模型...[0m[1;96mℹ️ 加载预训练模型...[0m

[1;96mℹ️ 加载预训练模型...[0m
[1;96mℹ️ 加载预训练模型...[0m
[1;92m✅ 成功加载预训练模型: ck注意力融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_attn_h8.pth[0m
[1;96mℹ️ 加载了 350/356 个层[0m
[1;92m✅ 成功加载预训练模型: ck注意力融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_attn_h8.pth[0m
[1;96mℹ️ 加载了 350/356 个层[0m
[1;92m✅ 成功加载预训练模型: ck注意力融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_attn_h8.pth[0m
[1;96mℹ️ 加载了 350/356 个层[0m[1;92m✅ 成功加载预训练模型: ck注意力融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_attn_h8.pth[0m

[1;96mℹ️ 加载了 350/356 个层[0m
[1;96mℹ️ sub17 - 迭代 100: 训练准确率=1.0000, 测试准确率=0.4706[0m
[1;96mℹ️ sub26 - 迭代 100: 训练准确率=0.9386, 测试准确率=0.4375[0m
[1;96mℹ️ sub05 - 迭代 100: 训练准确率=0.9956, 测试准确率=0.2105[0m
[1;96mℹ️ sub19 - 迭代 100: 训练准确率=0.8690, 测试准确率=0.4000[0m
[1;96mℹ️ sub17 - 迭代 200: 训练准确率=1.0000, 测试准确率=0.4118[0m
[1;96mℹ️ sub19 - 迭代 200: 训练准确率=0.8515, 测试准确率=0.3333[0m
[1;96mℹ️ sub05 - 迭代 200: 训练准确率=0.9689, 测试准确率=0.6842[0m
[1;96mℹ️ sub26 - 迭代 200: 训练准确率=0.9123, 测试准确率=0.3750[0m
[1;96mℹ️ sub17 - 迭代 300: 训练准确率=0.9762, 测试准确率=0.3824[0m
[1;96mℹ️ sub26 - 迭代 300: 训练准确率=0.9386, 测试准确率=0.3125[0m
[1;96mℹ️ sub19 - 迭代 300: 训练准确率=0.9869, 测试准确率=0.6667[0m
[1;96mℹ️ sub05 - 迭代 300: 训练准确率=0.9867, 测试准确率=0.3684[0m
[1;96mℹ️ sub17 - 迭代 400: 训练准确率=0.9905, 测试准确率=0.4118[0m
[1;96mℹ️ sub26 - 迭代 400: 训练准确率=0.9737, 测试准确率=0.3125[0m
[1;96mℹ️ sub19 - 迭代 400: 训练准确率=0.9738, 测试准确率=0.8000[0m
[1;96mℹ️ sub05 - 迭代 400: 训练准确率=0.9911, 测试准确率=0.3158[0m
[1;96mℹ️ sub17 - 迭代 500: 训练准确率=1.0000, 测试准确率=0.4412[0m

正在关闭日志系统...
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签: [4, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/cs_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/cs_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m🔥 启用三模态特征融合:[0m
  [1;96m多头注意力融合[0m 🧠
  [96m- 建立跨模态注意力关系[0m 🔗
  [96m- 动态关注重要特征[0m 🎯
  [1;96m- 注意力头数: 8[0m 🎯
[93m⏰ 使用固定训练轮数[0m

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

[1m[1;92m🚀 GPU数据预加载[0m
[1;92m------------[0m
[1;96m📤 正在将所有数据预加载到GPU内存...[0m
[1;96m📊 训练数据大小: 0.27 GB[0m
[1;96m📊 测试数据大小: 0.06 GB[0m
[1;96m📊 总数据大小: 0.32 GB[0m
[1;96m⬆️ 正在上传训练数据到GPU...[0m
[1;96m⬆️ 正在上传测试数据到GPU...[0m
[1;92m✅ 数据预加载到GPU完成！[0m
[1;96m💾 GPU内存使用: GPU0: 0.3GB/79.3GB (0.4%)[0m
[1;96m⚡ GPU预加载模式：数据传输将极大加速！[0m

[1m[1;96m📊 损失输出配置[0m
[1;96m----------[0m
[1;96m🔍 详细损失信息: 禁用[0m
[1;96m📈 Epoch损失统计: 禁用[0m
[1;96m📉 验证损失统计: 禁用[0m
[1;96m🚨 过拟合监控: 禁用[0m
[1;96m📦 批次损失详情: 禁用[0m
[1;96m❌ Epoch损失统计将显示: 否[0m
[1;96m❌ 验证损失统计将显示: 否[0m
[1;96m❌ 过拟合监控将显示: 否[0m

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 1 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.5233[0m (426/814)
   • 中间层1准确率: [96m0.5000[0m (407/814)
   • 中间层2准确率: [96m0.5344[0m (435/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 1 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.2353[0m (8/34)
🏆 [1m最佳准确率:[0m [1;93m0.0000[0m
📊 [1mUF1:[0m [1;92m0.1512[0m | [1mUAR:[0m [1;92m0.1619[0m
🥇 [1m最佳UF1:[0m [1;93m0.0000[0m | [1m最佳UAR:[0m [1;93m0.0000[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.2353 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.1512 | UAR: 0.1619 | 准确率: 0.2353[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/cs_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/cs_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth

[1m[1;96m📅 Epoch 2/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 2 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7027[0m (572/814)
   • 中间层1准确率: [96m0.6658[0m (542/814)
   • 中间层2准确率: [96m0.7138[0m (581/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 2 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.2353[0m
📊 [1mUF1:[0m [1;92m0.5717[0m | [1mUAR:[0m [1;92m0.5698[0m
🥇 [1m最佳UF1:[0m [1;93m0.1512[0m | [1m最佳UAR:[0m [1;93m0.1619[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.5882 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.5717 | UAR: 0.5698 | 准确率: 0.5882[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/cs_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/cs_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth

[1m[1;96m📅 Epoch 3/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 3 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7285[0m (593/814)
   • 中间层1准确率: [96m0.7396[0m (602/814)
   • 中间层2准确率: [96m0.7666[0m (624/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 3 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.5882[0m
📊 [1mUF1:[0m [1;96m0.3095[0m | [1mUAR:[0m [1;96m0.4571[0m
🥇 [1m最佳UF1:[0m [1;93m0.5717[0m | [1m最佳UAR:[0m [1;93m0.5698[0m
============================================================


[1m[1;96m📅 Epoch 4/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 4 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8059[0m (656/814)
   • 中间层1准确率: [96m0.8170[0m (665/814)
   • 中间层2准确率: [96m0.8231[0m (670/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 4 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.5882[0m
📊 [1mUF1:[0m [1;96m0.3322[0m | [1mUAR:[0m [1;96m0.3841[0m
🥇 [1m最佳UF1:[0m [1;93m0.5717[0m | [1m最佳UAR:[0m [1;93m0.5698[0m
============================================================


[1m[1;96m📅 Epoch 5/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 5 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8219[0m (669/814)
   • 中间层1准确率: [96m0.8317[0m (677/814)
   • 中间层2准确率: [96m0.8219[0m (669/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 5 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.5882[0m
📊 [1mUF1:[0m [1;96m0.2301[0m | [1mUAR:[0m [1;96m0.3000[0m
🥇 [1m最佳UF1:[0m [1;93m0.5717[0m | [1m最佳UAR:[0m [1;93m0.5698[0m
============================================================


[1m[1;96m📅 Epoch 6/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 6 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8538[0m (695/814)
   • 中间层1准确率: [96m0.8636[0m (703/814)
   • 中间层2准确率: [96m0.8894[0m (724/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 6 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.5882[0m
📊 [1mUF1:[0m [1;92m0.6556[0m | [1mUAR:[0m [1;92m0.7413[0m
🥇 [1m最佳UF1:[0m [1;93m0.5717[0m | [1m最佳UAR:[0m [1;93m0.5698[0m
============================================================

[1;92m💾 保存最佳模型，相同准确率下UF1和UAR都提升: UF1(0.6556↑) UAR(0.7413↑)[0m
[1;96m📊 UF1: 0.6556 | UAR: 0.7413 | 准确率: 0.5882[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/cs_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/cs_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth

[1m[1;96m📅 Epoch 7/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 7 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8980[0m (731/814)
   • 中间层1准确率: [96m0.8808[0m (717/814)
   • 中间层2准确率: [96m0.8993[0m (732/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 7 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.5882[0m
📊 [1mUF1:[0m [1;92m0.6891[0m | [1mUAR:[0m [1;92m0.7556[0m
🥇 [1m最佳UF1:[0m [1;93m0.6556[0m | [1m最佳UAR:[0m [1;93m0.7413[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.6765 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.6891 | UAR: 0.7556 | 准确率: 0.6765[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/cs_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/cs_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth

[1m[1;96m📅 Epoch 8/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 8 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9115[0m (742/814)
   • 中间层1准确率: [96m0.8993[0m (732/814)
   • 中间层2准确率: [96m0.9152[0m (745/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 8 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2941[0m (10/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.2291[0m | [1mUAR:[0m [1;96m0.3810[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 9/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 9 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8219[0m (669/814)
   • 中间层1准确率: [96m0.7936[0m (646/814)
   • 中间层2准确率: [96m0.8243[0m (671/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 9 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.4697[0m | [1mUAR:[0m [1;96m0.5683[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 10/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 10 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8464[0m (689/814)
   • 中间层1准确率: [96m0.8219[0m (669/814)
   • 中间层2准确率: [96m0.8428[0m (686/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 10 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.2915[0m | [1mUAR:[0m [1;96m0.3571[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 11/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 11 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8845[0m (720/814)
   • 中间层1准确率: [96m0.8722[0m (710/814)
   • 中间层2准确率: [96m0.8943[0m (728/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 11 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.4083[0m | [1mUAR:[0m [1;96m0.4984[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 12/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 12 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8833[0m (719/814)
   • 中间层1准确率: [96m0.8796[0m (716/814)
   • 中间层2准确率: [96m0.8919[0m (726/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 12 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.4467[0m | [1mUAR:[0m [1;96m0.6476[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 13/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 13 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9287[0m (756/814)
   • 中间层1准确率: [96m0.8907[0m (725/814)
   • 中间层2准确率: [96m0.9140[0m (744/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 13 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.3442[0m | [1mUAR:[0m [1;96m0.4857[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 14/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 14 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9459[0m (770/814)
   • 中间层1准确率: [96m0.9509[0m (774/814)
   • 中间层2准确率: [96m0.9533[0m (776/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 14 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.3894[0m | [1mUAR:[0m [1;96m0.4540[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 15/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 15 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9619[0m (783/814)
   • 中间层1准确率: [96m0.9607[0m (782/814)
   • 中间层2准确率: [96m0.9705[0m (790/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 15 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.4237[0m | [1mUAR:[0m [1;96m0.5286[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 16/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 16 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9570[0m (779/814)
   • 中间层1准确率: [96m0.9607[0m (782/814)
   • 中间层2准确率: [96m0.9619[0m (783/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 16 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.4357[0m | [1mUAR:[0m [1;96m0.6032[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 17/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 17 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9447[0m (769/814)
   • 中间层1准确率: [96m0.9373[0m (763/814)
   • 中间层2准确率: [96m0.9361[0m (762/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 17 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.3536[0m | [1mUAR:[0m [1;96m0.4778[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 18/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 18 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8931[0m (727/814)
   • 中间层1准确率: [96m0.9005[0m (733/814)
   • 中间层2准确率: [96m0.8956[0m (729/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 18 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.6679[0m | [1mUAR:[0m [1;96m0.7333[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 19/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 19 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9201[0m (749/814)
   • 中间层1准确率: [96m0.9349[0m (761/814)
   • 中间层2准确率: [96m0.9287[0m (756/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 19 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.3381[0m | [1mUAR:[0m [1;96m0.3810[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 20/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 20 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9754[0m (794/814)
   • 中间层1准确率: [96m0.9754[0m (794/814)
   • 中间层2准确率: [96m0.9730[0m (792/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 20 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.6069[0m | [1mUAR:[0m [1;92m0.7635[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 21/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 21 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9779[0m (796/814)
   • 中间层1准确率: [96m0.9791[0m (797/814)
   • 中间层2准确率: [96m0.9803[0m (798/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 21 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.6093[0m | [1mUAR:[0m [1;96m0.7143[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 22/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 22 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9459[0m (770/814)
   • 中间层1准确率: [96m0.9238[0m (752/814)
   • 中间层2准确率: [96m0.9398[0m (765/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 22 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.3899[0m | [1mUAR:[0m [1;96m0.5063[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 23/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 23 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9607[0m (782/814)
   • 中间层1准确率: [96m0.9459[0m (770/814)
   • 中间层2准确率: [96m0.9496[0m (773/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 23 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.5686[0m | [1mUAR:[0m [1;96m0.6778[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 24/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 24 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9865[0m (803/814)
   • 中间层2准确率: [96m0.9889[0m (805/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 24 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.5018[0m | [1mUAR:[0m [1;96m0.6270[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 25/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 25 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9865[0m (803/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 25 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.6078[0m | [1mUAR:[0m [1;96m0.7143[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 26/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 26 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9853[0m (802/814)
   • 中间层2准确率: [96m0.9853[0m (802/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 26 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.4735[0m | [1mUAR:[0m [1;96m0.5825[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 27/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 27 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9705[0m (790/814)
   • 中间层1准确率: [96m0.9742[0m (793/814)
   • 中间层2准确率: [96m0.9705[0m (790/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 27 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.4274[0m | [1mUAR:[0m [1;96m0.5206[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 28/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 28 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9840[0m (801/814)
   • 中间层1准确率: [96m0.9828[0m (800/814)
   • 中间层2准确率: [96m0.9865[0m (803/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 28 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.5267[0m | [1mUAR:[0m [1;96m0.5968[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 29/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 29 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9853[0m (802/814)
   • 中间层2准确率: [96m0.9914[0m (807/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 29 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.5294[0m | [1mUAR:[0m [1;96m0.6968[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 30/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 30 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9545[0m (777/814)
   • 中间层1准确率: [96m0.9570[0m (779/814)
   • 中间层2准确率: [96m0.9570[0m (779/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 30 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.4045[0m | [1mUAR:[0m [1;96m0.6365[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 31/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 31 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9472[0m (771/814)
   • 中间层1准确率: [96m0.9312[0m (758/814)
   • 中间层2准确率: [96m0.9484[0m (772/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 31 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.5270[0m | [1mUAR:[0m [1;96m0.6841[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 32/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 32 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9730[0m (792/814)
   • 中间层1准确率: [96m0.9730[0m (792/814)
   • 中间层2准确率: [96m0.9816[0m (799/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 32 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.5405[0m | [1mUAR:[0m [1;96m0.6175[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 33/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 33 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9816[0m (799/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 33 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.6186[0m | [1mUAR:[0m [1;96m0.6968[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 34/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 34 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9681[0m (788/814)
   • 中间层1准确率: [96m0.9619[0m (783/814)
   • 中间层2准确率: [96m0.9681[0m (788/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 34 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.6242[0m | [1mUAR:[0m [1;96m0.7016[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 35/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 35 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9152[0m (745/814)
   • 中间层1准确率: [96m0.9091[0m (740/814)
   • 中间层2准确率: [96m0.9189[0m (748/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 35 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.5952[0m | [1mUAR:[0m [1;96m0.6683[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 36/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 36 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9533[0m (776/814)
   • 中间层1准确率: [96m0.9509[0m (774/814)
   • 中间层2准确率: [96m0.9717[0m (791/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 36 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.3402[0m | [1mUAR:[0m [1;96m0.4333[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 37/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 37 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9828[0m (800/814)
   • 中间层2准确率: [96m0.9840[0m (801/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 37 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.2977[0m | [1mUAR:[0m [1;96m0.3794[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 38/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 38 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9902[0m (806/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 38 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.6067[0m | [1mUAR:[0m [1;96m0.6905[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 39/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 39 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 39 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.6118[0m | [1mUAR:[0m [1;96m0.7413[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 40/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 40 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9828[0m (800/814)
   • 中间层1准确率: [96m0.9730[0m (792/814)
   • 中间层2准确率: [96m0.9816[0m (799/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 40 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.6841[0m | [1mUAR:[0m [1;96m0.7397[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 41/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 41 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9865[0m (803/814)
   • 中间层2准确率: [96m0.9889[0m (805/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 41 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.5353[0m | [1mUAR:[0m [1;96m0.6159[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 42/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 42 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9767[0m (795/814)
   • 中间层1准确率: [96m0.9767[0m (795/814)
   • 中间层2准确率: [96m0.9668[0m (787/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 42 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.5833[0m | [1mUAR:[0m [1;96m0.6603[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 43/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 43 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9742[0m (793/814)
   • 中间层1准确率: [96m0.9730[0m (792/814)
   • 中间层2准确率: [96m0.9779[0m (796/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 43 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.6330[0m | [1mUAR:[0m [1;96m0.6889[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 44/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 44 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9816[0m (799/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9865[0m (803/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 44 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.4139[0m | [1mUAR:[0m [1;96m0.5857[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 45/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 45 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9877[0m (804/814)
   • 中间层1准确率: [96m0.9791[0m (797/814)
   • 中间层2准确率: [96m0.9853[0m (802/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 45 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.4937[0m | [1mUAR:[0m [1;96m0.5952[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 46/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 46 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9644[0m (785/814)
   • 中间层1准确率: [96m0.9631[0m (784/814)
   • 中间层2准确率: [96m0.9693[0m (789/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 46 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.4424[0m | [1mUAR:[0m [1;96m0.5365[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================


[1m[1;96m📅 Epoch 47/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 47 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9779[0m (796/814)
   • 中间层2准确率: [96m0.9865[0m (803/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 47 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.8235[0m (28/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;92m0.8318[0m | [1mUAR:[0m [1;92m0.8905[0m
🥇 [1m最佳UF1:[0m [1;93m0.6891[0m | [1m最佳UAR:[0m [1;93m0.7556[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.8235 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.8318 | UAR: 0.8905 | 准确率: 0.8235[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/cs_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/cs_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth

[1m[1;96m📅 Epoch 48/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 48 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9889[0m (805/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 48 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5923[0m | [1mUAR:[0m [1;96m0.6984[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 49/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 49 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 49 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5373[0m | [1mUAR:[0m [1;96m0.6397[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 50/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 50 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9877[0m (804/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9865[0m (803/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 50 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5998[0m | [1mUAR:[0m [1;96m0.6762[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 51/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 51 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9767[0m (795/814)
   • 中间层1准确率: [96m0.9767[0m (795/814)
   • 中间层2准确率: [96m0.9816[0m (799/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 51 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.6144[0m | [1mUAR:[0m [1;96m0.6905[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 52/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 52 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9840[0m (801/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 52 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.3575[0m | [1mUAR:[0m [1;96m0.4333[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 53/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 53 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9816[0m (799/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 53 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.6081[0m | [1mUAR:[0m [1;96m0.6905[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 54/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 54 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9656[0m (786/814)
   • 中间层1准确率: [96m0.9717[0m (791/814)
   • 中间层2准确率: [96m0.9595[0m (781/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 54 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.6539[0m | [1mUAR:[0m [1;96m0.7175[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 55/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 55 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9754[0m (794/814)
   • 中间层1准确率: [96m0.9705[0m (790/814)
   • 中间层2准确率: [96m0.9754[0m (794/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 55 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2353[0m (8/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.3842[0m | [1mUAR:[0m [1;96m0.5000[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 56/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 56 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9791[0m (797/814)
   • 中间层1准确率: [96m0.9840[0m (801/814)
   • 中间层2准确率: [96m0.9779[0m (796/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 56 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4382[0m | [1mUAR:[0m [1;96m0.6190[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 57/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 57 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9705[0m (790/814)
   • 中间层1准确率: [96m0.9791[0m (797/814)
   • 中间层2准确率: [96m0.9717[0m (791/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 57 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.6774[0m | [1mUAR:[0m [1;96m0.7540[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 58/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 58 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9423[0m (767/814)
   • 中间层1准确率: [96m0.9533[0m (776/814)
   • 中间层2准确率: [96m0.9521[0m (775/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 58 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.2922[0m | [1mUAR:[0m [1;96m0.3810[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 59/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 59 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9619[0m (783/814)
   • 中间层1准确率: [96m0.9742[0m (793/814)
   • 中间层2准确率: [96m0.9681[0m (788/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 59 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5574[0m | [1mUAR:[0m [1;96m0.6476[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 60/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 60 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9840[0m (801/814)
   • 中间层1准确率: [96m0.9902[0m (806/814)
   • 中间层2准确率: [96m0.9853[0m (802/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 60 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5607[0m | [1mUAR:[0m [1;96m0.6159[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 61/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 61 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 61 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.3184[0m | [1mUAR:[0m [1;96m0.3873[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 62/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 62 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 62 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5916[0m | [1mUAR:[0m [1;96m0.6587[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 63/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 63 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 63 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5359[0m | [1mUAR:[0m [1;96m0.6016[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 64/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 64 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 64 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5584[0m | [1mUAR:[0m [1;96m0.6365[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 65/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 65 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 65 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5772[0m | [1mUAR:[0m [1;96m0.6381[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 66/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 66 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 66 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5405[0m | [1mUAR:[0m [1;96m0.6175[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 67/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 67 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 67 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5056[0m | [1mUAR:[0m [1;96m0.5794[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 68/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 68 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 68 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4689[0m | [1mUAR:[0m [1;96m0.5127[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 69/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 69 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9889[0m (805/814)
   • 中间层1准确率: [96m0.9803[0m (798/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 69 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5300[0m | [1mUAR:[0m [1;96m0.6190[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 70/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 70 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9312[0m (758/814)
   • 中间层1准确率: [96m0.9398[0m (765/814)
   • 中间层2准确率: [96m0.9238[0m (752/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 70 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.2945[0m | [1mUAR:[0m [1;96m0.3476[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 71/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 71 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9337[0m (760/814)
   • 中间层1准确率: [96m0.9263[0m (754/814)
   • 中间层2准确率: [96m0.9226[0m (751/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 71 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2941[0m (10/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.3034[0m | [1mUAR:[0m [1;96m0.5238[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 72/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 72 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9803[0m (798/814)
   • 中间层1准确率: [96m0.9791[0m (797/814)
   • 中间层2准确率: [96m0.9767[0m (795/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 72 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.6524[0m | [1mUAR:[0m [1;96m0.6667[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 73/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 73 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9828[0m (800/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9865[0m (803/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 73 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.3672[0m | [1mUAR:[0m [1;96m0.4476[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 74/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 74 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9877[0m (804/814)
   • 中间层1准确率: [96m0.9853[0m (802/814)
   • 中间层2准确率: [96m0.9865[0m (803/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 74 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4357[0m | [1mUAR:[0m [1;96m0.5270[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 75/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 75 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 75 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4065[0m | [1mUAR:[0m [1;96m0.4905[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 76/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 76 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 76 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4639[0m | [1mUAR:[0m [1;96m0.6317[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 77/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 77 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 77 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4736[0m | [1mUAR:[0m [1;96m0.6333[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 78/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 78 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9889[0m (805/814)
   • 中间层2准确率: [96m0.9816[0m (799/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 78 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.6338[0m | [1mUAR:[0m [1;96m0.7048[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 79/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 79 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9840[0m (801/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 79 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.6721[0m | [1mUAR:[0m [1;96m0.7254[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 80/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 80 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9828[0m (800/814)
   • 中间层1准确率: [96m0.9816[0m (799/814)
   • 中间层2准确率: [96m0.9816[0m (799/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 80 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.6265[0m | [1mUAR:[0m [1;96m0.6905[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 81/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 81 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9865[0m (803/814)
   • 中间层1准确率: [96m0.9865[0m (803/814)
   • 中间层2准确率: [96m0.9803[0m (798/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 81 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5322[0m | [1mUAR:[0m [1;96m0.6016[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 82/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 82 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9717[0m (791/814)
   • 中间层1准确率: [96m0.9717[0m (791/814)
   • 中间层2准确率: [96m0.9693[0m (789/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 82 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.7080[0m | [1mUAR:[0m [1;96m0.7698[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 83/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 83 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9889[0m (805/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 83 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5553[0m | [1mUAR:[0m [1;96m0.6397[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 84/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 84 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 84 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.6492[0m | [1mUAR:[0m [1;96m0.6651[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 85/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 85 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9877[0m (804/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 85 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5226[0m | [1mUAR:[0m [1;96m0.6016[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 86/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 86 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9730[0m (792/814)
   • 中间层1准确率: [96m0.9803[0m (798/814)
   • 中间层2准确率: [96m0.9730[0m (792/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 86 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3235[0m (11/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.3603[0m | [1mUAR:[0m [1;96m0.5365[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 87/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 87 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9717[0m (791/814)
   • 中间层1准确率: [96m0.9558[0m (778/814)
   • 中间层2准确率: [96m0.9742[0m (793/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 87 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5339[0m | [1mUAR:[0m [1;96m0.6079[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 88/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 88 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9865[0m (803/814)
   • 中间层2准确率: [96m0.9889[0m (805/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 88 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.6204[0m | [1mUAR:[0m [1;96m0.6810[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 89/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 89 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9816[0m (799/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 89 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.6227[0m | [1mUAR:[0m [1;96m0.6952[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 90/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 90 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 90 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.6055[0m | [1mUAR:[0m [1;96m0.6524[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 91/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 91 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 91 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3235[0m (11/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4127[0m | [1mUAR:[0m [1;96m0.5317[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 92/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 92 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9889[0m (805/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 92 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.1765[0m (6/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.3208[0m | [1mUAR:[0m [1;96m0.4508[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 93/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 93 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 93 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.6187[0m | [1mUAR:[0m [1;96m0.6825[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 94/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 94 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 94 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5397[0m | [1mUAR:[0m [1;96m0.6587[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 95/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 95 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 95 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5026[0m | [1mUAR:[0m [1;96m0.6302[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 96/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 96 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 96 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4825[0m | [1mUAR:[0m [1;96m0.6095[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 97/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 97 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 97 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4924[0m | [1mUAR:[0m [1;96m0.6238[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 98/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 98 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 98 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4604[0m | [1mUAR:[0m [1;96m0.5444[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 99/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 99 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9865[0m (803/814)
   • 中间层1准确率: [96m0.9853[0m (802/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 99 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.6279[0m | [1mUAR:[0m [1;96m0.7778[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 100/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 100 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9558[0m (778/814)
   • 中间层1准确率: [96m0.9521[0m (775/814)
   • 中间层2准确率: [96m0.9570[0m (779/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 100 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2941[0m (10/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4017[0m | [1mUAR:[0m [1;96m0.5143[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 101/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 101 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9877[0m (804/814)
   • 中间层1准确率: [96m0.9914[0m (807/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 101 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.3000[0m | [1mUAR:[0m [1;96m0.3810[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 102/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 102 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 102 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.6457[0m | [1mUAR:[0m [1;96m0.6889[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 103/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 103 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9865[0m (803/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 103 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4302[0m | [1mUAR:[0m [1;96m0.5952[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 104/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 104 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9803[0m (798/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9779[0m (796/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 104 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5466[0m | [1mUAR:[0m [1;96m0.6254[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 105/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 105 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9705[0m (790/814)
   • 中间层1准确率: [96m0.9742[0m (793/814)
   • 中间层2准确率: [96m0.9693[0m (789/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 105 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5527[0m | [1mUAR:[0m [1;96m0.6746[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 106/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 106 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9816[0m (799/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 106 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5067[0m | [1mUAR:[0m [1;96m0.6095[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 107/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 107 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 107 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4304[0m | [1mUAR:[0m [1;96m0.5381[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 108/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 108 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 108 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5633[0m | [1mUAR:[0m [1;96m0.6476[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 109/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 109 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 109 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4629[0m | [1mUAR:[0m [1;96m0.5667[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 110/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 110 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 110 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5812[0m | [1mUAR:[0m [1;96m0.6381[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 111/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 111 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 111 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5538[0m | [1mUAR:[0m [1;96m0.6698[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 112/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 112 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 112 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4629[0m | [1mUAR:[0m [1;96m0.5667[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 113/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 113 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 113 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4776[0m | [1mUAR:[0m [1;96m0.5810[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 114/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 114 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 114 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5812[0m | [1mUAR:[0m [1;96m0.6381[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 115/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 115 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9889[0m (805/814)
   • 中间层1准确率: [96m0.9865[0m (803/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 115 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4456[0m | [1mUAR:[0m [1;96m0.5810[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 116/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 116 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9730[0m (792/814)
   • 中间层1准确率: [96m0.9668[0m (787/814)
   • 中间层2准确率: [96m0.9705[0m (790/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 116 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.5498[0m | [1mUAR:[0m [1;96m0.6302[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 117/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 117 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9730[0m (792/814)
   • 中间层1准确率: [96m0.9472[0m (771/814)
   • 中间层2准确率: [96m0.9730[0m (792/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 117 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4284[0m | [1mUAR:[0m [1;96m0.5365[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 118/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 118 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9791[0m (797/814)
   • 中间层1准确率: [96m0.9865[0m (803/814)
   • 中间层2准确率: [96m0.9816[0m (799/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 118 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4912[0m | [1mUAR:[0m [1;96m0.6619[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 119/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 119 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9595[0m (781/814)
   • 中间层1准确率: [96m0.9889[0m (805/814)
   • 中间层2准确率: [96m0.9558[0m (778/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 119 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4735[0m | [1mUAR:[0m [1;96m0.5825[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 120/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 120 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9828[0m (800/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 120 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4305[0m | [1mUAR:[0m [1;96m0.5889[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 121/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 121 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9914[0m (807/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 121 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
📊 [1mUF1:[0m [1;96m0.4929[0m | [1mUAR:[0m [1;96m0.5794[0m
🥇 [1m最佳UF1:[0m [1;93m0.8318[0m | [1m最佳UAR:[0m [1;93m0.8905[0m
============================================================


[1m[1;96m📅 Epoch 122/770[0m
[1;96m-----------------[0m
正在关闭TensorBoard写入器...
TensorBoard写入器已关闭

正在关闭日志系统...
