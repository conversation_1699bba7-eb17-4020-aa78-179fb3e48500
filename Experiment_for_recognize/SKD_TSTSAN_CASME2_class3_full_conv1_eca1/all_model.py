import torch.nn as nn
import torch
import torch.nn.functional as F
import math
from motion_magnification_learning_based_master.magnet import Manipulator as MagManipulator
from motion_magnification_learning_based_master.magnet import Encoder_No_texture as MagEncoder_No_texture
from Pinwheel_shaped_Convolution import Pinwheel_shaped_Convolution
from WTConv import WTConv2d
from PRO_CODE.WTRFAConv import WTRFAConv
from PRO_CODE.WTPRFAConv import WTPRFAConv
from PRO_CODE.WTPRFAConv_Lite import WTPRFAConv_Lite
from PRO_CODE.LEGM import LEGM


class DynamicCrossBranchFusion(nn.Module):
    """
    动态跨分支特征交互机制 (Dynamic Cross-Branch Fusion, DCBF)

    该模块实现三个分支（L、S、T）之间的动态特征交互，通过学习自适应权重矩阵
    来增强分支间的信息流动，提升微表情特征的判别性和表达能力。

    核心创新点：
    1. 自适应权重学习：根据输入特征动态生成分支间交互权重
    2. 多层次特征融合：在不同抽象层次进行跨分支信息交换
    3. 残差连接设计：保持原始分支特征的同时增强跨分支信息

    Args:
        channels: 特征通道数
        num_branches: 分支数量，默认为3（L、S、T）
        reduction_ratio: 权重生成网络的通道压缩比例
    """
    def __init__(self, channels, num_branches=3, reduction_ratio=4):
        super(DynamicCrossBranchFusion, self).__init__()
        self.channels = channels
        self.num_branches = num_branches
        self.reduction_ratio = reduction_ratio

        # 全局上下文提取器 - 用于生成分支间交互权重
        self.global_context_extractor = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels * num_branches, channels // reduction_ratio, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction_ratio, num_branches * num_branches, 1),
            nn.Sigmoid()
        )

        # 分支特征变换器 - 为每个分支学习特定的特征变换
        self.branch_transformers = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(channels, channels, 1),
                nn.BatchNorm2d(channels),
                nn.ReLU(inplace=True)
            ) for _ in range(num_branches)
        ])

        # 跨分支信息融合器
        self.cross_branch_fusion = nn.Sequential(
            nn.Conv2d(channels, channels, 3, padding=1),
            nn.BatchNorm2d(channels),
            nn.ReLU(inplace=True)
        )

        # 输出特征精炼器
        self.feature_refiner = nn.Sequential(
            nn.Conv2d(channels, channels, 1),
            nn.BatchNorm2d(channels)
        )

    def forward(self, branch_features):
        """
        前向传播过程

        Args:
            branch_features: 包含三个分支特征的列表 [x_L, x_S, x_T]
                           每个特征的形状为 [B, C, H, W]

        Returns:
            enhanced_features: 增强后的分支特征列表
        """
        B, C, H, W = branch_features[0].shape

        # 1. 生成动态交互权重矩阵
        # 将所有分支特征拼接用于全局上下文分析
        concat_features = torch.cat(branch_features, dim=1)  # [B, 3C, H, W]
        interaction_weights = self.global_context_extractor(concat_features)  # [B, 9, 1, 1]
        interaction_weights = interaction_weights.view(B, self.num_branches, self.num_branches)  # [B, 3, 3]

        # 2. 分支特征变换
        transformed_features = []
        for i, (feature, transformer) in enumerate(zip(branch_features, self.branch_transformers)):
            transformed = transformer(feature)
            transformed_features.append(transformed)

        # 3. 动态跨分支特征融合
        enhanced_features = []
        for i in range(self.num_branches):
            # 计算当前分支与其他分支的加权融合
            cross_branch_info = torch.zeros_like(transformed_features[i])

            for j in range(self.num_branches):
                if i != j:  # 排除自身，只融合其他分支信息
                    weight = interaction_weights[:, i, j].unsqueeze(-1).unsqueeze(-1).unsqueeze(-1)  # [B, 1, 1, 1]
                    cross_branch_info += weight * transformed_features[j]

            # 应用跨分支信息融合
            fused_info = self.cross_branch_fusion(cross_branch_info)

            # 残差连接：原始特征 + 跨分支增强信息
            enhanced_feature = transformed_features[i] + fused_info
            enhanced_feature = self.feature_refiner(enhanced_feature)

            enhanced_features.append(enhanced_feature)

        return enhanced_features


def gen_state_dict(weights_path):
    st = torch.load(weights_path)
    st_ks = list(st.keys())
    st_vs = list(st.values())
    state_dict = {}
    for st_k, st_v in zip(st_ks, st_vs):
        state_dict[st_k.replace('module.', '')] = st_v
    return state_dict

class ConsensusModule(torch.nn.Module):

    def __init__(self, consensus_type, dim=1):
        super(ConsensusModule, self).__init__()
        self.consensus_type = consensus_type if consensus_type != 'rnn' else 'identity'
        self.dim = dim

    def forward(self, input):
        return SegmentConsensus(self.consensus_type, self.dim)(input)

class SegmentConsensus(torch.nn.Module):

    def __init__(self, consensus_type, dim=1):
        super(SegmentConsensus, self).__init__()
        self.consensus_type = consensus_type
        self.dim = dim
        self.shape = None

    def forward(self, input_tensor):
        self.shape = input_tensor.size()
        if self.consensus_type == 'avg':
            output = input_tensor.mean(dim=self.dim, keepdim=True)
        elif self.consensus_type == 'identity':
            output = input_tensor
        else:
            output = None

        return output

class TemporalShift(nn.Module):
    def __init__(self, net, n_segment=3, n_div=8, inplace=False):
        super(TemporalShift, self).__init__()
        self.net = net
        self.n_segment = n_segment
        self.fold_div = n_div
        self.inplace = inplace
        if inplace:
            print('=> 使用原地移位...')
        print('=> 使用折叠除法: {}'.format(self.fold_div))

    def forward(self, x):
        x = self.shift(x, self.n_segment, fold_div=self.fold_div, inplace=self.inplace)
        return self.net(x)

    @staticmethod
    def shift(x, n_segment, fold_div=3, inplace=False):
        nt, c, h, w = x.size()
        n_batch = nt // n_segment
        x = x.view(n_batch, n_segment, c, h, w)

        fold = c // fold_div
        if inplace:
            raise NotImplementedError
        else:
            out = torch.zeros_like(x)
            out[:, :-1, :fold] = x[:, 1:, :fold]  # shift left
            out[:, 1:, fold: 2 * fold] = x[:, :-1, fold: 2 * fold]  # shift right
            out[:, :, 2 * fold:] = x[:, :, 2 * fold:]  # not shift

        return out.view(nt, c, h, w)


class LearnableTemporalShift(nn.Module):
    """
    可学习的时间移位模块 (Learnable Temporal Shift Module, LTSM)

    该模块通过端到端学习来优化时间移位策略，相比传统的固定移位模式，
    能够自适应地学习最优的时间建模方式，特别适合微表情识别任务。

    核心创新点：
    1. 可学习的移位偏移量：自动学习最优的时间移位方向和强度
    2. 通道级移位权重：为每个通道学习专门的移位策略
    3. 自适应移位模式：通过softmax学习移位分布
    4. 端到端优化：所有移位参数都可通过反向传播优化

    Args:
        net: 要包装的网络层
        n_segment: 时间段数，默认为2
        n_div: 通道分割比例，默认为8
        learnable_shifts: 是否启用可学习移位，默认为True
        shift_modes: 移位模式数量，默认为4（左移、右移、中心、跳跃）
    """
    def __init__(self, net, n_segment=2, n_div=8, learnable_shifts=True, shift_modes=4):
        super(LearnableTemporalShift, self).__init__()
        self.net = net
        self.n_segment = n_segment
        self.fold_div = n_div
        self.learnable_shifts = learnable_shifts
        self.shift_modes = shift_modes

        if learnable_shifts:
            # 获取网络的输出通道数
            if hasattr(net, 'out_channels'):
                channels = net.out_channels
            elif hasattr(net, 'weight') and len(net.weight.shape) >= 2:
                channels = net.weight.shape[0]  # 输出通道数
            else:
                channels = 64  # 默认值

            # 可学习的移位权重：为每个通道学习移位策略
            self.shift_weights = nn.Parameter(torch.randn(channels, shift_modes))

            # 可学习的移位偏移量：学习最优的移位距离
            self.shift_offsets = nn.Parameter(torch.zeros(shift_modes))

            # 可学习的移位强度：控制移位的强度
            self.shift_intensity = nn.Parameter(torch.ones(1))

            # 温度参数：控制softmax的锐度
            self.temperature = nn.Parameter(torch.ones(1))

            print(f"🔄 启用可学习时间移位: {channels}通道, {shift_modes}种移位模式")
        else:
            print("🔄 使用传统固定时间移位")

    def forward(self, x):
        if self.learnable_shifts:
            x = self.learnable_shift(x)
        else:
            x = TemporalShift.shift(x, self.n_segment, fold_div=self.fold_div, inplace=False)
        return self.net(x)

    def learnable_shift(self, x):
        """
        可学习的时间移位操作

        Args:
            x: 输入特征 [nt, c, h, w]

        Returns:
            shifted_x: 移位后的特征
        """
        nt, c, h, w = x.size()
        n_batch = nt // self.n_segment
        x = x.view(n_batch, self.n_segment, c, h, w)

        # 计算每个通道的移位权重分布
        shift_probs = torch.softmax(self.shift_weights / self.temperature, dim=-1)  # [c, shift_modes]

        # 初始化输出
        out = torch.zeros_like(x)

        # 为每个移位模式计算移位结果
        shift_results = []

        # 模式1: 左移 (捕获未来信息)
        left_shift = torch.zeros_like(x)
        offset = torch.clamp(self.shift_offsets[0], -self.n_segment+1, 0).int()
        if offset < 0:
            left_shift[:, :offset, :] = x[:, -offset:, :]
        shift_results.append(left_shift)

        # 模式2: 右移 (捕获历史信息)
        right_shift = torch.zeros_like(x)
        offset = torch.clamp(self.shift_offsets[1], 0, self.n_segment-1).int()
        if offset > 0:
            right_shift[:, offset:, :] = x[:, :-offset, :]
        shift_results.append(right_shift)

        # 模式3: 中心移位 (当前帧与前后帧的平均)
        center_shift = torch.zeros_like(x)
        center_shift[:, 1:-1, :] = (x[:, :-2, :] + x[:, 2:, :]) / 2
        center_shift[:, 0, :] = x[:, 0, :]  # 边界处理
        center_shift[:, -1, :] = x[:, -1, :]
        shift_results.append(center_shift)

        # 模式4: 跳跃移位 (非连续时间模式)
        skip_shift = torch.zeros_like(x)
        skip_offset = torch.clamp(self.shift_offsets[3], 1, self.n_segment-1).int()
        for i in range(self.n_segment):
            src_idx = (i + skip_offset) % self.n_segment
            skip_shift[:, i, :] = x[:, src_idx, :]
        shift_results.append(skip_shift)

        # 根据学习到的权重组合不同的移位结果
        for c_idx in range(c):
            channel_out = torch.zeros_like(x[:, :, c_idx:c_idx+1, :, :])
            for mode_idx, shift_result in enumerate(shift_results):
                weight = shift_probs[c_idx, mode_idx] * self.shift_intensity
                channel_out += weight * shift_result[:, :, c_idx:c_idx+1, :, :]
            out[:, :, c_idx:c_idx+1, :, :] = channel_out

        return out.view(nt, c, h, w)

    def get_shift_statistics(self):
        """获取移位统计信息，用于分析和可视化"""
        if not self.learnable_shifts:
            return None

        shift_probs = torch.softmax(self.shift_weights / self.temperature, dim=-1)
        stats = {
            'shift_weights_mean': shift_probs.mean(dim=0).detach().cpu().numpy(),
            'shift_offsets': self.shift_offsets.detach().cpu().numpy(),
            'shift_intensity': self.shift_intensity.item(),
            'temperature': self.temperature.item(),
            'dominant_modes': torch.argmax(shift_probs, dim=-1).detach().cpu().numpy()
        }
        return stats


class ECA_Enhanced(nn.Module):
    """
    增强型ECA机制（ECA-Enhanced）
    对现有ECA机制进行精细化改进，增强其对微表情关键特征的敏感性

    Args:
        channel: 输入特征图的通道数
        gamma: 自适应卷积核大小计算参数，默认为2
        b: 自适应卷积核大小计算偏置，默认为1
    """
    def __init__(self, channel, gamma=2, b=1):
        super(ECA_Enhanced, self).__init__()
        # 自适应卷积核大小计算
        t = int(abs((math.log(channel, 2) + b) / gamma))
        k_size = t if t % 2 else t + 1

        # 双分支ECA设计
        self.avg_conv = nn.Conv1d(1, 1, kernel_size=k_size, padding=(k_size - 1) // 2, bias=False)
        self.max_conv = nn.Conv1d(1, 1, kernel_size=k_size, padding=(k_size - 1) // 2, bias=False)

        # 特征重标定
        self.sigmoid = nn.Sigmoid()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)

    def forward(self, x):
        # 双池化策略
        y_avg = self.avg_pool(x).squeeze(-1).transpose(-1, -2)
        y_max = self.max_pool(x).squeeze(-1).transpose(-1, -2)

        # 分别处理平均和最大池化特征
        y_avg = self.avg_conv(y_avg).transpose(-1, -2).unsqueeze(-1)
        y_max = self.max_conv(y_max).transpose(-1, -2).unsqueeze(-1)

        # 自适应融合权重
        attention_weight = self.sigmoid(y_avg + y_max)

        return x * attention_weight.expand_as(x)


class eca_layer_2d_v2(nn.Module):
    """Constructs a ECA module.

    Args:
        channel: Number of channels of the input feature map
        k_size: Adaptive selection of kernel size
    """

    def __init__(self, channel):
        super(eca_layer_2d_v2, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        t = int(abs(math.log(channel,2)+1)/2)
        k_size = t if t%2 else (t+1)
        self.conv = nn.Conv1d(1, 1, kernel_size=k_size, padding=(k_size - 1) // 2, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        y_avg = self.avg_pool(x)
        y_max = self.max_pool(x)

        y_avg = self.conv(y_avg.squeeze(-1).transpose(-1, -2)).transpose(-1, -2).unsqueeze(-1)
        y_max = self.conv(y_max.squeeze(-1).transpose(-1, -2)).transpose(-1, -2).unsqueeze(-1)

        y = self.sigmoid(y_avg+y_max)

        return x * y.expand_as(x)

class SKD_TSTSAN(nn.Module):
    def __init__(self, out_channels=5, amp_factor=5, use_pinwheel_conv=False, use_wtconv=False,
                 use_wtrfaconv=False, use_wtprfaconv=False, use_wtprfaconv_lite=False,
                 use_legm=False, use_eca_enhanced=False, use_cross_branch_fusion=False,
                 use_learnable_tsm=False, wt_type='db4', wt_levels=1):
        super(SKD_TSTSAN, self).__init__()
        self.use_pinwheel_conv = use_pinwheel_conv
        self.use_wtconv = use_wtconv
        self.use_wtrfaconv = use_wtrfaconv
        self.use_wtprfaconv = use_wtprfaconv
        self.use_wtprfaconv_lite = use_wtprfaconv_lite
        self.use_legm = use_legm
        self.use_eca_enhanced = use_eca_enhanced
        self.use_cross_branch_fusion = use_cross_branch_fusion  # 跨分支交互开关
        self.use_learnable_tsm = use_learnable_tsm  # 可学习时间移位开关
        self.wt_type = wt_type  # 小波类型参数
        self.wt_levels = wt_levels  # 小波变换层数参数

        # 打印小波配置信息
        if use_wtconv or use_wtrfaconv or use_wtprfaconv or use_wtprfaconv_lite:
            print(f"🌊 小波配置: 类型={wt_type}, 层数={wt_levels}")

        # 打印跨分支交互配置信息
        if use_cross_branch_fusion:
            print("🔗 启用动态跨分支特征交互机制 (DCBF) - 增强分支间信息流动")

        # 打印可学习时间移位配置信息
        if use_learnable_tsm:
            print("🔄 启用可学习时间移位模块 (LTSM) - 自适应时间建模策略")

        # 确保只有一种卷积类型被激活
        conv_types = [use_pinwheel_conv, use_wtconv, use_wtrfaconv, use_wtprfaconv, use_wtprfaconv_lite]
        if sum(conv_types) > 1:
            raise ValueError("只能同时启用一种卷积类型：use_pinwheel_conv, use_wtconv, use_wtrfaconv, use_wtprfaconv, use_wtprfaconv_lite")
        self.Aug_Encoder_L = MagEncoder_No_texture(dim_in=16)
        self.Aug_Encoder_S = MagEncoder_No_texture(dim_in=1)
        self.Aug_Encoder_T = MagEncoder_No_texture(dim_in=2)
        self.Aug_Manipulator_L = MagManipulator()
        self.Aug_Manipulator_S = MagManipulator()
        self.Aug_Manipulator_T = MagManipulator()

        # 根据配置选择不同的卷积层类型
        if self.use_wtprfaconv_lite:
            # WTPRFAConv_Lite要求输入输出通道数相同，需要先用1x1卷积调整通道数
            self.conv1_L_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_S_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_T_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            # 使用轻量化WTPRFAConv，大幅减少参数数量
            self.conv1_L = WTPRFAConv_Lite(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv1_S = WTPRFAConv_Lite(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv1_T = WTPRFAConv_Lite(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
        elif self.use_wtprfaconv:
            # WTPRFAConv要求输入输出通道数相同，需要先用1x1卷积调整通道数
            self.conv1_L_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_S_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_T_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            # 使用WTPRFAConv进行小波变换风车形感受野注意力卷积，融合三种先进技术
            self.conv1_L = WTPRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv1_S = WTPRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv1_T = WTPRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
        elif self.use_wtrfaconv:
            # WTRFAConv要求输入输出通道数相同，需要先用1x1卷积调整通道数
            self.conv1_L_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_L_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_S_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_T_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            # 使用WTRFAConv进行小波变换感受野注意力卷积，结合多频响应和空间注意力
            self.conv1_L = WTRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3)
            self.conv1_S = WTRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3)
            self.conv1_T = WTRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3)
        elif self.use_wtconv:
            # WTConv要求输入输出通道数相同，需要先用1x1卷积调整通道数
            self.conv1_L_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_S_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_T_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            # 使用WTConv进行小波变换卷积，增强多频响应特征提取
            self.conv1_L = WTConv2d(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type)
            self.conv1_S = WTConv2d(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type)
            self.conv1_T = WTConv2d(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type)
        elif self.use_pinwheel_conv:
            # 风车形卷积要求输出通道数是4的倍数，这里使用64通道
            self.conv1_L = Pinwheel_shaped_Convolution(c1=32, c2=64, k=5, s=1)
            self.conv1_S = Pinwheel_shaped_Convolution(c1=32, c2=64, k=5, s=1)
            self.conv1_T = Pinwheel_shaped_Convolution(c1=32, c2=64, k=5, s=1)
        else:
            # 使用标准卷积
            self.conv1_L = nn.Conv2d(32, out_channels=64, kernel_size=5, stride=1)
            self.conv1_S = nn.Conv2d(32, out_channels=64, kernel_size=5, stride=1)
            self.conv1_T = nn.Conv2d(32, out_channels=64, kernel_size=5, stride=1)

        self.relu = nn.ReLU()
        self.bn1_L = nn.BatchNorm2d(64)
        self.bn1_S = nn.BatchNorm2d(64)
        self.bn1_T = nn.BatchNorm2d(64)
        self.maxpool = nn.MaxPool2d(kernel_size=5, stride=2, padding=2)

        self.AC1_conv1_L = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC1_conv1_S = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC1_conv1_T = self._create_temporal_shift_module(
            nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.AC1_bn1_L = nn.BatchNorm2d(128)
        self.AC1_bn1_S = nn.BatchNorm2d(128)
        self.AC1_bn1_T = nn.BatchNorm2d(128)

        self.AC1_conv2_L = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC1_conv2_S = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC1_conv2_T = self._create_temporal_shift_module(
            nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.AC1_bn2_L = nn.BatchNorm2d(128)
        self.AC1_bn2_S = nn.BatchNorm2d(128)
        self.AC1_bn2_T = nn.BatchNorm2d(128)
        self.AC1_pool = nn.AdaptiveAvgPool2d(1)
        self.AC1_fc = nn.Linear(in_features=384, out_features=out_channels)

        # conv2层根据配置选择不同的卷积类型
        if self.use_wtprfaconv_lite:
            # 使用轻量化WTPRFAConv进行第二层处理
            self.conv2_L = WTPRFAConv_Lite(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv2_S = WTPRFAConv_Lite(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            # 对于时序分支，使用时间移位模块包装的标准卷积
            self.conv2_T = self._create_temporal_shift_module(
                nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        elif self.use_wtprfaconv:
            # 使用WTPRFAConv进行第二层小波变换风车形感受野注意力卷积，进一步增强特征提取
            self.conv2_L = WTPRFAConv(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv2_S = WTPRFAConv(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            # 对于时序分支，使用时间移位模块包装的标准卷积，因为WTPRFAConv与时间移位的兼容性需要进一步验证
            self.conv2_T = self._create_temporal_shift_module(
                nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        elif self.use_wtrfaconv:
            # 使用WTRFAConv进行第二层小波变换感受野注意力卷积，进一步增强频域和空间特征提取
            self.conv2_L = WTRFAConv(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3)
            self.conv2_S = WTRFAConv(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3)
            # 对于时序分支，使用时间移位模块包装的标准卷积，因为WTRFAConv与时间移位的兼容性需要进一步验证
            self.conv2_T = self._create_temporal_shift_module(
                nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        elif self.use_wtconv:
            # 使用WTConv进行第二层小波变换卷积，进一步增强频域特征提取
            self.conv2_L = WTConv2d(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type)
            self.conv2_S = WTConv2d(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type)
            # 对于时序分支，使用时间移位模块包装的标准卷积，因为WTConv与时间移位的兼容性需要进一步验证
            self.conv2_T = self._create_temporal_shift_module(
                nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        elif self.use_pinwheel_conv:
            self.conv2_L = Pinwheel_shaped_Convolution(c1=64, c2=64, k=3, s=1)
            self.conv2_S = Pinwheel_shaped_Convolution(c1=64, c2=64, k=3, s=1)
            # 对于时序分支，使用时间移位模块包装的标准卷积，因为风车形卷积与时间移位的兼容性需要进一步验证
            self.conv2_T = self._create_temporal_shift_module(
                nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        else:
            self.conv2_L = nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1)
            self.conv2_S = nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1)
            self.conv2_T = self._create_temporal_shift_module(
                nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.bn2_L = nn.BatchNorm2d(64)
        self.bn2_S = nn.BatchNorm2d(64)
        self.bn2_T = nn.BatchNorm2d(64)

        self.conv3_L = nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1)
        self.conv3_S = nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1)
        self.conv3_T = self._create_temporal_shift_module(
            nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.bn3_L = nn.BatchNorm2d(64)
        self.bn3_S = nn.BatchNorm2d(64)
        self.bn3_T = nn.BatchNorm2d(64)

        self.avgpool = nn.AvgPool2d(kernel_size=3, stride=2, padding=1)

        self.AC2_conv1_L = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC2_conv1_S = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC2_conv1_T = self._create_temporal_shift_module(
            nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.AC2_bn1_L = nn.BatchNorm2d(128)
        self.AC2_bn1_S = nn.BatchNorm2d(128)
        self.AC2_bn1_T = nn.BatchNorm2d(128)

        self.AC2_conv2_L = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC2_conv2_S = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC2_conv2_T = self._create_temporal_shift_module(
            nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.AC2_bn2_L = nn.BatchNorm2d(128)
        self.AC2_bn2_S = nn.BatchNorm2d(128)
        self.AC2_bn2_T = nn.BatchNorm2d(128)
        self.AC2_pool = nn.AdaptiveAvgPool2d(1)
        self.AC2_fc = nn.Linear(in_features=384, out_features=out_channels)

        self.all_avgpool = nn.AdaptiveAvgPool2d(1)
        self.conv4_L = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.conv4_S = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.conv4_T = self._create_temporal_shift_module(
            nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.bn4_L = nn.BatchNorm2d(128)
        self.bn4_S = nn.BatchNorm2d(128)
        self.bn4_T = nn.BatchNorm2d(128)

        self.conv5_L = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.conv5_S = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.conv5_T = self._create_temporal_shift_module(
            nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2, n_div=8)
        self.bn5_L = nn.BatchNorm2d(128)
        self.bn5_S = nn.BatchNorm2d(128)
        self.bn5_T = nn.BatchNorm2d(128)

        self.fc2 = nn.Linear(in_features=384, out_features=out_channels)

        # ECA注意力模块 - 根据选择使用标准ECA或增强型ECA
        if self.use_eca_enhanced:
            # 使用增强型ECA模块
            self.ECA1 = ECA_Enhanced(64)
            self.ECA2 = ECA_Enhanced(64)
            self.ECA3 = ECA_Enhanced(64)
            self.ECA4 = ECA_Enhanced(128)
            self.ECA5 = ECA_Enhanced(128)

            self.AC1_ECA1 = ECA_Enhanced(128)
            self.AC1_ECA2 = ECA_Enhanced(128)
            self.AC2_ECA1 = ECA_Enhanced(128)
            self.AC2_ECA2 = ECA_Enhanced(128)
            print("🔥 使用增强型ECA注意力机制 - 双分支自适应特征重标定")
        else:
            # 使用标准ECA模块
            self.ECA1 = eca_layer_2d_v2(64)
            self.ECA2 = eca_layer_2d_v2(64)
            self.ECA3 = eca_layer_2d_v2(64)
            self.ECA4 = eca_layer_2d_v2(128)
            self.ECA5 = eca_layer_2d_v2(128)

            self.AC1_ECA1 = eca_layer_2d_v2(128)
            self.AC1_ECA2 = eca_layer_2d_v2(128)
            self.AC2_ECA1 = eca_layer_2d_v2(128)
            self.AC2_ECA2 = eca_layer_2d_v2(128)

        # 动态跨分支特征交互模块配置
        if self.use_cross_branch_fusion:
            # 在不同层级设置跨分支交互模块
            self.dcbf_layer1 = DynamicCrossBranchFusion(channels=64, num_branches=3, reduction_ratio=4)  # conv1后
            self.dcbf_layer2 = DynamicCrossBranchFusion(channels=64, num_branches=3, reduction_ratio=4)  # conv2后
            self.dcbf_layer3 = DynamicCrossBranchFusion(channels=128, num_branches=3, reduction_ratio=4)  # conv4后
            print("🔗 跨分支交互模块已配置: 3个交互层级 (64→64→128通道)")

        # LEGM模块配置 - 仅在位置1使用，用于增强初期特征提取（低层纹理和边缘特征）
        if self.use_legm:
            # 使用标准LEGM
            # LEGM位置1：第一个卷积层后，增强初期特征提取
            self.legm1_L = LEGM(network_depth=6, dim=64, depth=2, num_heads=4,
                               window_size=8, attn_ratio=0.5, attn_loc='last', conv_type='DWConv')
            self.legm1_S = LEGM(network_depth=6, dim=64, depth=2, num_heads=4,
                               window_size=8, attn_ratio=0.5, attn_loc='last', conv_type='DWConv')

        self.amp_factor = amp_factor

        self.consensus = ConsensusModule("avg")

        self.dropout = nn.Dropout(0.2)

    def _create_temporal_shift_module(self, conv_layer, n_segment=2, n_div=8):
        """
        创建时间移位模块的辅助方法

        Args:
            conv_layer: 要包装的卷积层
            n_segment: 时间段数
            n_div: 通道分割比例

        Returns:
            时间移位模块 (传统TSM或可学习TSM)
        """
        if self.use_learnable_tsm:
            return LearnableTemporalShift(conv_layer, n_segment=n_segment, n_div=n_div,
                                        learnable_shifts=True, shift_modes=4)
        else:
            return TemporalShift(conv_layer, n_segment=n_segment, n_div=n_div, inplace=False)

    def forward(self, input):
        x1 = input[:, 2:18, :, :]
        x1_onset = input[:, 18:34, :, :]
        x2 = input[:, 0, :, :].unsqueeze(dim=1)
        x2_onset = input[:, 1, :, :].unsqueeze(dim=1)
        x3 = input[:, 34:, :, :]

        bsz = x1.shape[0]

        x3 = torch.reshape(x3, (bsz * 2, 2, 48, 48))

        # 根据输入设备创建相应的零张量
        device = x3.device
        x3_onset = torch.zeros(bsz * 2, 2, 48, 48, device=device)

        motion_x1_onset = self.Aug_Encoder_L(x1_onset)
        motion_x1 = self.Aug_Encoder_L(x1)
        x1 = self.Aug_Manipulator_L(motion_x1_onset, motion_x1, self.amp_factor)
        motion_x2_onset = self.Aug_Encoder_S(x2_onset)
        motion_x2 = self.Aug_Encoder_S(x2)
        x2 = self.Aug_Manipulator_S(motion_x2_onset, motion_x2, self.amp_factor)
        motion_x3_onset = self.Aug_Encoder_T(x3_onset)
        motion_x3 = self.Aug_Encoder_T(x3)
        x3 = self.Aug_Manipulator_T(motion_x3_onset, motion_x3, self.amp_factor)

        # 根据配置选择不同的卷积处理方式
        if self.use_wtprfaconv_lite or self.use_wtprfaconv or self.use_wtrfaconv or self.use_wtconv:
            # WTPRFAConv_Lite、WTPRFAConv、WTRFAConv和WTConv都需要先调整通道数
            x1 = self.conv1_L_pre(x1)
            x1 = self.conv1_L(x1)
            x1 = self.bn1_L(x1)
            x1 = self.relu(x1)
            # LEGM位置1：第一个卷积层后，增强初期特征提取
            if self.use_legm:
                x1 = self.legm1_L(x1)
            else:
                x1 = self.ECA1(x1)
            x1 = self.maxpool(x1)

            x2 = self.conv1_S_pre(x2)
            x2 = self.conv1_S(x2)
            x2 = self.bn1_S(x2)
            x2 = self.relu(x2)
            # LEGM位置1：第一个卷积层后，增强初期特征提取
            if self.use_legm:
                x2 = self.legm1_S(x2)
            x2 = self.maxpool(x2)

            x3 = self.conv1_T_pre(x3)
            x3 = self.conv1_T(x3)
            x3 = self.bn1_T(x3)
            x3 = self.relu(x3)
            x3 = self.maxpool(x3)
        else:
            # 标准卷积或风车形卷积
            x1 = self.conv1_L(x1)
            x1 = self.bn1_L(x1)
            x1 = self.relu(x1)
            # LEGM位置1：第一个卷积层后，增强初期特征提取
            if self.use_legm:
                x1 = self.legm1_L(x1)
            else:
                x1 = self.ECA1(x1)

            x2 = self.conv1_S(x2)
            x2 = self.bn1_S(x2)
            x2 = self.relu(x2)
            # LEGM位置1：第一个卷积层后，增强初期特征提取
            if self.use_legm:
                x2 = self.legm1_S(x2)

            x3 = self.conv1_T(x3)
            x3 = self.bn1_T(x3)
            x3 = self.relu(x3)

            # 第一层跨分支特征交互 (在maxpool之前进行)
            if self.use_cross_branch_fusion:
                # 处理x3分支的batch size不匹配问题
                # x3的batch size是bsz*2，而x1和x2是bsz
                # 我们需要将x1和x2重复以匹配x3的batch size
                B_x3 = x3.shape[0]  # bsz * 2
                B_x1 = x1.shape[0]  # bsz

                if B_x3 != B_x1:
                    # 重复x1和x2以匹配x3的batch size
                    x1_expanded = x1.repeat(2, 1, 1, 1)  # [bsz*2, C, H, W]
                    x2_expanded = x2.repeat(2, 1, 1, 1)  # [bsz*2, C, H, W]

                    # 进行跨分支交互
                    branch_features_1 = [x1_expanded, x2_expanded, x3]
                    enhanced_features_1 = self.dcbf_layer1(branch_features_1)
                    x1_enhanced, x2_enhanced, x3 = enhanced_features_1

                    # 将x1和x2恢复到原始batch size (取平均)
                    x1 = (x1_enhanced[:B_x1] + x1_enhanced[B_x1:]) / 2
                    x2 = (x2_enhanced[:B_x1] + x2_enhanced[B_x1:]) / 2
                else:
                    # batch size匹配，直接进行交互
                    branch_features_1 = [x1, x2, x3]
                    enhanced_features_1 = self.dcbf_layer1(branch_features_1)
                    x1, x2, x3 = enhanced_features_1

            # 应用maxpool
            x1 = self.maxpool(x1)
            x2 = self.maxpool(x2)
            x3 = self.maxpool(x3)

        AC1_x1 = self.AC1_conv1_L(x1)
        AC1_x1 = self.AC1_bn1_L(AC1_x1)
        AC1_x1 = self.relu(AC1_x1)
        AC1_x1 = self.AC1_ECA1(AC1_x1)
        AC1_x1 = self.AC1_conv2_L(AC1_x1)
        AC1_x1 = self.AC1_bn2_L(AC1_x1)
        AC1_x1 = self.relu(AC1_x1)
        AC1_x1 = self.AC1_ECA2(AC1_x1)
        AC1_x1 = self.AC1_pool(AC1_x1)
        AC1_x1_all = AC1_x1.view(AC1_x1.size(0), -1)

        AC1_x2 = self.AC1_conv1_S(x2)
        AC1_x2 = self.AC1_bn1_S(AC1_x2)
        AC1_x2 = self.relu(AC1_x2)
        AC1_x2 = self.AC1_conv2_S(AC1_x2)
        AC1_x2 = self.AC1_bn2_S(AC1_x2)
        AC1_x2 = self.relu(AC1_x2)
        AC1_x2 = self.AC1_pool(AC1_x2)
        AC1_x2_all = AC1_x2.view(AC1_x2.size(0), -1)

        AC1_x3 = self.AC1_conv1_T(x3)
        AC1_x3 = self.AC1_bn1_T(AC1_x3)
        AC1_x3 = self.relu(AC1_x3)
        AC1_x3 = self.AC1_conv2_T(AC1_x3)
        AC1_x3 = self.AC1_bn2_T(AC1_x3)
        AC1_x3 = self.relu(AC1_x3)
        AC1_x3 = self.AC1_pool(AC1_x3)
        AC1_x3_all = AC1_x3.view(AC1_x3.size(0), -1)

        AC1_x3_all = AC1_x3_all.view((-1, 2) + AC1_x3_all.size()[1:])
        AC1_x3_all = self.consensus(AC1_x3_all)
        AC1_x3_all = AC1_x3_all.squeeze(1)
        AC1_feature = torch.cat((AC1_x1_all, AC1_x2_all, AC1_x3_all), 1)
        AC1_x_all = self.dropout(AC1_feature)
        AC1_x_all = self.AC1_fc(AC1_x_all)


        x1 = self.conv2_L(x1)
        x1 = self.bn2_L(x1)
        x1 = self.relu(x1)
        x1 = self.ECA2(x1)

        x2 = self.conv2_S(x2)
        x2 = self.bn2_S(x2)
        x2 = self.relu(x2)
        x2 = self.ECA2(x2)

        x3 = self.conv2_T(x3)
        x3 = self.bn2_T(x3)
        x3 = self.relu(x3)

        # 第二层跨分支特征交互 (在conv3之前进行)
        if self.use_cross_branch_fusion:
            # 处理batch size不匹配问题
            B_x3 = x3.shape[0]  # bsz * 2
            B_x1 = x1.shape[0]  # bsz

            if B_x3 != B_x1:
                # 重复x1和x2以匹配x3的batch size
                x1_expanded = x1.repeat(2, 1, 1, 1)
                x2_expanded = x2.repeat(2, 1, 1, 1)

                # 进行跨分支交互
                branch_features_2 = [x1_expanded, x2_expanded, x3]
                enhanced_features_2 = self.dcbf_layer2(branch_features_2)
                x1_enhanced, x2_enhanced, x3 = enhanced_features_2

                # 将x1和x2恢复到原始batch size
                x1 = (x1_enhanced[:B_x1] + x1_enhanced[B_x1:]) / 2
                x2 = (x2_enhanced[:B_x1] + x2_enhanced[B_x1:]) / 2
            else:
                branch_features_2 = [x1, x2, x3]
                enhanced_features_2 = self.dcbf_layer2(branch_features_2)
                x1, x2, x3 = enhanced_features_2

        x1 = self.conv3_L(x1)
        x1 = self.bn3_L(x1)
        x1 = self.relu(x1)
        x1 = self.ECA3(x1)
        x1 = self.avgpool(x1)

        x2 = self.conv3_S(x2)
        x2 = self.bn3_S(x2)
        x2 = self.relu(x2)
        x2 = self.avgpool(x2)

        x3 = self.conv3_T(x3)
        x3 = self.bn3_T(x3)
        x3 = self.relu(x3)
        x3 = self.avgpool(x3)

        AC2_x1 = self.AC2_conv1_L(x1)
        AC2_x1 = self.AC2_bn1_L(AC2_x1)
        AC2_x1 = self.relu(AC2_x1)
        AC2_x1 = self.AC2_ECA1(AC2_x1)
        AC2_x1 = self.AC2_conv2_L(AC2_x1)
        AC2_x1 = self.AC2_bn2_L(AC2_x1)
        AC2_x1 = self.relu(AC2_x1)
        AC2_x1 = self.AC2_ECA2(AC2_x1)
        AC2_x1 = self.AC2_pool(AC2_x1)
        AC2_x1_all = AC2_x1.view(AC2_x1.size(0), -1)

        AC2_x2 = self.AC2_conv1_S(x2)
        AC2_x2 = self.AC2_bn1_S(AC2_x2)
        AC2_x2 = self.relu(AC2_x2)
        AC2_x2 = self.AC2_conv2_S(AC2_x2)
        AC2_x2 = self.AC2_bn2_S(AC2_x2)
        AC2_x2 = self.relu(AC2_x2)
        AC2_x2 = self.AC2_pool(AC2_x2)
        AC2_x2_all = AC2_x2.view(AC2_x2.size(0), -1)

        AC2_x3 = self.AC2_conv1_T(x3)
        AC2_x3 = self.AC2_bn1_T(AC2_x3)
        AC2_x3 = self.relu(AC2_x3)
        AC2_x3 = self.AC2_conv2_T(AC2_x3)
        AC2_x3 = self.AC2_bn2_T(AC2_x3)
        AC2_x3 = self.relu(AC2_x3)
        AC2_x3 = self.AC2_pool(AC2_x3)
        AC2_x3_all = AC2_x3.view(AC2_x3.size(0), -1)

        AC2_x3_all = AC2_x3_all.view((-1, 2) + AC2_x3_all.size()[1:])
        AC2_x3_all = self.consensus(AC2_x3_all)
        AC2_x3_all = AC2_x3_all.squeeze(1)
        AC2_feature = torch.cat((AC2_x1_all, AC2_x2_all, AC2_x3_all), 1)
        AC2_x_all = self.dropout(AC2_feature)
        AC2_x_all = self.AC2_fc(AC2_x_all)


        x1 = self.conv4_L(x1)
        x1 = self.bn4_L(x1)
        x1 = self.relu(x1)
        x1 = self.ECA4(x1)

        x2 = self.conv4_S(x2)
        x2 = self.bn4_S(x2)
        x2 = self.relu(x2)

        x3 = self.conv4_T(x3)
        x3 = self.bn4_T(x3)
        x3 = self.relu(x3)

        # 第三层跨分支特征交互 (在conv5之前进行，128通道)
        if self.use_cross_branch_fusion:
            # 处理batch size不匹配问题
            B_x3 = x3.shape[0]  # bsz * 2
            B_x1 = x1.shape[0]  # bsz

            if B_x3 != B_x1:
                # 重复x1和x2以匹配x3的batch size
                x1_expanded = x1.repeat(2, 1, 1, 1)
                x2_expanded = x2.repeat(2, 1, 1, 1)

                # 进行跨分支交互
                branch_features_3 = [x1_expanded, x2_expanded, x3]
                enhanced_features_3 = self.dcbf_layer3(branch_features_3)
                x1_enhanced, x2_enhanced, x3 = enhanced_features_3

                # 将x1和x2恢复到原始batch size
                x1 = (x1_enhanced[:B_x1] + x1_enhanced[B_x1:]) / 2
                x2 = (x2_enhanced[:B_x1] + x2_enhanced[B_x1:]) / 2
            else:
                branch_features_3 = [x1, x2, x3]
                enhanced_features_3 = self.dcbf_layer3(branch_features_3)
                x1, x2, x3 = enhanced_features_3

        x1 = self.conv5_L(x1)
        x1 = self.bn5_L(x1)
        x1 = self.relu(x1)
        x1 = self.ECA5(x1)

        x1 = self.all_avgpool(x1)
        x1_all = x1.view(x1.size(0), -1)

        x2 = self.conv5_S(x2)
        x2 = self.bn5_S(x2)
        x2 = self.relu(x2)

        x2 = self.all_avgpool(x2)
        x2_all = x2.view(x2.size(0), -1)

        x3 = self.conv5_T(x3)
        x3 = self.bn5_T(x3)
        x3 = self.relu(x3)
        x3 = self.all_avgpool(x3)
        x3_all = x3.view(x3.size(0), -1)

        x3_all = x3_all.view((-1, 2) + x3_all.size()[1:])

        x3_all = self.consensus(x3_all)
        x3_all = x3_all.squeeze(1)

        final_feature = torch.cat((x1_all, x2_all, x3_all), 1)
        x_all = self.dropout(final_feature)
        x_all = self.fc2(x_all)
        return x_all, AC1_x_all, AC2_x_all, final_feature, AC1_feature, AC2_feature


def get_model(model_name, class_num, alpha, use_pinwheel_conv=False, use_wtconv=False,
              use_wtrfaconv=False, use_wtprfaconv=False, use_wtprfaconv_lite=False,
              use_legm=False, use_eca_enhanced=False, use_cross_branch_fusion=False,
              use_learnable_tsm=False, wt_type='db4', wt_levels=1):
    if model_name == "SKD_TSTSAN":
        return SKD_TSTSAN(out_channels=class_num, amp_factor=alpha,
                         use_pinwheel_conv=use_pinwheel_conv, use_wtconv=use_wtconv,
                         use_wtrfaconv=use_wtrfaconv, use_wtprfaconv=use_wtprfaconv,
                         use_wtprfaconv_lite=use_wtprfaconv_lite, use_legm=use_legm,
                         use_eca_enhanced=use_eca_enhanced, use_cross_branch_fusion=use_cross_branch_fusion,
                         use_learnable_tsm=use_learnable_tsm, wt_type=wt_type, wt_levels=wt_levels)


def get_model_by_conv_type(model_name, class_num, alpha, conv_type=1, use_legm=False,
                          use_eca_enhanced=False, use_cross_branch_fusion=False,
                          use_learnable_tsm=False, wt_type='db4', wt_levels=1):
    """
    通过卷积类型数字选择创建模型

    Args:
        model_name: 模型名称
        class_num: 分类数量
        alpha: 放大因子
        conv_type: 卷积类型选择
            1 - 标准卷积
            2 - 风车形卷积
            3 - 小波变换卷积
            4 - 小波变换感受野注意力卷积
            5 - 小波变换风车形感受野注意力卷积 (原版，参数多)
            6 - 小波变换风车形感受野注意力卷积 (轻量化版本)
        use_legm: 是否使用标准LEGM模块进行局部-全局特征增强
        use_cross_branch_fusion: 是否使用动态跨分支特征交互机制
        use_learnable_tsm: 是否使用可学习时间移位模块
        wt_type: 小波类型 (仅对小波相关卷积有效)
        wt_levels: 小波变换层数 (仅对小波相关卷积有效)

    Returns:
        模型实例
    """
    conv_configs = {
        1: {"use_pinwheel_conv": False, "use_wtconv": False, "use_wtrfaconv": False, "use_wtprfaconv": False, "use_wtprfaconv_lite": False},
        2: {"use_pinwheel_conv": True, "use_wtconv": False, "use_wtrfaconv": False, "use_wtprfaconv": False, "use_wtprfaconv_lite": False},
        3: {"use_pinwheel_conv": False, "use_wtconv": True, "use_wtrfaconv": False, "use_wtprfaconv": False, "use_wtprfaconv_lite": False},
        4: {"use_pinwheel_conv": False, "use_wtconv": False, "use_wtrfaconv": True, "use_wtprfaconv": False, "use_wtprfaconv_lite": False},
        5: {"use_pinwheel_conv": False, "use_wtconv": False, "use_wtrfaconv": False, "use_wtprfaconv": True, "use_wtprfaconv_lite": False},
        6: {"use_pinwheel_conv": False, "use_wtconv": False, "use_wtrfaconv": False, "use_wtprfaconv": False, "use_wtprfaconv_lite": True},
    }

    if conv_type not in conv_configs:
        raise ValueError(f"无效的卷积类型: {conv_type}. 请选择1-6之间的数字。")

    config = conv_configs[conv_type]

    return get_model(
        model_name=model_name,
        class_num=class_num,
        alpha=alpha,
        use_legm=use_legm,
        use_eca_enhanced=use_eca_enhanced,
        use_cross_branch_fusion=use_cross_branch_fusion,
        use_learnable_tsm=use_learnable_tsm,
        wt_type=wt_type,
        wt_levels=wt_levels,
        **config
    )