
================================================================================
【训练开始】
================================================================================

【硬件信息】使用单个GPU进行训练
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

【损失函数配置】
使用加权Focal Loss (4分类)
类别权重: ['0.4082', '0.1651', '0.2041', '0.2226']
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: S30
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过fear/sadness类别数据: 4
跳过fear/sadness类别数据: 3
正在加载测试数据...
跳过fear/sadness类别测试数据: 4
跳过fear/sadness类别测试数据: 3

S30测试标签: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3]
创建受试者目录: S30
创建日志目录: ./Experiment_for_recognize/MMEWLEGM2_class4_conv1_legm2/S30/logs
正在初始化 标准卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 使用标准卷积层
✅ 启用标准LEGM模块
  - 局部-全局特征增强
  - 提升特征表达能力
  - 推荐选择，性能提升明显

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共646个epoch...

Epoch 1/646

训练集准确率: 0.5606

开始验证...
验证集准确率: 0.5143 | 最佳准确率: 0.0000
保存最佳模型,准确率: 0.5143

Epoch 2/646

训练集准确率: 0.8321

开始验证...
验证集准确率: 0.4000 | 最佳准确率: 0.5143

Epoch 3/646

训练集准确率: 0.9057

开始验证...
验证集准确率: 0.7714 | 最佳准确率: 0.5143
保存最佳模型,准确率: 0.7714

Epoch 4/646

训练集准确率: 0.9440

开始验证...
验证集准确率: 0.6000 | 最佳准确率: 0.7714

Epoch 5/646

训练集准确率: 0.9202

开始验证...
验证集准确率: 0.6286 | 最佳准确率: 0.7714

Epoch 6/646

训练集准确率: 0.9606

开始验证...
验证集准确率: 0.5714 | 最佳准确率: 0.7714

Epoch 7/646

训练集准确率: 0.9855

开始验证...
验证集准确率: 0.7429 | 最佳准确率: 0.7714

Epoch 8/646

训练集准确率: 0.9793

开始验证...
验证集准确率: 0.7429 | 最佳准确率: 0.7714

Epoch 9/646

训练集准确率: 0.9741

开始验证...
验证集准确率: 0.4000 | 最佳准确率: 0.7714

Epoch 10/646

训练集准确率: 0.9751

开始验证...
验证集准确率: 0.5429 | 最佳准确率: 0.7714

Epoch 11/646

训练集准确率: 0.9762

开始验证...
验证集准确率: 0.5714 | 最佳准确率: 0.7714

Epoch 12/646

训练集准确率: 0.9855

开始验证...
验证集准确率: 0.6286 | 最佳准确率: 0.7714

Epoch 13/646

训练集准确率: 0.9689

开始验证...
验证集准确率: 0.4286 | 最佳准确率: 0.7714

Epoch 14/646

训练集准确率: 0.9834

开始验证...
验证集准确率: 0.5143 | 最佳准确率: 0.7714

Epoch 15/646

训练集准确率: 0.9554

开始验证...
验证集准确率: 0.5714 | 最佳准确率: 0.7714

Epoch 16/646

训练集准确率: 0.9689

开始验证...
验证集准确率: 0.4286 | 最佳准确率: 0.7714

Epoch 17/646

训练集准确率: 0.9617

开始验证...
验证集准确率: 0.6000 | 最佳准确率: 0.7714

Epoch 18/646

训练集准确率: 0.9772

开始验证...
验证集准确率: 0.4286 | 最佳准确率: 0.7714

Epoch 19/646

训练集准确率: 0.9244

开始验证...
验证集准确率: 0.5714 | 最佳准确率: 0.7714

Epoch 20/646

训练集准确率: 0.9845

开始验证...
验证集准确率: 0.7143 | 最佳准确率: 0.7714

Epoch 21/646

训练集准确率: 0.9959

开始验证...
验证集准确率: 0.6000 | 最佳准确率: 0.7714

Epoch 22/646

训练集准确率: 0.9969

开始验证...
验证集准确率: 0.6286 | 最佳准确率: 0.7714

Epoch 23/646

训练集准确率: 0.9979

开始验证...
验证集准确率: 0.7143 | 最佳准确率: 0.7714

Epoch 24/646

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.6000 | 最佳准确率: 0.7714

Epoch 25/646

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.5714 | 最佳准确率: 0.7714

Epoch 26/646

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.5714 | 最佳准确率: 0.7714

Epoch 27/646

训练集准确率: 0.9979

开始验证...
验证集准确率: 0.6286 | 最佳准确率: 0.7714

Epoch 28/646

训练集准确率: 0.9399

开始验证...
验证集准确率: 0.6286 | 最佳准确率: 0.7714

Epoch 29/646

训练集准确率: 0.9782

开始验证...
验证集准确率: 0.4286 | 最佳准确率: 0.7714

Epoch 30/646

训练集准确率: 0.9959

开始验证...
验证集准确率: 0.5143 | 最佳准确率: 0.7714

Epoch 31/646

训练集准确率: 0.9969

开始验证...
验证集准确率: 0.6571 | 最佳准确率: 0.7714

Epoch 32/646

训练集准确率: 0.9969

开始验证...
验证集准确率: 0.5714 | 最佳准确率: 0.7714

Epoch 33/646

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.5714 | 最佳准确率: 0.7714

Epoch 34/646

训练集准确率: 0.9990

开始验证...
验证集准确率: 0.4857 | 最佳准确率: 0.7714

Epoch 35/646

训练集准确率: 0.9979

开始验证...
验证集准确率: 0.6286 | 最佳准确率: 0.7714

Epoch 36/646

训练集准确率: 0.9731

开始验证...
验证集准确率: 0.5429 | 最佳准确率: 0.7714

Epoch 37/646

训练集准确率: 0.9430

开始验证...
验证集准确率: 0.6000 | 最佳准确率: 0.7714

Epoch 38/646

训练集准确率: 0.9751

开始验证...
验证集准确率: 0.5714 | 最佳准确率: 0.7714

Epoch 39/646

训练集准确率: 0.9876

开始验证...
验证集准确率: 0.6286 | 最佳准确率: 0.7714

Epoch 40/646

训练集准确率: 0.9834

开始验证...
验证集准确率: 0.6000 | 最佳准确率: 0.7714

Epoch 41/646

训练集准确率: 0.9845

开始验证...
验证集准确率: 0.6286 | 最佳准确率: 0.7714

Epoch 42/646

训练集准确率: 0.9907

开始验证...
验证集准确率: 0.6286 | 最佳准确率: 0.7714

Epoch 43/646

训练集准确率: 0.9793

开始验证...
验证集准确率: 0.4857 | 最佳准确率: 0.7714

Epoch 44/646

训练集准确率: 0.9772

开始验证...
验证集准确率: 0.6857 | 最佳准确率: 0.7714

Epoch 45/646

训练集准确率: 0.9927

开始验证...
验证集准确率: 0.6571 | 最佳准确率: 0.7714

Epoch 46/646

训练集准确率: 0.9896

开始验证...
验证集准确率: 0.6571 | 最佳准确率: 0.7714

Epoch 47/646

训练集准确率: 0.9865

开始验证...
验证集准确率: 0.6000 | 最佳准确率: 0.7714

Epoch 48/646

训练集准确率: 0.9959

开始验证...
验证集准确率: 0.6000 | 最佳准确率: 0.7714

Epoch 49/646

训练集准确率: 0.9990

开始验证...
验证集准确率: 0.6286 | 最佳准确率: 0.7714

Epoch 50/646

训练集准确率: 1.0000

开始验证...
验证集准确率: 0.5714 | 最佳准确率: 0.7714

Epoch 51/646

训练集准确率: 0.9865

开始验证...
验证集准确率: 0.6000 | 最佳准确率: 0.7714

Epoch 52/646

训练集准确率: 0.9803

开始验证...
验证集准确率: 0.6000 | 最佳准确率: 0.7714

Epoch 53/646

训练集准确率: 0.9596

开始验证...
验证集准确率: 0.5429 | 最佳准确率: 0.7714

Epoch 54/646

训练集准确率: 0.9886

开始验证...
验证集准确率: 0.5714 | 最佳准确率: 0.7714

Epoch 55/646
