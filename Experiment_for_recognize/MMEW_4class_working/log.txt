
================================================================================
【MMEW数据集训练开始】
================================================================================
使用设备: cuda
训练顺序: ['S30', 'S06', 'S13', 'S09', 'S10', 'S21', 'S16', 'S05', 'S11', 'S15', 'S03', 'S08', 'S01', 'S18', 'S02', 'S04', 'S12', 'S20', 'S22', 'S24', 'S25', 'S29', 'S07', 'S17', 'S19', 'S27', 'S28', 'S14', 'S23', 'S26']
总共需要训练 30 个受试者

============================================================
训练受试者 1/30: S30
受试者组别: 大样本组(>15个测试样本)
============================================================
正在加载MMEW数据集，测试受试者: S30
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S01 训练数据: 391 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S02 训练数据: 392 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 82 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S03 训练数据: 395 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 69 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S04 训练数据: 393 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S05 训练数据: 387 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 55 个原始样本, 扩充倍数: 1
  加载 S06 训练数据: 382 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S07 训练数据: 402 个样本
    情绪类别 0: 31 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 70 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S08 训练数据: 385 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 65 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S09 训练数据: 384 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 66 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S10 训练数据: 383 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S11 训练数据: 390 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S12 训练数据: 402 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 65 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S13 训练数据: 379 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S14 训练数据: 403 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 63 个原始样本, 扩充倍数: 1
  加载 S15 训练数据: 390 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 62 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S16 训练数据: 383 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S17 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S18 训练数据: 392 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S19 训练数据: 404 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 69 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S20 训练数据: 399 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S21 训练数据: 389 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 70 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S22 训练数据: 399 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S23 训练数据: 406 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S24 训练数据: 401 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S25 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S26 训练数据: 405 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S27 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S28 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S29 训练数据: 403 个样本
    情绪类别 0: 4 个原始样本, 扩充倍数: 1
    情绪类别 1: 14 个原始样本, 扩充倍数: 1
    情绪类别 2: 12 个原始样本, 扩充倍数: 1
    情绪类别 3: 5 个原始样本, 扩充倍数: 1
  加载 S30 测试数据: 35 个样本
数据加载完成 - 训练: 11451, 测试: 35
训练数据最终形状: (11451, 48, 48, 36)
测试数据最终形状: (35, 48, 48, 36)
4分类标签映射完成
训练标签分布: {np.int64(0): np.int64(3036), np.int64(1): np.int64(2506), np.int64(2): np.int64(4056), np.int64(3): np.int64(1853)}
测试标签分布: {np.int64(0): np.int64(4), np.int64(1): np.int64(14), np.int64(2): np.int64(12), np.int64(3): np.int64(5)}
训练样本数: 11451, 测试样本数: 35
训练标签分布: {np.int64(0): np.int64(3036), np.int64(1): np.int64(2506), np.int64(2): np.int64(4056), np.int64(3): np.int64(1853)}
测试标签分布: {np.int64(0): np.int64(4), np.int64(1): np.int64(14), np.int64(2): np.int64(12), np.int64(3): np.int64(5)}
加载预训练模型: /home/<USER>/data/ajq/SKD-TSTSAN-new/Pretrained_model/SKD-TSTSAN.pth
跳过不存在的层: bn1_L.weight
跳过不存在的层: bn1_L.bias
跳过不存在的层: bn1_L.running_mean
跳过不存在的层: bn1_L.running_var
跳过不存在的层: bn1_L.num_batches_tracked
跳过不存在的层: bn1_S.weight
跳过不存在的层: bn1_S.bias
跳过不存在的层: bn1_S.running_mean
跳过不存在的层: bn1_S.running_var
跳过不存在的层: bn1_S.num_batches_tracked
跳过不存在的层: bn1_T.weight
跳过不存在的层: bn1_T.bias
跳过不存在的层: bn1_T.running_mean
跳过不存在的层: bn1_T.running_var
跳过不存在的层: bn1_T.num_batches_tracked
跳过不存在的层: AC1_fc.weight
跳过不存在的层: AC1_fc.bias
跳过不存在的层: AC2_fc.weight
跳过不存在的层: AC2_fc.bias
跳过形状不匹配的层: fc2.weight (预训练: torch.Size([7, 384]), 当前: torch.Size([4, 384]))
跳过形状不匹配的层: fc2.bias (预训练: torch.Size([7]), 当前: torch.Size([4]))
预训练模型部分加载成功，加载了 234/255 层
开始训练受试者 S30，总共1个epoch...
自适应参数 - Alpha: 0.1000, Temperature: 3.0, Beta: 1.00e-06

📊 资源使用情况:
  CPU使用率: 99.6%
  内存使用率: 32.2%
  可用内存: 42.9GB
  GPU内存: 已分配: 0.0GB, 已保留: 0.0GB

Epoch 1/1
Batch 0/1289, Total: 4.5939, Main: 1.4222, KD: 0.0496
Batch 10/1289, Total: 3.3738, Main: 1.2642, KD: 0.0341
Batch 20/1289, Total: 3.5829, Main: 1.2926, KD: 0.0785
Batch 30/1289, Total: 3.9976, Main: 1.0945, KD: 0.0656
Batch 40/1289, Total: 3.3371, Main: 1.1461, KD: 0.0582
Batch 50/1289, Total: 2.1338, Main: 0.8264, KD: 0.0441
Batch 60/1289, Total: 2.9153, Main: 1.0321, KD: 0.0297
Batch 70/1289, Total: 2.5760, Main: 0.9003, KD: 0.0583
Batch 80/1289, Total: 4.5097, Main: 1.4077, KD: 0.0490
Batch 90/1289, Total: 1.8924, Main: 0.7064, KD: 0.0427
Batch 100/1289, Total: 3.2384, Main: 1.0356, KD: 0.0690
Batch 110/1289, Total: 1.8460, Main: 0.5978, KD: 0.0480
Batch 120/1289, Total: 2.9212, Main: 0.9450, KD: 0.0331
Batch 130/1289, Total: 3.7993, Main: 1.0187, KD: 0.0323
Batch 140/1289, Total: 2.9638, Main: 0.7181, KD: 0.0431
Batch 150/1289, Total: 1.5596, Main: 0.5055, KD: 0.0537
Batch 160/1289, Total: 2.3821, Main: 0.7032, KD: 0.0288
Batch 170/1289, Total: 2.3049, Main: 0.7398, KD: 0.0426
Batch 180/1289, Total: 1.6394, Main: 0.4452, KD: 0.0419
Batch 190/1289, Total: 1.5896, Main: 0.4942, KD: 0.0214
Batch 200/1289, Total: 1.9847, Main: 0.7013, KD: 0.0380
Batch 210/1289, Total: 2.2780, Main: 0.8271, KD: 0.0309
Batch 220/1289, Total: 1.9423, Main: 0.5733, KD: 0.0176
Batch 230/1289, Total: 1.2602, Main: 0.5276, KD: 0.0344
Batch 240/1289, Total: 1.2673, Main: 0.4555, KD: 0.0350
Batch 250/1289, Total: 1.6678, Main: 0.5507, KD: 0.0400
Batch 260/1289, Total: 1.2417, Main: 0.4437, KD: 0.0368
Batch 270/1289, Total: 0.9821, Main: 0.3987, KD: 0.0434
Batch 280/1289, Total: 3.3819, Main: 1.0406, KD: 0.0384
Batch 290/1289, Total: 0.6182, Main: 0.2659, KD: 0.0409
Batch 300/1289, Total: 2.3485, Main: 0.8895, KD: 0.0425
Batch 310/1289, Total: 3.0842, Main: 1.1146, KD: 0.0328
Batch 320/1289, Total: 2.0427, Main: 0.7432, KD: 0.0453
Batch 330/1289, Total: 1.2933, Main: 0.4509, KD: 0.0255
Batch 340/1289, Total: 2.5691, Main: 0.7309, KD: 0.0227
Batch 350/1289, Total: 2.1033, Main: 0.6405, KD: 0.0305
Batch 360/1289, Total: 0.9726, Main: 0.3450, KD: 0.0325
Batch 370/1289, Total: 0.9110, Main: 0.3214, KD: 0.0298
Batch 380/1289, Total: 1.9619, Main: 0.7227, KD: 0.0408
Batch 390/1289, Total: 1.5159, Main: 0.4366, KD: 0.0279
Batch 400/1289, Total: 1.8797, Main: 0.6152, KD: 0.0258
Batch 410/1289, Total: 1.1107, Main: 0.3388, KD: 0.0365
Batch 420/1289, Total: 1.3210, Main: 0.4156, KD: 0.0241
Batch 430/1289, Total: 1.0310, Main: 0.3626, KD: 0.0416
Batch 440/1289, Total: 0.6366, Main: 0.2406, KD: 0.0271
Batch 450/1289, Total: 1.8281, Main: 0.6679, KD: 0.0277
Batch 460/1289, Total: 0.9487, Main: 0.3975, KD: 0.0321
Batch 470/1289, Total: 0.6487, Main: 0.2726, KD: 0.0542
Batch 480/1289, Total: 0.8061, Main: 0.2467, KD: 0.0288
Batch 490/1289, Total: 0.9944, Main: 0.4428, KD: 0.0407
Epoch 1 损失统计:
  总损失: 2.0202
  主损失: 0.6744
  知识蒸馏: 0.0395
  特征损失: 0.0026
受试者 S30 训练完成
最佳准确率: 0.0000, 最佳UF1: 0.0000
训练摘要:
  总轮次: 1
  总时间: 3.2分钟
  最终损失: 2.0202

📊 资源使用情况:
  CPU使用率: 99.3%
  内存使用率: 32.4%
  可用内存: 42.8GB
  GPU内存: 已分配: 0.1GB, 已保留: 0.2GB
警告: 受试者 S30 训练失败

============================================================
训练受试者 2/30: S06
受试者组别: 大样本组(>15个测试样本)
============================================================
正在加载MMEW数据集，测试受试者: S06
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S01 训练数据: 391 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S02 训练数据: 392 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 82 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S03 训练数据: 395 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 69 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S04 训练数据: 393 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S05 训练数据: 387 个样本
    情绪类别 0: 2 个原始样本, 扩充倍数: 1
    情绪类别 2: 4 个原始样本, 扩充倍数: 1
    情绪类别 3: 11 个原始样本, 扩充倍数: 1
  加载 S06 测试数据: 17 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S07 训练数据: 402 个样本
    情绪类别 0: 31 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 70 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S08 训练数据: 385 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 65 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S09 训练数据: 384 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 66 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S10 训练数据: 383 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S11 训练数据: 390 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S12 训练数据: 402 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 65 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S13 训练数据: 379 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S14 训练数据: 403 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 63 个原始样本, 扩充倍数: 1
  加载 S15 训练数据: 390 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 62 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S16 训练数据: 383 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S17 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S18 训练数据: 392 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S19 训练数据: 404 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 69 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S20 训练数据: 399 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S21 训练数据: 389 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 70 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S22 训练数据: 399 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S23 训练数据: 406 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S24 训练数据: 401 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S25 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S26 训练数据: 405 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S27 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S28 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S29 训练数据: 403 个样本
    情绪类别 0: 32 个原始样本, 扩充倍数: 3
    情绪类别 1: 75 个原始样本, 扩充倍数: 1
    情绪类别 2: 60 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S30 训练数据: 352 个样本
数据加载完成 - 训练: 11421, 测试: 17
训练数据最终形状: (11421, 48, 48, 36)
测试数据最终形状: (17, 48, 48, 36)
4分类标签映射完成
训练标签分布: {np.int64(0): np.int64(3030), np.int64(1): np.int64(2492), np.int64(2): np.int64(4040), np.int64(3): np.int64(1859)}
测试标签分布: {np.int64(0): np.int64(2), np.int64(2): np.int64(4), np.int64(3): np.int64(11)}
训练样本数: 11421, 测试样本数: 17
训练标签分布: {np.int64(0): np.int64(3030), np.int64(1): np.int64(2492), np.int64(2): np.int64(4040), np.int64(3): np.int64(1859)}
测试标签分布: {np.int64(0): np.int64(2), np.int64(2): np.int64(4), np.int64(3): np.int64(11)}
加载预训练模型: /home/<USER>/data/ajq/SKD-TSTSAN-new/Pretrained_model/SKD-TSTSAN.pth
跳过不存在的层: bn1_L.weight
跳过不存在的层: bn1_L.bias
跳过不存在的层: bn1_L.running_mean
跳过不存在的层: bn1_L.running_var
跳过不存在的层: bn1_L.num_batches_tracked
跳过不存在的层: bn1_S.weight
跳过不存在的层: bn1_S.bias
跳过不存在的层: bn1_S.running_mean
跳过不存在的层: bn1_S.running_var
跳过不存在的层: bn1_S.num_batches_tracked
跳过不存在的层: bn1_T.weight
跳过不存在的层: bn1_T.bias
跳过不存在的层: bn1_T.running_mean
跳过不存在的层: bn1_T.running_var
跳过不存在的层: bn1_T.num_batches_tracked
跳过不存在的层: AC1_fc.weight
跳过不存在的层: AC1_fc.bias
跳过不存在的层: AC2_fc.weight
跳过不存在的层: AC2_fc.bias
跳过形状不匹配的层: fc2.weight (预训练: torch.Size([7, 384]), 当前: torch.Size([4, 384]))
跳过形状不匹配的层: fc2.bias (预训练: torch.Size([7]), 当前: torch.Size([4]))
预训练模型部分加载成功，加载了 234/255 层
开始训练受试者 S06，总共1个epoch...
自适应参数 - Alpha: 0.1000, Temperature: 3.0, Beta: 1.00e-06

📊 资源使用情况:
  CPU使用率: 94.2%
  内存使用率: 33.1%
  可用内存: 42.3GB
  GPU内存: 已分配: 0.0GB, 已保留: 0.2GB

Epoch 1/1
Batch 0/1285, Total: 4.1405, Main: 1.3739, KD: 0.0265
Batch 10/1285, Total: 3.9340, Main: 1.3067, KD: 0.0497
Batch 20/1285, Total: 2.9121, Main: 1.1183, KD: 0.0996
Batch 30/1285, Total: 3.5683, Main: 1.1099, KD: 0.0719
Batch 40/1285, Total: 2.3386, Main: 0.8293, KD: 0.0496
Batch 50/1285, Total: 1.7304, Main: 0.7100, KD: 0.0621
Batch 60/1285, Total: 2.4483, Main: 0.7714, KD: 0.0497
Batch 70/1285, Total: 3.0614, Main: 1.0921, KD: 0.0524
Batch 80/1285, Total: 1.6378, Main: 0.4979, KD: 0.0313
Batch 90/1285, Total: 4.0743, Main: 1.3976, KD: 0.0371
Batch 100/1285, Total: 2.9727, Main: 0.8976, KD: 0.0449
Batch 110/1285, Total: 1.3126, Main: 0.4605, KD: 0.0357
Batch 120/1285, Total: 3.6357, Main: 1.0252, KD: 0.0479
Batch 130/1285, Total: 2.0461, Main: 0.6699, KD: 0.0342
Batch 140/1285, Total: 2.4962, Main: 0.7460, KD: 0.0374
Batch 150/1285, Total: 2.2542, Main: 0.6158, KD: 0.0270
Batch 160/1285, Total: 2.2049, Main: 0.6606, KD: 0.0303
Batch 170/1285, Total: 1.7093, Main: 0.5992, KD: 0.0259
Batch 180/1285, Total: 2.5179, Main: 0.8717, KD: 0.0500
Batch 190/1285, Total: 2.1447, Main: 0.6216, KD: 0.0372
Batch 200/1285, Total: 2.7949, Main: 0.8975, KD: 0.0626
Batch 210/1285, Total: 1.7673, Main: 0.6247, KD: 0.0275
Batch 220/1285, Total: 1.1752, Main: 0.3908, KD: 0.0351
Batch 230/1285, Total: 3.3920, Main: 1.1504, KD: 0.0585
Batch 240/1285, Total: 1.5383, Main: 0.5787, KD: 0.0338
Batch 250/1285, Total: 1.6530, Main: 0.6501, KD: 0.0306
Batch 260/1285, Total: 2.6948, Main: 0.7245, KD: 0.0465
Batch 270/1285, Total: 1.7732, Main: 0.6394, KD: 0.0422
Batch 280/1285, Total: 1.8079, Main: 0.5647, KD: 0.0450
Batch 290/1285, Total: 1.6952, Main: 0.4518, KD: 0.0589
Batch 300/1285, Total: 1.8680, Main: 0.6365, KD: 0.0426
Batch 310/1285, Total: 1.6102, Main: 0.5519, KD: 0.0436
Batch 320/1285, Total: 0.6582, Main: 0.2966, KD: 0.0485
Batch 330/1285, Total: 0.7536, Main: 0.3202, KD: 0.0648
Batch 340/1285, Total: 2.3811, Main: 0.8765, KD: 0.0704
Batch 350/1285, Total: 0.9456, Main: 0.3718, KD: 0.0541
Batch 360/1285, Total: 1.3635, Main: 0.4538, KD: 0.0436
Batch 370/1285, Total: 2.1759, Main: 0.7357, KD: 0.0521
Batch 380/1285, Total: 2.2487, Main: 0.8094, KD: 0.0414
Batch 390/1285, Total: 1.3027, Main: 0.4231, KD: 0.0599
Batch 400/1285, Total: 1.9150, Main: 0.6374, KD: 0.0690
Batch 410/1285, Total: 1.3729, Main: 0.5445, KD: 0.0724
Batch 420/1285, Total: 3.2583, Main: 1.0814, KD: 0.0515
Batch 430/1285, Total: 0.6048, Main: 0.2880, KD: 0.0458
Batch 440/1285, Total: 1.4014, Main: 0.4673, KD: 0.0312
Batch 450/1285, Total: 1.7018, Main: 0.5688, KD: 0.0366
Batch 460/1285, Total: 1.5543, Main: 0.6278, KD: 0.0517
Batch 470/1285, Total: 1.2566, Main: 0.4242, KD: 0.0280
Batch 480/1285, Total: 1.6413, Main: 0.5117, KD: 0.0342
Batch 490/1285, Total: 0.9881, Main: 0.3419, KD: 0.0756
Epoch 1 损失统计:
  总损失: 1.9996
  主损失: 0.6727
  知识蒸馏: 0.0489
  特征损失: 0.0028
受试者 S06 训练完成
最佳准确率: 0.0000, 最佳UF1: 0.0000
训练摘要:
  总轮次: 1
  总时间: 3.2分钟
  最终损失: 1.9996

📊 资源使用情况:
  CPU使用率: 89.3%
  内存使用率: 32.3%
  可用内存: 42.8GB
  GPU内存: 已分配: 0.1GB, 已保留: 0.3GB
警告: 受试者 S06 训练失败

============================================================
训练受试者 3/30: S13
受试者组别: 大样本组(>15个测试样本)
============================================================
正在加载MMEW数据集，测试受试者: S13
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S01 训练数据: 391 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S02 训练数据: 392 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 82 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S03 训练数据: 395 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 69 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S04 训练数据: 393 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S05 训练数据: 387 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 55 个原始样本, 扩充倍数: 1
  加载 S06 训练数据: 382 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S07 训练数据: 402 个样本
    情绪类别 0: 31 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 70 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S08 训练数据: 385 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 65 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S09 训练数据: 384 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 66 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S10 训练数据: 383 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S11 训练数据: 390 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S12 训练数据: 402 个样本
    情绪类别 0: 3 个原始样本, 扩充倍数: 1
    情绪类别 1: 4 个原始样本, 扩充倍数: 1
    情绪类别 2: 7 个原始样本, 扩充倍数: 1
    情绪类别 3: 1 个原始样本, 扩充倍数: 1
  加载 S13 测试数据: 15 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S14 训练数据: 403 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 63 个原始样本, 扩充倍数: 1
  加载 S15 训练数据: 390 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 62 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S16 训练数据: 383 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S17 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S18 训练数据: 392 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S19 训练数据: 404 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 69 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S20 训练数据: 399 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S21 训练数据: 389 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 70 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S22 训练数据: 399 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S23 训练数据: 406 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S24 训练数据: 401 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S25 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S26 训练数据: 405 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S27 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S28 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S29 训练数据: 403 个样本
    情绪类别 0: 32 个原始样本, 扩充倍数: 3
    情绪类别 1: 75 个原始样本, 扩充倍数: 1
    情绪类别 2: 60 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S30 训练数据: 352 个样本
数据加载完成 - 训练: 11424, 测试: 15
训练数据最终形状: (11424, 48, 48, 36)
测试数据最终形状: (15, 48, 48, 36)
4分类标签映射完成
训练标签分布: {np.int64(0): np.int64(3033), np.int64(1): np.int64(2496), np.int64(2): np.int64(4046), np.int64(3): np.int64(1849)}
测试标签分布: {np.int64(0): np.int64(3), np.int64(1): np.int64(4), np.int64(2): np.int64(7), np.int64(3): np.int64(1)}
训练样本数: 11424, 测试样本数: 15
训练标签分布: {np.int64(0): np.int64(3033), np.int64(1): np.int64(2496), np.int64(2): np.int64(4046), np.int64(3): np.int64(1849)}
测试标签分布: {np.int64(0): np.int64(3), np.int64(1): np.int64(4), np.int64(2): np.int64(7), np.int64(3): np.int64(1)}
加载预训练模型: /home/<USER>/data/ajq/SKD-TSTSAN-new/Pretrained_model/SKD-TSTSAN.pth
跳过不存在的层: bn1_L.weight
跳过不存在的层: bn1_L.bias
跳过不存在的层: bn1_L.running_mean
跳过不存在的层: bn1_L.running_var
跳过不存在的层: bn1_L.num_batches_tracked
跳过不存在的层: bn1_S.weight
跳过不存在的层: bn1_S.bias
跳过不存在的层: bn1_S.running_mean
跳过不存在的层: bn1_S.running_var
跳过不存在的层: bn1_S.num_batches_tracked
跳过不存在的层: bn1_T.weight
跳过不存在的层: bn1_T.bias
跳过不存在的层: bn1_T.running_mean
跳过不存在的层: bn1_T.running_var
跳过不存在的层: bn1_T.num_batches_tracked
跳过不存在的层: AC1_fc.weight
跳过不存在的层: AC1_fc.bias
跳过不存在的层: AC2_fc.weight
跳过不存在的层: AC2_fc.bias
跳过形状不匹配的层: fc2.weight (预训练: torch.Size([7, 384]), 当前: torch.Size([4, 384]))
跳过形状不匹配的层: fc2.bias (预训练: torch.Size([7]), 当前: torch.Size([4]))
预训练模型部分加载成功，加载了 234/255 层
开始训练受试者 S13，总共1个epoch...
自适应参数 - Alpha: 0.1000, Temperature: 3.0, Beta: 1.00e-06

📊 资源使用情况:
  CPU使用率: 99.7%
  内存使用率: 36.7%
  可用内存: 40.0GB
  GPU内存: 已分配: 0.0GB, 已保留: 0.3GB

Epoch 1/1
Batch 0/1286, Total: 4.3858, Main: 1.4129, KD: 0.0226
Batch 10/1286, Total: 4.3873, Main: 1.3963, KD: 0.0514
Batch 20/1286, Total: 3.2412, Main: 1.2190, KD: 0.0535
Batch 30/1286, Total: 2.5068, Main: 0.9690, KD: 0.0679
Batch 40/1286, Total: 3.2850, Main: 1.1344, KD: 0.0446
Batch 50/1286, Total: 2.4822, Main: 0.9025, KD: 0.0632
Batch 60/1286, Total: 2.3587, Main: 0.8116, KD: 0.0484
Batch 70/1286, Total: 3.7171, Main: 1.3299, KD: 0.0471
Batch 80/1286, Total: 3.1314, Main: 0.7622, KD: 0.0513
Batch 90/1286, Total: 4.3052, Main: 1.3066, KD: 0.0522
Batch 100/1286, Total: 2.4940, Main: 0.8515, KD: 0.0442
Batch 110/1286, Total: 2.6545, Main: 0.7991, KD: 0.0357
Batch 120/1286, Total: 2.0422, Main: 0.6838, KD: 0.0645
Batch 130/1286, Total: 2.5150, Main: 0.9022, KD: 0.0344
Batch 140/1286, Total: 1.4529, Main: 0.4823, KD: 0.0419
Batch 150/1286, Total: 2.1960, Main: 0.7542, KD: 0.0328
Batch 160/1286, Total: 2.6736, Main: 1.0262, KD: 0.0277
Batch 170/1286, Total: 2.5309, Main: 0.7668, KD: 0.0364
Batch 180/1286, Total: 1.8742, Main: 0.5828, KD: 0.0542
Batch 190/1286, Total: 1.5095, Main: 0.5583, KD: 0.0379
Batch 200/1286, Total: 1.8165, Main: 0.5501, KD: 0.0412
Batch 210/1286, Total: 1.9080, Main: 0.6609, KD: 0.0332
Batch 220/1286, Total: 0.5876, Main: 0.1787, KD: 0.0226
Batch 230/1286, Total: 2.5416, Main: 0.7303, KD: 0.0606
Batch 240/1286, Total: 2.2391, Main: 0.7104, KD: 0.0305
Batch 250/1286, Total: 0.8021, Main: 0.3300, KD: 0.0498
Batch 260/1286, Total: 2.4424, Main: 0.8131, KD: 0.0287
Batch 270/1286, Total: 1.6278, Main: 0.4583, KD: 0.0336
Batch 280/1286, Total: 2.2487, Main: 0.6556, KD: 0.0304
Batch 290/1286, Total: 1.4110, Main: 0.4455, KD: 0.0353
Batch 300/1286, Total: 1.5887, Main: 0.5385, KD: 0.0324
Batch 310/1286, Total: 0.7038, Main: 0.2864, KD: 0.0394
Batch 320/1286, Total: 1.7665, Main: 0.6182, KD: 0.0304
Batch 330/1286, Total: 1.7009, Main: 0.5276, KD: 0.0314
Batch 340/1286, Total: 2.6078, Main: 0.6558, KD: 0.0401
Batch 350/1286, Total: 3.2748, Main: 1.1084, KD: 0.0279
Batch 360/1286, Total: 1.9980, Main: 0.7247, KD: 0.0287
Batch 370/1286, Total: 1.2306, Main: 0.4282, KD: 0.0331
Batch 380/1286, Total: 1.2455, Main: 0.3405, KD: 0.0428
Batch 390/1286, Total: 1.1954, Main: 0.3032, KD: 0.0220
Batch 400/1286, Total: 0.8502, Main: 0.2994, KD: 0.0273
Batch 410/1286, Total: 1.1132, Main: 0.3720, KD: 0.0295
Batch 420/1286, Total: 0.7508, Main: 0.2330, KD: 0.0184
Batch 430/1286, Total: 2.7002, Main: 0.9406, KD: 0.0344
Batch 440/1286, Total: 1.7089, Main: 0.5499, KD: 0.0323
Batch 450/1286, Total: 1.5892, Main: 0.4543, KD: 0.0253
Batch 460/1286, Total: 0.9845, Main: 0.3549, KD: 0.0242
Batch 470/1286, Total: 1.0152, Main: 0.4522, KD: 0.0449
Batch 480/1286, Total: 0.9646, Main: 0.3703, KD: 0.0210
Batch 490/1286, Total: 3.0580, Main: 1.1167, KD: 0.0424
Epoch 1 损失统计:
  总损失: 2.1001
  主损失: 0.6827
  知识蒸馏: 0.0398
  特征损失: 0.0030
受试者 S13 训练完成
最佳准确率: 0.0000, 最佳UF1: 0.0000
训练摘要:
  总轮次: 1
  总时间: 3.2分钟
  最终损失: 2.1001

📊 资源使用情况:
  CPU使用率: 96.8%
  内存使用率: 36.4%
  可用内存: 40.3GB
  GPU内存: 已分配: 0.1GB, 已保留: 0.3GB
警告: 受试者 S13 训练失败

============================================================
训练受试者 4/30: S09
受试者组别: 大样本组(>15个测试样本)
============================================================
正在加载MMEW数据集，测试受试者: S09
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S01 训练数据: 391 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S02 训练数据: 392 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 82 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S03 训练数据: 395 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 69 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S04 训练数据: 393 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S05 训练数据: 387 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 55 个原始样本, 扩充倍数: 1
  加载 S06 训练数据: 382 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S07 训练数据: 402 个样本
    情绪类别 0: 31 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 70 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S08 训练数据: 385 个样本
    情绪类别 0: 1 个原始样本, 扩充倍数: 1
    情绪类别 1: 5 个原始样本, 扩充倍数: 1
    情绪类别 2: 7 个原始样本, 扩充倍数: 1
    情绪类别 3: 1 个原始样本, 扩充倍数: 1
  加载 S09 测试数据: 14 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 66 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S10 训练数据: 383 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S11 训练数据: 390 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S12 训练数据: 402 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 65 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S13 训练数据: 379 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S14 训练数据: 403 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 63 个原始样本, 扩充倍数: 1
  加载 S15 训练数据: 390 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 62 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S16 训练数据: 383 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S17 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S18 训练数据: 392 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S19 训练数据: 404 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 69 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S20 训练数据: 399 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S21 训练数据: 389 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 70 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S22 训练数据: 399 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S23 训练数据: 406 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S24 训练数据: 401 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S25 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S26 训练数据: 405 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S27 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S28 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S29 训练数据: 403 个样本
    情绪类别 0: 32 个原始样本, 扩充倍数: 3
    情绪类别 1: 75 个原始样本, 扩充倍数: 1
    情绪类别 2: 60 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S30 训练数据: 352 个样本
数据加载完成 - 训练: 11419, 测试: 14
训练数据最终形状: (11419, 48, 48, 36)
测试数据最终形状: (14, 48, 48, 36)
4分类标签映射完成
训练标签分布: {np.int64(0): np.int64(3027), np.int64(1): np.int64(2497), np.int64(2): np.int64(4046), np.int64(3): np.int64(1849)}
测试标签分布: {np.int64(0): np.int64(1), np.int64(1): np.int64(5), np.int64(2): np.int64(7), np.int64(3): np.int64(1)}
训练样本数: 11419, 测试样本数: 14
训练标签分布: {np.int64(0): np.int64(3027), np.int64(1): np.int64(2497), np.int64(2): np.int64(4046), np.int64(3): np.int64(1849)}
测试标签分布: {np.int64(0): np.int64(1), np.int64(1): np.int64(5), np.int64(2): np.int64(7), np.int64(3): np.int64(1)}
加载预训练模型: /home/<USER>/data/ajq/SKD-TSTSAN-new/Pretrained_model/SKD-TSTSAN.pth
跳过不存在的层: bn1_L.weight
跳过不存在的层: bn1_L.bias
跳过不存在的层: bn1_L.running_mean
跳过不存在的层: bn1_L.running_var
跳过不存在的层: bn1_L.num_batches_tracked
跳过不存在的层: bn1_S.weight
跳过不存在的层: bn1_S.bias
跳过不存在的层: bn1_S.running_mean
跳过不存在的层: bn1_S.running_var
跳过不存在的层: bn1_S.num_batches_tracked
跳过不存在的层: bn1_T.weight
跳过不存在的层: bn1_T.bias
跳过不存在的层: bn1_T.running_mean
跳过不存在的层: bn1_T.running_var
跳过不存在的层: bn1_T.num_batches_tracked
跳过不存在的层: AC1_fc.weight
跳过不存在的层: AC1_fc.bias
跳过不存在的层: AC2_fc.weight
跳过不存在的层: AC2_fc.bias
跳过形状不匹配的层: fc2.weight (预训练: torch.Size([7, 384]), 当前: torch.Size([4, 384]))
跳过形状不匹配的层: fc2.bias (预训练: torch.Size([7]), 当前: torch.Size([4]))
预训练模型部分加载成功，加载了 234/255 层
开始训练受试者 S09，总共1个epoch...
自适应参数 - Alpha: 0.1000, Temperature: 3.0, Beta: 1.00e-06

📊 资源使用情况:
  CPU使用率: 90.7%
  内存使用率: 37.0%
  可用内存: 39.9GB
  GPU内存: 已分配: 0.0GB, 已保留: 0.3GB

Epoch 1/1
Batch 0/1285, Total: 4.3587, Main: 1.4007, KD: 0.0218
Batch 10/1285, Total: 2.7136, Main: 1.1912, KD: 0.0904
Batch 20/1285, Total: 3.8124, Main: 1.2604, KD: 0.0525
Batch 30/1285, Total: 3.4567, Main: 1.2606, KD: 0.0383
Batch 40/1285, Total: 4.6562, Main: 1.4326, KD: 0.0446
Batch 50/1285, Total: 3.6366, Main: 1.3240, KD: 0.0478
Batch 60/1285, Total: 2.6206, Main: 0.9276, KD: 0.0724
Batch 70/1285, Total: 2.6333, Main: 0.9492, KD: 0.0557
Batch 80/1285, Total: 2.3013, Main: 0.9094, KD: 0.0406
Batch 90/1285, Total: 2.9593, Main: 1.0522, KD: 0.0681
Batch 100/1285, Total: 1.7169, Main: 0.6755, KD: 0.0801
Batch 110/1285, Total: 2.1639, Main: 0.8063, KD: 0.0385
Batch 120/1285, Total: 3.1046, Main: 1.1020, KD: 0.0591
Batch 130/1285, Total: 2.3668, Main: 0.7338, KD: 0.0618
Batch 140/1285, Total: 2.3402, Main: 0.7541, KD: 0.0367
Batch 150/1285, Total: 1.9655, Main: 0.6978, KD: 0.0379
Batch 160/1285, Total: 2.2070, Main: 0.7495, KD: 0.0274
Batch 170/1285, Total: 1.3321, Main: 0.5065, KD: 0.0361
Batch 180/1285, Total: 1.2453, Main: 0.5037, KD: 0.0336
Batch 190/1285, Total: 1.9661, Main: 0.6893, KD: 0.0281
Batch 200/1285, Total: 2.0565, Main: 0.7126, KD: 0.0311
Batch 210/1285, Total: 1.4419, Main: 0.5162, KD: 0.0424
Batch 220/1285, Total: 1.2289, Main: 0.4452, KD: 0.0345
Batch 230/1285, Total: 0.9307, Main: 0.3430, KD: 0.0382
Batch 240/1285, Total: 1.1177, Main: 0.4798, KD: 0.0628
Batch 250/1285, Total: 2.3622, Main: 0.6588, KD: 0.0264
Batch 260/1285, Total: 1.4756, Main: 0.5661, KD: 0.0409
Batch 270/1285, Total: 2.0814, Main: 0.6511, KD: 0.0386
Batch 280/1285, Total: 2.3899, Main: 0.7873, KD: 0.0214
Batch 290/1285, Total: 2.3194, Main: 0.8179, KD: 0.0677
Batch 300/1285, Total: 2.3337, Main: 0.7441, KD: 0.0247
Batch 310/1285, Total: 1.8568, Main: 0.6191, KD: 0.0307
Batch 320/1285, Total: 2.1075, Main: 0.7814, KD: 0.0360
Batch 330/1285, Total: 1.5397, Main: 0.5251, KD: 0.0571
Batch 340/1285, Total: 2.4188, Main: 0.7630, KD: 0.0260
Batch 350/1285, Total: 1.6258, Main: 0.5440, KD: 0.0256
Batch 360/1285, Total: 1.7301, Main: 0.5950, KD: 0.0387
Batch 370/1285, Total: 1.6481, Main: 0.5909, KD: 0.0250
Batch 380/1285, Total: 1.4020, Main: 0.4838, KD: 0.0448
Batch 390/1285, Total: 2.0300, Main: 0.7728, KD: 0.0460
Batch 400/1285, Total: 1.0919, Main: 0.3839, KD: 0.0360
Batch 410/1285, Total: 3.2265, Main: 1.0994, KD: 0.0378
Batch 420/1285, Total: 1.9106, Main: 0.6725, KD: 0.0455
Batch 430/1285, Total: 2.2514, Main: 0.8001, KD: 0.0569
Batch 440/1285, Total: 1.5801, Main: 0.4754, KD: 0.0429
Batch 450/1285, Total: 0.6725, Main: 0.2622, KD: 0.0548
Batch 460/1285, Total: 1.8633, Main: 0.5772, KD: 0.0583
Batch 470/1285, Total: 1.1458, Main: 0.3899, KD: 0.0187
Batch 480/1285, Total: 0.9715, Main: 0.3388, KD: 0.0313
Batch 490/1285, Total: 0.9160, Main: 0.3695, KD: 0.0474
Epoch 1 损失统计:
  总损失: 2.1965
  主损失: 0.7402
  知识蒸馏: 0.0423
  特征损失: 0.0030
受试者 S09 训练完成
最佳准确率: 0.0000, 最佳UF1: 0.0000
训练摘要:
  总轮次: 1
  总时间: 3.1分钟
  最终损失: 2.1965

📊 资源使用情况:
  CPU使用率: 97.7%
  内存使用率: 37.0%
  可用内存: 39.9GB
  GPU内存: 已分配: 0.1GB, 已保留: 0.3GB
警告: 受试者 S09 训练失败

============================================================
训练受试者 5/30: S10
受试者组别: 大样本组(>15个测试样本)
============================================================
正在加载MMEW数据集，测试受试者: S10
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S01 训练数据: 391 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S02 训练数据: 392 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 82 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S03 训练数据: 395 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 69 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S04 训练数据: 393 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S05 训练数据: 387 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 55 个原始样本, 扩充倍数: 1
  加载 S06 训练数据: 382 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S07 训练数据: 402 个样本
    情绪类别 0: 31 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 70 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S08 训练数据: 385 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 65 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S09 训练数据: 384 个样本
    情绪类别 0: 2 个原始样本, 扩充倍数: 1
    情绪类别 1: 2 个原始样本, 扩充倍数: 1
    情绪类别 2: 6 个原始样本, 扩充倍数: 1
    情绪类别 3: 4 个原始样本, 扩充倍数: 1
  加载 S10 测试数据: 14 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S11 训练数据: 390 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S12 训练数据: 402 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 65 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S13 训练数据: 379 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S14 训练数据: 403 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 63 个原始样本, 扩充倍数: 1
  加载 S15 训练数据: 390 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 62 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S16 训练数据: 383 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S17 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S18 训练数据: 392 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S19 训练数据: 404 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 69 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S20 训练数据: 399 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S21 训练数据: 389 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 70 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S22 训练数据: 399 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S23 训练数据: 406 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S24 训练数据: 401 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S25 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S26 训练数据: 405 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S27 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S28 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S29 训练数据: 403 个样本
    情绪类别 0: 32 个原始样本, 扩充倍数: 3
    情绪类别 1: 75 个原始样本, 扩充倍数: 1
    情绪类别 2: 60 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S30 训练数据: 352 个样本
数据加载完成 - 训练: 11420, 测试: 14
训练数据最终形状: (11420, 48, 48, 36)
测试数据最终形状: (14, 48, 48, 36)
4分类标签映射完成
训练标签分布: {np.int64(0): np.int64(3030), np.int64(1): np.int64(2494), np.int64(2): np.int64(4044), np.int64(3): np.int64(1852)}
测试标签分布: {np.int64(0): np.int64(2), np.int64(1): np.int64(2), np.int64(2): np.int64(6), np.int64(3): np.int64(4)}
训练样本数: 11420, 测试样本数: 14
训练标签分布: {np.int64(0): np.int64(3030), np.int64(1): np.int64(2494), np.int64(2): np.int64(4044), np.int64(3): np.int64(1852)}
测试标签分布: {np.int64(0): np.int64(2), np.int64(1): np.int64(2), np.int64(2): np.int64(6), np.int64(3): np.int64(4)}
加载预训练模型: /home/<USER>/data/ajq/SKD-TSTSAN-new/Pretrained_model/SKD-TSTSAN.pth
跳过不存在的层: bn1_L.weight
跳过不存在的层: bn1_L.bias
跳过不存在的层: bn1_L.running_mean
跳过不存在的层: bn1_L.running_var
跳过不存在的层: bn1_L.num_batches_tracked
跳过不存在的层: bn1_S.weight
跳过不存在的层: bn1_S.bias
跳过不存在的层: bn1_S.running_mean
跳过不存在的层: bn1_S.running_var
跳过不存在的层: bn1_S.num_batches_tracked
跳过不存在的层: bn1_T.weight
跳过不存在的层: bn1_T.bias
跳过不存在的层: bn1_T.running_mean
跳过不存在的层: bn1_T.running_var
跳过不存在的层: bn1_T.num_batches_tracked
跳过不存在的层: AC1_fc.weight
跳过不存在的层: AC1_fc.bias
跳过不存在的层: AC2_fc.weight
跳过不存在的层: AC2_fc.bias
跳过形状不匹配的层: fc2.weight (预训练: torch.Size([7, 384]), 当前: torch.Size([4, 384]))
跳过形状不匹配的层: fc2.bias (预训练: torch.Size([7]), 当前: torch.Size([4]))
预训练模型部分加载成功，加载了 234/255 层
开始训练受试者 S10，总共1个epoch...
自适应参数 - Alpha: 0.1000, Temperature: 3.0, Beta: 1.00e-06

📊 资源使用情况:
  CPU使用率: 99.8%
  内存使用率: 37.0%
  可用内存: 39.8GB
  GPU内存: 已分配: 0.0GB, 已保留: 0.3GB

Epoch 1/1
Batch 0/1285, Total: 4.3167, Main: 1.3725, KD: 0.0237
Batch 10/1285, Total: 3.4967, Main: 1.3027, KD: 0.0702
Batch 20/1285, Total: 3.1564, Main: 0.9927, KD: 0.0362
Batch 30/1285, Total: 3.5785, Main: 1.0408, KD: 0.0488
Batch 40/1285, Total: 3.2513, Main: 1.0538, KD: 0.0506
Batch 50/1285, Total: 2.2391, Main: 0.7055, KD: 0.0576
Batch 60/1285, Total: 2.5136, Main: 0.9307, KD: 0.0555
Batch 70/1285, Total: 1.6730, Main: 0.6586, KD: 0.0360
Batch 80/1285, Total: 4.2059, Main: 1.3454, KD: 0.0488
Batch 90/1285, Total: 4.6698, Main: 1.3533, KD: 0.0456
Batch 100/1285, Total: 2.9256, Main: 1.0499, KD: 0.0338
Batch 110/1285, Total: 3.0163, Main: 0.8997, KD: 0.0343
Batch 120/1285, Total: 4.0502, Main: 1.3393, KD: 0.0490
Batch 130/1285, Total: 1.7935, Main: 0.5284, KD: 0.0397
Batch 140/1285, Total: 1.2735, Main: 0.5250, KD: 0.0329
Batch 150/1285, Total: 1.1386, Main: 0.4104, KD: 0.0301
Batch 160/1285, Total: 2.7897, Main: 0.9581, KD: 0.0414
Batch 170/1285, Total: 1.4799, Main: 0.5250, KD: 0.0505
Batch 180/1285, Total: 3.5648, Main: 1.1418, KD: 0.0370
Batch 190/1285, Total: 2.0569, Main: 0.6753, KD: 0.0295
Batch 200/1285, Total: 1.6574, Main: 0.5156, KD: 0.0253
Batch 210/1285, Total: 3.3825, Main: 1.0912, KD: 0.0278
Batch 220/1285, Total: 1.6817, Main: 0.5757, KD: 0.0449
Batch 230/1285, Total: 3.8956, Main: 1.4642, KD: 0.0325
Batch 240/1285, Total: 3.2877, Main: 1.0592, KD: 0.0311
Batch 250/1285, Total: 1.9366, Main: 0.5375, KD: 0.0434
Batch 260/1285, Total: 1.4969, Main: 0.5447, KD: 0.0354
Batch 270/1285, Total: 2.4926, Main: 0.7814, KD: 0.0177
Batch 280/1285, Total: 0.9529, Main: 0.3187, KD: 0.0262
Batch 290/1285, Total: 1.1662, Main: 0.3175, KD: 0.0450
Batch 300/1285, Total: 1.0695, Main: 0.3577, KD: 0.0487
Batch 310/1285, Total: 1.6923, Main: 0.5450, KD: 0.0216
Batch 320/1285, Total: 1.8416, Main: 0.6190, KD: 0.0375
Batch 330/1285, Total: 1.9605, Main: 0.6863, KD: 0.0434
Batch 340/1285, Total: 0.9886, Main: 0.2527, KD: 0.0384
Batch 350/1285, Total: 1.3143, Main: 0.4034, KD: 0.0428
Batch 360/1285, Total: 1.1346, Main: 0.4384, KD: 0.0300
Batch 370/1285, Total: 1.4182, Main: 0.5284, KD: 0.0204
Batch 380/1285, Total: 1.0646, Main: 0.3602, KD: 0.0268
Batch 390/1285, Total: 1.7311, Main: 0.5764, KD: 0.0411
Batch 400/1285, Total: 0.8501, Main: 0.2351, KD: 0.0276
Batch 410/1285, Total: 0.5050, Main: 0.2027, KD: 0.0277
Batch 420/1285, Total: 0.6237, Main: 0.2085, KD: 0.0290
Batch 430/1285, Total: 0.6439, Main: 0.2446, KD: 0.0290
Batch 440/1285, Total: 1.3530, Main: 0.5068, KD: 0.0314
Batch 450/1285, Total: 1.1472, Main: 0.3738, KD: 0.0427
Batch 460/1285, Total: 2.7190, Main: 0.7098, KD: 0.0472
Batch 470/1285, Total: 2.6614, Main: 0.8442, KD: 0.0304
Batch 480/1285, Total: 1.0396, Main: 0.3754, KD: 0.0340
Batch 490/1285, Total: 2.9200, Main: 0.8211, KD: 0.0361
Epoch 1 损失统计:
  总损失: 2.1852
  主损失: 0.7006
  知识蒸馏: 0.0418
  特征损失: 0.0030
受试者 S10 训练完成
最佳准确率: 0.0000, 最佳UF1: 0.0000
训练摘要:
  总轮次: 1
  总时间: 3.2分钟
  最终损失: 2.1852

📊 资源使用情况:
  CPU使用率: 86.3%
  内存使用率: 37.0%
  可用内存: 39.9GB
  GPU内存: 已分配: 0.1GB, 已保留: 0.3GB
警告: 受试者 S10 训练失败

============================================================
训练受试者 6/30: S21
受试者组别: 大样本组(>15个测试样本)
============================================================
正在加载MMEW数据集，测试受试者: S21
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S01 训练数据: 391 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S02 训练数据: 392 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 82 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S03 训练数据: 395 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 69 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S04 训练数据: 393 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S05 训练数据: 387 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 55 个原始样本, 扩充倍数: 1
  加载 S06 训练数据: 382 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S07 训练数据: 402 个样本
    情绪类别 0: 31 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 70 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S08 训练数据: 385 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 65 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S09 训练数据: 384 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 66 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S10 训练数据: 383 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S11 训练数据: 390 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S12 训练数据: 402 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 65 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S13 训练数据: 379 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S14 训练数据: 403 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 63 个原始样本, 扩充倍数: 1
  加载 S15 训练数据: 390 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 62 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S16 训练数据: 383 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S17 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S18 训练数据: 392 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S19 训练数据: 404 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 69 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S20 训练数据: 399 个样本
    情绪类别 0: 3 个原始样本, 扩充倍数: 1
    情绪类别 1: 5 个原始样本, 扩充倍数: 1
    情绪类别 3: 4 个原始样本, 扩充倍数: 1
  加载 S21 测试数据: 12 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 70 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S22 训练数据: 399 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S23 训练数据: 406 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S24 训练数据: 401 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S25 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S26 训练数据: 405 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S27 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S28 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S29 训练数据: 403 个样本
    情绪类别 0: 32 个原始样本, 扩充倍数: 3
    情绪类别 1: 75 个原始样本, 扩充倍数: 1
    情绪类别 2: 60 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S30 训练数据: 352 个样本
数据加载完成 - 训练: 11414, 测试: 12
训练数据最终形状: (11414, 48, 48, 36)
测试数据最终形状: (12, 48, 48, 36)
4分类标签映射完成
训练标签分布: {np.int64(0): np.int64(3033), np.int64(1): np.int64(2497), np.int64(2): np.int64(4032), np.int64(3): np.int64(1852)}
测试标签分布: {np.int64(0): np.int64(3), np.int64(1): np.int64(5), np.int64(3): np.int64(4)}
训练样本数: 11414, 测试样本数: 12
训练标签分布: {np.int64(0): np.int64(3033), np.int64(1): np.int64(2497), np.int64(2): np.int64(4032), np.int64(3): np.int64(1852)}
测试标签分布: {np.int64(0): np.int64(3), np.int64(1): np.int64(5), np.int64(3): np.int64(4)}
加载预训练模型: /home/<USER>/data/ajq/SKD-TSTSAN-new/Pretrained_model/SKD-TSTSAN.pth
跳过不存在的层: bn1_L.weight
跳过不存在的层: bn1_L.bias
跳过不存在的层: bn1_L.running_mean
跳过不存在的层: bn1_L.running_var
跳过不存在的层: bn1_L.num_batches_tracked
跳过不存在的层: bn1_S.weight
跳过不存在的层: bn1_S.bias
跳过不存在的层: bn1_S.running_mean
跳过不存在的层: bn1_S.running_var
跳过不存在的层: bn1_S.num_batches_tracked
跳过不存在的层: bn1_T.weight
跳过不存在的层: bn1_T.bias
跳过不存在的层: bn1_T.running_mean
跳过不存在的层: bn1_T.running_var
跳过不存在的层: bn1_T.num_batches_tracked
跳过不存在的层: AC1_fc.weight
跳过不存在的层: AC1_fc.bias
跳过不存在的层: AC2_fc.weight
跳过不存在的层: AC2_fc.bias
跳过形状不匹配的层: fc2.weight (预训练: torch.Size([7, 384]), 当前: torch.Size([4, 384]))
跳过形状不匹配的层: fc2.bias (预训练: torch.Size([7]), 当前: torch.Size([4]))
预训练模型部分加载成功，加载了 234/255 层
开始训练受试者 S21，总共1个epoch...
自适应参数 - Alpha: 0.1000, Temperature: 3.0, Beta: 1.00e-06

📊 资源使用情况:
  CPU使用率: 94.3%
  内存使用率: 37.1%
  可用内存: 39.8GB
  GPU内存: 已分配: 0.0GB, 已保留: 0.3GB

Epoch 1/1
Batch 0/1284, Total: 4.6301, Main: 1.4012, KD: 0.0326
Batch 10/1284, Total: 3.7521, Main: 1.2690, KD: 0.0954
Batch 20/1284, Total: 4.1406, Main: 1.3368, KD: 0.0547
Batch 30/1284, Total: 2.9063, Main: 0.9912, KD: 0.0572
Batch 40/1284, Total: 3.5735, Main: 0.9195, KD: 0.0548
Batch 50/1284, Total: 3.6209, Main: 1.1567, KD: 0.0471
Batch 60/1284, Total: 2.5277, Main: 0.7082, KD: 0.0341
Batch 70/1284, Total: 3.9793, Main: 1.1640, KD: 0.0689
Batch 80/1284, Total: 4.6617, Main: 1.3765, KD: 0.0493
Batch 90/1284, Total: 4.2970, Main: 1.2434, KD: 0.0305
Batch 100/1284, Total: 1.8663, Main: 0.6207, KD: 0.0367
Batch 110/1284, Total: 1.0252, Main: 0.5257, KD: 0.0735
Batch 120/1284, Total: 1.5525, Main: 0.5540, KD: 0.0233
Batch 130/1284, Total: 2.5764, Main: 0.7510, KD: 0.0359
Batch 140/1284, Total: 2.0406, Main: 0.7754, KD: 0.0308
Batch 150/1284, Total: 3.6690, Main: 1.1452, KD: 0.0334
Batch 160/1284, Total: 2.3303, Main: 0.8123, KD: 0.0358
Batch 170/1284, Total: 3.1571, Main: 0.9399, KD: 0.0365
Batch 180/1284, Total: 4.3753, Main: 1.2638, KD: 0.0395
Batch 190/1284, Total: 2.3887, Main: 0.7825, KD: 0.0375
Batch 200/1284, Total: 3.3027, Main: 0.9353, KD: 0.0273
Batch 210/1284, Total: 3.4188, Main: 1.0652, KD: 0.0319
Batch 220/1284, Total: 2.4576, Main: 0.8027, KD: 0.0317
Batch 230/1284, Total: 1.6472, Main: 0.5429, KD: 0.0296
Batch 240/1284, Total: 3.1628, Main: 1.0112, KD: 0.0331
Batch 250/1284, Total: 2.1236, Main: 0.7454, KD: 0.0262
Batch 260/1284, Total: 2.7426, Main: 0.8254, KD: 0.0396
Batch 270/1284, Total: 3.1986, Main: 0.9924, KD: 0.0430
Batch 280/1284, Total: 1.5220, Main: 0.4533, KD: 0.0296
Batch 290/1284, Total: 1.5423, Main: 0.5606, KD: 0.0357
Batch 300/1284, Total: 1.7070, Main: 0.5576, KD: 0.0229
Batch 310/1284, Total: 1.7717, Main: 0.5749, KD: 0.0282
Batch 320/1284, Total: 1.8444, Main: 0.5289, KD: 0.0324
Batch 330/1284, Total: 1.3297, Main: 0.4395, KD: 0.0304
Batch 340/1284, Total: 0.8800, Main: 0.3688, KD: 0.0532
Batch 350/1284, Total: 2.0377, Main: 0.7648, KD: 0.0375
Batch 360/1284, Total: 2.3135, Main: 0.7863, KD: 0.0262
Batch 370/1284, Total: 1.1774, Main: 0.5045, KD: 0.0470
Batch 380/1284, Total: 0.6455, Main: 0.2088, KD: 0.0280
Batch 390/1284, Total: 1.3729, Main: 0.4931, KD: 0.0448
Batch 400/1284, Total: 1.0013, Main: 0.4162, KD: 0.0199
Batch 410/1284, Total: 1.3715, Main: 0.5531, KD: 0.0454
Batch 420/1284, Total: 0.7258, Main: 0.3004, KD: 0.0661
Batch 430/1284, Total: 0.5269, Main: 0.2109, KD: 0.0280
Batch 440/1284, Total: 1.7855, Main: 0.6186, KD: 0.0354
Batch 450/1284, Total: 4.0415, Main: 1.2507, KD: 0.0291
Batch 460/1284, Total: 2.6905, Main: 0.8680, KD: 0.0325
Batch 470/1284, Total: 1.5450, Main: 0.5200, KD: 0.0346
Batch 480/1284, Total: 1.1175, Main: 0.3968, KD: 0.0364
Batch 490/1284, Total: 3.4969, Main: 1.1534, KD: 0.0313
Epoch 1 损失统计:
  总损失: 2.1304
  主损失: 0.7019
  知识蒸馏: 0.0409
  特征损失: 0.0027
受试者 S21 训练完成
最佳准确率: 0.0000, 最佳UF1: 0.0000
训练摘要:
  总轮次: 1
  总时间: 3.2分钟
  最终损失: 2.1304

📊 资源使用情况:
  CPU使用率: 96.9%
  内存使用率: 42.5%
  可用内存: 36.4GB
  GPU内存: 已分配: 0.1GB, 已保留: 0.3GB
警告: 受试者 S21 训练失败

============================================================
训练受试者 7/30: S16
受试者组别: 大样本组(>15个测试样本)
============================================================
正在加载MMEW数据集，测试受试者: S16
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S01 训练数据: 391 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S02 训练数据: 392 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 82 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S03 训练数据: 395 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 69 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S04 训练数据: 393 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S05 训练数据: 387 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 55 个原始样本, 扩充倍数: 1
  加载 S06 训练数据: 382 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S07 训练数据: 402 个样本
    情绪类别 0: 31 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 70 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S08 训练数据: 385 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 65 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S09 训练数据: 384 个样本
    情绪类别 0: 34 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 66 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S10 训练数据: 383 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S11 训练数据: 390 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S12 训练数据: 402 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 65 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S13 训练数据: 379 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S14 训练数据: 403 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 63 个原始样本, 扩充倍数: 1
  加载 S15 训练数据: 390 个样本
    情绪类别 1: 2 个原始样本, 扩充倍数: 1
    情绪类别 2: 10 个原始样本, 扩充倍数: 1
    情绪类别 3: 2 个原始样本, 扩充倍数: 1
  加载 S16 测试数据: 14 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S17 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 68 个原始样本, 扩充倍数: 2
    情绪类别 3: 64 个原始样本, 扩充倍数: 1
  加载 S18 训练数据: 392 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S19 训练数据: 404 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 87 个原始样本, 扩充倍数: 1
    情绪类别 2: 69 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S20 训练数据: 399 个样本
    情绪类别 0: 33 个原始样本, 扩充倍数: 3
    情绪类别 1: 84 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 62 个原始样本, 扩充倍数: 1
  加载 S21 训练数据: 389 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 70 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S22 训练数据: 399 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 89 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S23 训练数据: 406 个样本
    情绪类别 0: 35 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S24 训练数据: 401 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 86 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S25 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S26 训练数据: 405 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S27 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 88 个原始样本, 扩充倍数: 1
    情绪类别 2: 71 个原始样本, 扩充倍数: 2
    情绪类别 3: 65 个原始样本, 扩充倍数: 1
  加载 S28 训练数据: 403 个样本
    情绪类别 0: 36 个原始样本, 扩充倍数: 3
    情绪类别 1: 85 个原始样本, 扩充倍数: 1
    情绪类别 2: 72 个原始样本, 扩充倍数: 2
    情绪类别 3: 66 个原始样本, 扩充倍数: 1
  加载 S29 训练数据: 403 个样本
    情绪类别 0: 32 个原始样本, 扩充倍数: 3
    情绪类别 1: 75 个原始样本, 扩充倍数: 1
    情绪类别 2: 60 个原始样本, 扩充倍数: 2
    情绪类别 3: 61 个原始样本, 扩充倍数: 1
  加载 S30 训练数据: 352 个样本
数据加载完成 - 训练: 11420, 测试: 14
训练数据最终形状: (11420, 48, 48, 36)
测试数据最终形状: (14, 48, 48, 36)
4分类标签映射完成
训练标签分布: {np.int64(0): np.int64(3024), np.int64(1): np.int64(2494), np.int64(2): np.int64(4052), np.int64(3): np.int64(1850)}
测试标签分布: {np.int64(1): np.int64(2), np.int64(2): np.int64(10), np.int64(3): np.int64(2)}
训练样本数: 11420, 测试样本数: 14
训练标签分布: {np.int64(0): np.int64(3024), np.int64(1): np.int64(2494), np.int64(2): np.int64(4052), np.int64(3): np.int64(1850)}
测试标签分布: {np.int64(1): np.int64(2), np.int64(2): np.int64(10), np.int64(3): np.int64(2)}
加载预训练模型: /home/<USER>/data/ajq/SKD-TSTSAN-new/Pretrained_model/SKD-TSTSAN.pth
跳过不存在的层: bn1_L.weight
跳过不存在的层: bn1_L.bias