日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (3分类)[0m
[1;96m🎯 类别权重: ['0.3794', '0.1349', '0.4857'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub17测试标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=haar, 层数=2
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: haar (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用跨分支交互[0m
  [96m- 保持原始三分支独立处理[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡
  [96m- 传统架构，稳定可靠[0m 🔧

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 953 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.1613[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.1613[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 2/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9091[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.1613[0m | [1m最佳准确率:[0m [1;93m0.1613[0m
[1;92m💾 保存最佳模型，准确率: 0.1613[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 3/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9818[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.1935[0m | [1m最佳准确率:[0m [1;93m0.1613[0m
[1;92m💾 保存最佳模型，准确率: 0.1935[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 4/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.4839[0m | [1m最佳准确率:[0m [1;93m0.1935[0m
[1;92m💾 保存最佳模型，准确率: 0.4839[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 5/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.5806[0m | [1m最佳准确率:[0m [1;93m0.4839[0m
[1;92m💾 保存最佳模型，准确率: 0.5806[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 6/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.4839[0m | [1m最佳准确率:[0m [1;93m0.5806[0m

[1m[1;96m📅 Epoch 7/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9924[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.6452[0m | [1m最佳准确率:[0m [1;93m0.5806[0m
[1;92m💾 保存最佳模型，准确率: 0.6452[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 8/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6129[0m | [1m最佳准确率:[0m [1;93m0.6452[0m

[1m[1;96m📅 Epoch 9/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.7742[0m | [1m最佳准确率:[0m [1;93m0.6452[0m
[1;92m💾 保存最佳模型，准确率: 0.7742[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 10/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9939[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.7742[0m
[1;92m💾 保存最佳模型，准确率: 0.7742[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 11/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.8710[0m | [1m最佳准确率:[0m [1;93m0.7742[0m
[1;92m💾 保存最佳模型，准确率: 0.8710[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 12/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.8710[0m

[1m[1;96m📅 Epoch 13/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.8710[0m

[1m[1;96m📅 Epoch 14/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.8710[0m

[1m[1;96m📅 Epoch 15/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.8710[0m

[1m[1;96m📅 Epoch 16/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.8710[0m
[1;92m💾 保存最佳模型，准确率: 0.8710[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 17/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.8710[0m

[1m[1;96m📅 Epoch 18/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.8710[0m

[1m[1;96m📅 Epoch 19/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.8710[0m
[1;92m💾 保存最佳模型，准确率: 0.8710[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 20/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.8710[0m
[1;92m💾 保存最佳模型，准确率: 0.8710[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 21/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.8710[0m

[1m[1;96m📅 Epoch 22/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9894[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.8710[0m

[1m[1;96m📅 Epoch 23/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.8710[0m

[1m[1;96m📅 Epoch 24/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.8710[0m

[1m[1;96m📅 Epoch 25/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9894[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.8710[0m

[1m[1;96m📅 Epoch 26/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9773[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.8710[0m

[1m[1;96m📅 Epoch 27/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9894[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.8710[0m

[1m[1;96m📅 Epoch 28/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9909[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6452[0m | [1m最佳准确率:[0m [1;93m0.8710[0m

[1m[1;96m📅 Epoch 29/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.8710[0m

[1m[1;96m📅 Epoch 30/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.8710[0m
[1;92m💾 保存最佳模型，准确率: 0.8710[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 31/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.9032[0m | [1m最佳准确率:[0m [1;93m0.8710[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 32/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 33/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 34/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 35/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 36/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 37/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 38/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9032[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 39/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9032[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 40/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 41/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 42/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 43/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 44/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 45/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9032[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 46/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 47/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9879[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 48/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9652[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 49/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9894[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 50/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 51/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 52/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 53/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 54/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 55/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9032[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 56/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 57/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 58/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 59/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 60/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 61/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 62/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 63/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 64/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 65/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 66/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 67/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 68/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 69/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 70/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 71/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9939[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 72/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6774[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 73/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 74/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9032[0m
[1;92m💾 保存最佳模型，准确率: 0.9355[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 75/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 76/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9355[0m
[1;92m💾 保存最佳模型，准确率: 0.9355[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 77/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 78/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 79/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 80/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 81/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 82/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 83/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 84/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 85/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 86/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 87/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9939[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 88/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9894[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 89/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9355[0m
[1;92m💾 保存最佳模型，准确率: 0.9355[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 90/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 91/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 92/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 93/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 94/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 95/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 96/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 97/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 98/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 99/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 100/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9355[0m
[1;92m💾 保存最佳模型，准确率: 0.9355[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 101/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9939[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 102/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9833[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 103/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 104/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 105/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 106/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 107/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 108/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 109/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 110/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 111/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 112/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 113/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 114/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 115/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 116/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 117/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 118/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 119/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 120/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 121/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 122/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9939[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 123/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 124/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 125/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 126/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 127/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 128/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9355[0m
[1;92m💾 保存最佳模型，准确率: 0.9355[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 129/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9355[0m
[1;92m💾 保存最佳模型，准确率: 0.9355[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 130/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 131/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 132/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 133/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 134/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 135/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9879[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 136/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9924[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5484[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 137/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 138/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 139/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 140/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 141/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 142/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 143/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 144/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 145/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 146/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 147/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 148/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 149/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 150/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 151/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 152/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 153/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 154/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 155/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 156/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 157/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 158/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 159/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 160/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 161/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9924[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6774[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 162/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9924[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 163/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 164/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 165/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 166/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 167/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 168/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 169/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9909[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 170/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 171/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 172/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 173/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9909[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 174/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 175/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 176/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 177/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 178/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 179/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 180/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 181/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 182/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 183/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 184/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 185/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9355[0m
[1;92m💾 保存最佳模型，准确率: 0.9355[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 186/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 187/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6452[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 188/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 189/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 190/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 191/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 192/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 193/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 194/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 195/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 196/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 197/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9355[0m
[1;92m💾 保存最佳模型，准确率: 0.9355[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 198/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 199/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 200/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 201/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 202/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9939[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 203/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 204/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9909[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 205/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9924[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 206/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 207/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 208/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 209/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 210/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 211/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 212/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 213/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 214/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 215/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 216/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9355[0m
[1;92m💾 保存最佳模型，准确率: 0.9355[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 217/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 218/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 219/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 220/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 221/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 222/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 223/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 224/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 225/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 226/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 227/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 228/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 229/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 230/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 231/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 232/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 233/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 234/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 235/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 236/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 237/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 238/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 239/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 240/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 241/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 242/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 243/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 244/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 245/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.3226[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 246/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 247/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 248/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 249/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9355[0m
[1;92m💾 保存最佳模型，准确率: 0.9355[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 250/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 251/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 252/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 253/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 254/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 255/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 256/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 257/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 258/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 259/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 260/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 261/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 262/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 263/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 264/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 265/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 266/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 267/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9939[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 268/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9894[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 269/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9924[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 270/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 271/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 272/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 273/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 274/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 275/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 276/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 277/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 278/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 279/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 280/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 281/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 282/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 283/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 284/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 285/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9355[0m

[1m[1;96m📅 Epoch 286/953[0m
[1;96m-----------------[0m
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (3分类)[0m
[1;96m🎯 类别权重: ['0.3794', '0.1349', '0.4857'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub17测试标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
TensorBoard日志目录已存在: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=haar, 层数=2
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: haar (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用跨分支交互[0m
  [96m- 保持原始三分支独立处理[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡
  [96m- 传统架构，稳定可靠[0m 🔧

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 953 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.7030[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.1613[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.1613[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 2/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9076[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.1935[0m | [1m最佳准确率:[0m [1;93m0.1613[0m
[1;92m💾 保存最佳模型，准确率: 0.1935[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 3/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9803[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.5806[0m | [1m最佳准确率:[0m [1;93m0.1935[0m
[1;92m💾 保存最佳模型，准确率: 0.5806[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 4/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9909[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5806[0m | [1m最佳准确率:[0m [1;93m0.5806[0m
[1;92m💾 保存最佳模型，准确率: 0.5806[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 5/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.6452[0m | [1m最佳准确率:[0m [1;93m0.5806[0m
[1;92m💾 保存最佳模型，准确率: 0.6452[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 6/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5161[0m | [1m最佳准确率:[0m [1;93m0.6452[0m

[1m[1;96m📅 Epoch 7/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9924[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5806[0m | [1m最佳准确率:[0m [1;93m0.6452[0m

[1m[1;96m📅 Epoch 8/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5806[0m | [1m最佳准确率:[0m [1;93m0.6452[0m

[1m[1;96m📅 Epoch 9/953[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.7742[0m | [1m最佳准确率:[0m [1;93m0.6452[0m
[1;92m💾 保存最佳模型，准确率: 0.7742[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 10/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.7742[0m

[1m[1;96m📅 Epoch 11/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.7742[0m

[1m[1;96m📅 Epoch 12/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.8387[0m | [1m最佳准确率:[0m [1;93m0.7742[0m
[1;92m💾 保存最佳模型，准确率: 0.8387[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 13/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 14/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.8387[0m
[1;92m💾 保存最佳模型，准确率: 0.8387[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 15/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 16/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.8387[0m
[1;92m💾 保存最佳模型，准确率: 0.8387[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 17/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6774[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 18/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9879[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5161[0m | [1m最佳准确率:[0m [1;93m0.8387[0m

[1m[1;96m📅 Epoch 19/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9742[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.8387[0m
[1;92m💾 保存最佳模型，准确率: 0.8387[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 20/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9939[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.8710[0m | [1m最佳准确率:[0m [1;93m0.8387[0m
[1;92m💾 保存最佳模型，准确率: 0.8710[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 21/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.9032[0m | [1m最佳准确率:[0m [1;93m0.8710[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 22/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9032[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 23/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9032[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 24/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9032[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 25/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 26/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 27/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 28/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9032[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 29/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9032[0m
[1;92m💾 保存最佳模型，准确率: 0.9032[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 30/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 31/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 32/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 33/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 34/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 35/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9879[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 36/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 37/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 38/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9924[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 39/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 40/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6129[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 41/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 42/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9909[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 43/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 44/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9032[0m

[1m[1;96m📅 Epoch 45/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.9677[0m | [1m最佳准确率:[0m [1;93m0.9032[0m
[1;92m💾 保存最佳模型，准确率: 0.9677[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 46/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 47/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 48/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 49/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 50/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 51/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 52/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 53/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 54/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 55/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9803[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 56/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 57/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 58/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 59/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 60/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 61/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 62/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 63/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 64/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 65/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 66/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 67/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 68/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 69/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 70/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 71/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 72/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9939[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 73/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9955[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 74/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 75/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 76/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 77/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 78/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 79/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 80/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 81/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 82/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 83/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 84/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 85/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 86/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 87/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 88/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 89/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 90/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 91/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 92/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 93/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9848[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7097[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 94/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6774[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 95/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 96/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7742[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 97/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 98/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9939[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 99/953[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8387[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 100/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 101/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 102/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 103/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 104/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 105/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 106/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 107/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 108/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 109/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 110/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9355[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 111/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 112/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 113/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7419[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 114/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9970[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8065[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 115/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9879[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 116/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8710[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 117/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 118/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 119/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 120/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 0.9985[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9032[0m | [1m最佳准确率:[0m [1;93m0.9677[0m

[1m[1;96m📅 Epoch 121/953[0m
[1;96m-----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.9677[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub17/sub17.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
真实标签: [2, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 7
TP: 7, FP: 0, FN: 0, TN: 24
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 23
TP: 23, FP: 0, FN: 0, TN: 8
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 30
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 7
TP: 7, FP: 0, FN: 0, TN: 24
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 23
TP: 23, FP: 0, FN: 0, TN: 8
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 30
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 200 个样本
标签 1: 增强了 130 个样本
标签 2: 增强了 216 个样本

========================================
【当前处理受试者】: sub05
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub05测试标签: [2, 2, 2, 2, 2, 0, 1]

【测试数据镜像训练】为 sub05 添加测试数据的镜像版本到训练集
  ✓ 成功添加 62 个镜像训练样本
  ✓ 训练集大小: 740 → 802
  ✓ 光流数据已正确处理 (u分量取反，v分量保持)
创建受试者目录: sub05
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=haar, 层数=2
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: haar (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用跨分支交互[0m
  [96m- 保持原始三分支独立处理[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡
  [96m- 传统架构，稳定可靠[0m 🔧

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6584[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.7143[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.7143[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/sub05.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/sub05.pth

[1m[1;96m📅 Epoch 2/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9027[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.8571[0m | [1m最佳准确率:[0m [1;93m0.7143[0m
[1;92m💾 保存最佳模型，准确率: 0.8571[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/sub05.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/sub05.pth

[1m[1;96m📅 Epoch 3/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9601[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8571[0m | [1m最佳准确率:[0m [1;93m0.8571[0m
[1;92m💾 保存最佳模型，准确率: 0.8571[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/sub05.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/sub05.pth

[1m[1;96m📅 Epoch 4/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9850[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8571[0m | [1m最佳准确率:[0m [1;93m0.8571[0m
[1;92m💾 保存最佳模型，准确率: 0.8571[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/sub05.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/sub05.pth

[1m[1;96m📅 Epoch 5/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9888[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8571[0m | [1m最佳准确率:[0m [1;93m0.8571[0m
[1;92m💾 保存最佳模型，准确率: 0.8571[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/sub05.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/sub05.pth

[1m[1;96m📅 Epoch 6/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9900[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8571[0m | [1m最佳准确率:[0m [1;93m0.8571[0m
[1;92m💾 保存最佳模型，准确率: 0.8571[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/sub05.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/sub05.pth

[1m[1;96m📅 Epoch 7/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9751[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8571[0m | [1m最佳准确率:[0m [1;93m0.8571[0m
[1;92m💾 保存最佳模型，准确率: 0.8571[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/sub05.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/sub05.pth

[1m[1;96m📅 Epoch 8/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9950[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8571[0m | [1m最佳准确率:[0m [1;93m0.8571[0m
[1;92m💾 保存最佳模型，准确率: 0.8571[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/sub05.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/sub05.pth

[1m[1;96m📅 Epoch 9/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8571[0m | [1m最佳准确率:[0m [1;93m0.8571[0m
[1;92m💾 保存最佳模型，准确率: 0.8571[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/sub05.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/sub05.pth

[1m[1;96m📅 Epoch 10/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9988[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.8571[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/sub05.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub05/sub05.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [2, 2, 2, 2, 2, 0, 1]
真实标签: [2, 2, 2, 2, 2, 0, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 6
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 6
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 6
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 6
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 448 个样本
标签 1: 增强了 304 个样本
标签 2: 增强了 396 个样本

========================================
【当前处理受试者】: sub26
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub26测试标签: [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1]
创建受试者目录: sub26
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub26/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub26/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=haar, 层数=2
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: haar (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用跨分支交互[0m
  [96m- 保持原始三分支独立处理[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡
  [96m- 传统架构，稳定可靠[0m 🔧

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 834 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6684[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub26/sub26.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub26/sub26.pth

[1m[1;96m📅 Epoch 2/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.8705[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.9091[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.9091[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub26/sub26.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub26/sub26.pth

[1m[1;96m📅 Epoch 3/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9524[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8182[0m | [1m最佳准确率:[0m [1;93m0.9091[0m

[1m[1;96m📅 Epoch 4/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9841[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9091[0m | [1m最佳准确率:[0m [1;93m0.9091[0m
[1;92m💾 保存最佳模型，准确率: 0.9091[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub26/sub26.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub26/sub26.pth

[1m[1;96m📅 Epoch 5/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9934[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6364[0m | [1m最佳准确率:[0m [1;93m0.9091[0m

[1m[1;96m📅 Epoch 6/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9908[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7273[0m | [1m最佳准确率:[0m [1;93m0.9091[0m

[1m[1;96m📅 Epoch 7/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7273[0m | [1m最佳准确率:[0m [1;93m0.9091[0m

[1m[1;96m📅 Epoch 8/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7273[0m | [1m最佳准确率:[0m [1;93m0.9091[0m

[1m[1;96m📅 Epoch 9/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.9091[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub26/sub26.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub26/sub26.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1]
真实标签: [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 9
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 9
TP: 9, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 2
TP: 2, FP: 0, FN: 0, TN: 9
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 9
TP: 9, FP: 0, FN: 0, TN: 2
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 688 个样本
标签 1: 增强了 462 个样本
标签 2: 增强了 621 个样本

========================================
【当前处理受试者】: sub19
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub19测试标签: [2, 2, 2, 2, 2, 0, 0, 0, 1, 1, 1]
创建受试者目录: sub19
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub19/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub19/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=haar, 层数=2
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: haar (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用跨分支交互[0m
  [96m- 保持原始三分支独立处理[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡
  [96m- 传统架构，稳定可靠[0m 🔧

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 870 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6634[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.4545[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.4545[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub19/sub19.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub19/sub19.pth

[1m[1;96m📅 Epoch 2/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9008[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.8182[0m | [1m最佳准确率:[0m [1;93m0.4545[0m
[1;92m💾 保存最佳模型，准确率: 0.8182[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub19/sub19.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub19/sub19.pth

[1m[1;96m📅 Epoch 3/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9735[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8182[0m | [1m最佳准确率:[0m [1;93m0.8182[0m
[1;92m💾 保存最佳模型，准确率: 0.8182[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub19/sub19.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub19/sub19.pth

[1m[1;96m📅 Epoch 4/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9944[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7273[0m | [1m最佳准确率:[0m [1;93m0.8182[0m

[1m[1;96m📅 Epoch 5/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9944[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.2727[0m | [1m最佳准确率:[0m [1;93m0.8182[0m

[1m[1;96m📅 Epoch 6/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9944[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.3636[0m | [1m最佳准确率:[0m [1;93m0.8182[0m

[1m[1;96m📅 Epoch 7/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.9091[0m | [1m最佳准确率:[0m [1;93m0.8182[0m
[1;92m💾 保存最佳模型，准确率: 0.9091[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub19/sub19.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub19/sub19.pth

[1m[1;96m📅 Epoch 8/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9091[0m | [1m最佳准确率:[0m [1;93m0.9091[0m
[1;92m💾 保存最佳模型，准确率: 0.9091[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub19/sub19.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub19/sub19.pth

[1m[1;96m📅 Epoch 9/870[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8182[0m | [1m最佳准确率:[0m [1;93m0.9091[0m

[1m[1;96m📅 Epoch 10/870[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.9091[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub19/sub19.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub19/sub19.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [2, 2, 2, 2, 2, 0, 0, 0, 1, 1, 1]
真实标签: [2, 2, 2, 2, 2, 0, 0, 0, 1, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 3
TP: 3, FP: 0, FN: 0, TN: 8
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 3
TP: 3, FP: 0, FN: 0, TN: 8
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 6
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 3
TP: 3, FP: 0, FN: 0, TN: 8
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 3
TP: 3, FP: 0, FN: 0, TN: 8
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 6
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 920 个样本
标签 1: 增强了 632 个样本
标签 2: 增强了 801 个样本

========================================
【当前处理受试者】: sub09
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub09测试标签: [0, 0, 0, 0, 0, 1, 1, 1, 1, 1]
创建受试者目录: sub09
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=haar, 层数=2
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: haar (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用跨分支交互[0m
  [96m- 保持原始三分支独立处理[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡
  [96m- 传统架构，稳定可靠[0m 🔧

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 834 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6712[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.5000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.5000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth

[1m[1;96m📅 Epoch 2/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9164[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.6000[0m | [1m最佳准确率:[0m [1;93m0.5000[0m
[1;92m💾 保存最佳模型，准确率: 0.6000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth

[1m[1;96m📅 Epoch 3/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9286[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.7000[0m | [1m最佳准确率:[0m [1;93m0.6000[0m
[1;92m💾 保存最佳模型，准确率: 0.7000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth

[1m[1;96m📅 Epoch 4/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9690[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7000[0m

[1m[1;96m📅 Epoch 5/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9811[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.8000[0m | [1m最佳准确率:[0m [1;93m0.7000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth

[1m[1;96m📅 Epoch 6/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9919[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m

[1m[1;96m📅 Epoch 7/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m

[1m[1;96m📅 Epoch 8/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9960[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth

[1m[1;96m📅 Epoch 9/834[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9960[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth

[1m[1;96m📅 Epoch 10/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth

[1m[1;96m📅 Epoch 11/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth

[1m[1;96m📅 Epoch 12/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9946[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth

[1m[1;96m📅 Epoch 13/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9946[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.9000[0m | [1m最佳准确率:[0m [1;93m0.8000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth

[1m[1;96m📅 Epoch 14/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 15/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9852[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth

[1m[1;96m📅 Epoch 16/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9892[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 17/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9906[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 18/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9906[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 19/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9906[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 20/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9933[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 21/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9946[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 22/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 23/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9919[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth

[1m[1;96m📅 Epoch 24/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9690[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 25/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 26/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth

[1m[1;96m📅 Epoch 27/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9825[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 28/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9946[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 29/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 30/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 31/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 32/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 33/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 34/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 35/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9879[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 36/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9919[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 37/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth

[1m[1;96m📅 Epoch 38/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth

[1m[1;96m📅 Epoch 39/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9879[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 40/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9906[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 41/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 42/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9973[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth

[1m[1;96m📅 Epoch 43/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth

[1m[1;96m📅 Epoch 44/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9987[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 45/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.9000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth

[1m[1;96m📅 Epoch 46/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9933[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 47/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9946[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.8000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m

[1m[1;96m📅 Epoch 48/834[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9946[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.9000[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub09/sub09.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [0, 0, 0, 0, 0, 1, 1, 1, 1, 1]
真实标签: [0, 0, 0, 0, 0, 1, 1, 1, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 5
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 5
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 5
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 5
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
惊讶(surprise): 没有样本

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 1136 个样本
标签 1: 增强了 798 个样本
标签 2: 增强了 1026 个样本

========================================
【当前处理受试者】: sub02
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub02测试标签: [2, 2, 2, 0, 1, 1, 1, 1, 1]

【测试数据镜像训练】为 sub02 添加测试数据的镜像版本到训练集
  ✓ 成功添加 54 个镜像训练样本
  ✓ 训练集大小: 748 → 802
  ✓ 光流数据已正确处理 (u分量取反，v分量保持)
创建受试者目录: sub02
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub02/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub02/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=haar, 层数=2
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: haar (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用跨分支交互[0m
  [96m- 保持原始三分支独立处理[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡
  [96m- 传统架构，稳定可靠[0m 🔧

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6559[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.3333[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;92m💾 保存最佳模型，准确率: 0.3333[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub02/sub02.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub02/sub02.pth

[1m[1;96m📅 Epoch 2/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9077[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.4444[0m | [1m最佳准确率:[0m [1;93m0.3333[0m
[1;92m💾 保存最佳模型，准确率: 0.4444[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub02/sub02.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub02/sub02.pth

[1m[1;96m📅 Epoch 3/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9551[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.4444[0m
[1;92m💾 保存最佳模型，准确率: 1.0000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub02/sub02.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub02/sub02.pth
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [2, 2, 2, 0, 1, 1, 1, 1, 1]
真实标签: [2, 2, 2, 0, 1, 1, 1, 1, 1]
当前评估结果:

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 8
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 4
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 3
TP: 3, FP: 0, FN: 0, TN: 6
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000

正性(positive)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 8
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

负性(negative)评估结果:
样本数: 5
TP: 5, FP: 0, FN: 0, TN: 4
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

惊讶(surprise)评估结果:
样本数: 3
TP: 3, FP: 0, FN: 0, TN: 6
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 1384 个样本
标签 1: 增强了 964 个样本
标签 2: 增强了 1224 个样本

========================================
【当前处理受试者】: sub23
========================================
组别: 中等样本组(7-12个样本)
正在加载训练数据...
跳过"其他"类别数据: 4
正在加载测试数据...
跳过"其他"类别测试数据: 4

sub23测试标签: [0, 1, 1, 1, 1, 1, 1, 1]
创建受试者目录: sub23
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub23/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class3_full_conv3_haar_L2_eca1/sub23/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=haar, 层数=2
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: haar (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用跨分支交互[0m
  [96m- 保持原始三分支独立处理[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡
  [96m- 传统架构，稳定可靠[0m 🔧

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 801 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/801[0m
[1;96m---------------[0m
