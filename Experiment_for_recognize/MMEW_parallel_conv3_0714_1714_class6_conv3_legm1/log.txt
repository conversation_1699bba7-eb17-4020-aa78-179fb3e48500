
================================================================================
【训练开始】
================================================================================

【硬件信息】使用单个GPU进行训练
✓ TF32加速已启用
✓ 混合精度训练已启用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

【损失函数配置】
使用加权Focal Loss (6分类)
类别权重: ['0.1339', '0.0542', '0.0669', '0.3012', '0.3708', '0.0730']
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: S30
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

S30测试标签: [4, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 5, 5, 5, 5, 5, 3]
创建受试者目录: S30
创建日志目录: ./Experiment_for_recognize/MMEW_parallel_conv3_0714_1714_class6_conv3_legm1/S30/logs
正在初始化 小波变换卷积 模型...
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
✅ 启用小波变换卷积功能:
  - 在初始卷积层(conv1_L, conv1_S, conv1_T)中使用WTConv
  - 在中间卷积层(conv2_L, conv2_S)中使用WTConv
  - 通过小波变换实现多频响应特征提取
  - 同时捕获微表情的全局结构和局部细节
  - 实现非常大的感受野而不会过度参数化
  - 特别适合微表情的频域特征分析
🚫 未启用LEGM模块
  - 使用标准ECA注意力模块
  - 保持原始模型结构
  - 最快推理速度，最低内存占用

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复
加载预训练模型...
开始训练,总共910个epoch...

Epoch 1/910
